/**
 * Component Migration Testing Framework
 * ===================================
 * Comprehensive testing infrastructure for component migration validation
 * Validates functionality, visual consistency, and performance improvements
 */

import { describe, test, expect, beforeAll, afterAll } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { axe, toHaveNoViolations } from 'jest-axe'
import React from 'react'

// Extend expect with accessibility matchers
expect.extend(toHaveNoViolations)

// ============================================================================
// Testing Framework Types
// ============================================================================

export interface ComponentTestConfig {
  componentName: string
  testId: string
  props: Record<string, any>
  expectedBehaviors: string[]
  performanceThresholds: PerformanceThresholds
  visualConsistencyChecks: VisualCheck[]
}

export interface PerformanceThresholds {
  renderTime: number // milliseconds
  memoryUsage: number // MB
  bundleSize: number // KB
}

export interface VisualCheck {
  selector: string
  property: string
  expectedValue: string | RegExp
}

export interface MigrationTestReport {
  appName: string
  componentName: string
  testResults: TestResult[]
  performanceMetrics: PerformanceMetrics
  migrationStatus: 'PASSED' | 'FAILED' | 'WARNING'
  issues: Issue[]
}

export interface TestResult {
  testName: string
  status: 'PASSED' | 'FAILED' | 'SKIPPED'
  duration: number
  error?: string
}

export interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  bundleSize: number
  accessibility: number // axe score
}

export interface Issue {
  type: 'REGRESSION' | 'PERFORMANCE' | 'ACCESSIBILITY' | 'VISUAL'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  description: string
  component: string
  recommendation: string
}

// ============================================================================
// Core Testing Framework Class
// ============================================================================

export class ComponentMigrationTester {
  private testReports: MigrationTestReport[] = []
  private performanceBaselines: Map<string, PerformanceMetrics> = new Map()

  constructor(private config: {
    enablePerformanceTracking: boolean
    enableVisualRegression: boolean
    enableAccessibilityTesting: boolean
    reportOutput: string
  }) {}

  /**
   * Test a single component migration
   */
  async testComponentMigration(
    OriginalComponent: React.ComponentType<any>,
    MigratedComponent: React.ComponentType<any>,
    testConfig: ComponentTestConfig
  ): Promise<MigrationTestReport> {
    const report: MigrationTestReport = {
      appName: 'Unknown',
      componentName: testConfig.componentName,
      testResults: [],
      performanceMetrics: { renderTime: 0, memoryUsage: 0, bundleSize: 0, accessibility: 0 },
      migrationStatus: 'PASSED',
      issues: []
    }

    console.log(`🧪 Testing migration for ${testConfig.componentName}...`)

    try {
      // 1. Functional Tests
      await this.runFunctionalTests(OriginalComponent, MigratedComponent, testConfig, report)

      // 2. Performance Tests
      if (this.config.enablePerformanceTracking) {
        await this.runPerformanceTests(OriginalComponent, MigratedComponent, testConfig, report)
      }

      // 3. Accessibility Tests
      if (this.config.enableAccessibilityTesting) {
        await this.runAccessibilityTests(MigratedComponent, testConfig, report)
      }

      // 4. Visual Consistency Tests
      if (this.config.enableVisualRegression) {
        await this.runVisualConsistencyTests(OriginalComponent, MigratedComponent, testConfig, report)
      }

      // 5. Determine overall status
      report.migrationStatus = this.determineOverallStatus(report)

    } catch (error) {
      report.migrationStatus = 'FAILED'
      report.issues.push({
        type: 'REGRESSION',
        severity: 'CRITICAL',
        description: `Test execution failed: ${error}`,
        component: testConfig.componentName,
        recommendation: 'Review test configuration and component implementation'
      })
    }

    this.testReports.push(report)
    return report
  }

  /**
   * Run functional tests comparing original vs migrated component
   */
  private async runFunctionalTests(
    OriginalComponent: React.ComponentType<any>,
    MigratedComponent: React.ComponentType<any>,
    testConfig: ComponentTestConfig,
    report: MigrationTestReport
  ) {
    const user = userEvent.setup()

    // Test prop compatibility
    const propCompatibilityResult = await this.measureTestTime(async () => {
      const { unmount: unmountOriginal } = render(
        React.createElement(OriginalComponent, testConfig.props)
      )
      unmountOriginal()

      const { unmount: unmountMigrated } = render(
        React.createElement(MigratedComponent, testConfig.props)
      )
      unmountMigrated()

      return true
    })

    report.testResults.push({
      testName: 'Prop Compatibility',
      status: propCompatibilityResult.success ? 'PASSED' : 'FAILED',
      duration: propCompatibilityResult.duration,
      error: propCompatibilityResult.error
    })

    // Test expected behaviors
    for (const behavior of testConfig.expectedBehaviors) {
      const behaviorResult = await this.testBehavior(MigratedComponent, testConfig, behavior, user)
      report.testResults.push(behaviorResult)
    }
  }

  /**
   * Test specific component behavior
   */
  private async testBehavior(
    Component: React.ComponentType<any>,
    testConfig: ComponentTestConfig,
    behavior: string,
    user: any
  ): Promise<TestResult> {
    const startTime = performance.now()

    try {
      const { container } = render(React.createElement(Component, testConfig.props))
      
      // Basic behavior tests based on behavior string
      switch (behavior) {
        case 'renders_without_crashing':
          expect(container).toBeInTheDocument()
          break
        
        case 'responds_to_click':
          const clickableElement = container.querySelector(`[data-testid="${testConfig.testId}"]`)
          if (clickableElement) {
            await user.click(clickableElement)
          }
          break
        
        case 'handles_keyboard_navigation':
          const focusableElement = container.querySelector(`[data-testid="${testConfig.testId}"]`)
          if (focusableElement) {
            focusableElement.focus()
            await user.keyboard('{Enter}')
          }
          break
        
        case 'validates_form_input':
          const inputElement = container.querySelector('input')
          if (inputElement) {
            await user.type(inputElement, 'test value')
            expect(inputElement).toHaveValue('test value')
          }
          break

        default:
          // Custom behavior test
          break
      }

      return {
        testName: `Behavior: ${behavior}`,
        status: 'PASSED',
        duration: performance.now() - startTime
      }

    } catch (error) {
      return {
        testName: `Behavior: ${behavior}`,
        status: 'FAILED',
        duration: performance.now() - startTime,
        error: error.message
      }
    }
  }

  /**
   * Run performance tests
   */
  private async runPerformanceTests(
    OriginalComponent: React.ComponentType<any>,
    MigratedComponent: React.ComponentType<any>,
    testConfig: ComponentTestConfig,
    report: MigrationTestReport
  ) {
    console.log(`📊 Running performance tests for ${testConfig.componentName}...`)

    // Measure render performance
    const originalRenderTime = await this.measureRenderTime(OriginalComponent, testConfig.props)
    const migratedRenderTime = await this.measureRenderTime(MigratedComponent, testConfig.props)

    report.performanceMetrics.renderTime = migratedRenderTime

    // Check if performance improved or regressed
    if (migratedRenderTime > originalRenderTime * 1.2) {
      report.issues.push({
        type: 'PERFORMANCE',
        severity: 'MEDIUM',
        description: `Render time regressed from ${originalRenderTime}ms to ${migratedRenderTime}ms`,
        component: testConfig.componentName,
        recommendation: 'Review component implementation for performance optimizations'
      })
    }

    report.testResults.push({
      testName: 'Performance Regression Check',
      status: migratedRenderTime <= originalRenderTime * 1.2 ? 'PASSED' : 'FAILED',
      duration: migratedRenderTime
    })
  }

  /**
   * Run accessibility tests
   */
  private async runAccessibilityTests(
    Component: React.ComponentType<any>,
    testConfig: ComponentTestConfig,
    report: MigrationTestReport
  ) {
    console.log(`♿ Running accessibility tests for ${testConfig.componentName}...`)

    const { container } = render(React.createElement(Component, testConfig.props))
    const results = await axe(container)

    report.performanceMetrics.accessibility = results.violations.length === 0 ? 100 : 
      Math.max(0, 100 - (results.violations.length * 10))

    if (results.violations.length > 0) {
      results.violations.forEach(violation => {
        report.issues.push({
          type: 'ACCESSIBILITY',
          severity: violation.impact === 'critical' ? 'CRITICAL' : 
                   violation.impact === 'serious' ? 'HIGH' : 'MEDIUM',
          description: violation.description,
          component: testConfig.componentName,
          recommendation: violation.help
        })
      })
    }

    report.testResults.push({
      testName: 'Accessibility Compliance',
      status: results.violations.length === 0 ? 'PASSED' : 'FAILED',
      duration: 0
    })
  }

  /**
   * Run visual consistency tests
   */
  private async runVisualConsistencyTests(
    OriginalComponent: React.ComponentType<any>,
    MigratedComponent: React.ComponentType<any>,
    testConfig: ComponentTestConfig,
    report: MigrationTestReport
  ) {
    console.log(`👀 Running visual consistency tests for ${testConfig.componentName}...`)

    const { container: originalContainer } = render(React.createElement(OriginalComponent, testConfig.props))
    const { container: migratedContainer } = render(React.createElement(MigratedComponent, testConfig.props))

    for (const check of testConfig.visualConsistencyChecks) {
      try {
        const originalElement = originalContainer.querySelector(check.selector)
        const migratedElement = migratedContainer.querySelector(check.selector)

        if (!originalElement || !migratedElement) {
          report.issues.push({
            type: 'VISUAL',
            severity: 'MEDIUM',
            description: `Element not found: ${check.selector}`,
            component: testConfig.componentName,
            recommendation: 'Verify selector and element structure'
          })
          continue
        }

        const originalValue = getComputedStyle(originalElement)[check.property]
        const migratedValue = getComputedStyle(migratedElement)[check.property]

        const matches = typeof check.expectedValue === 'string' 
          ? migratedValue === check.expectedValue
          : check.expectedValue.test(migratedValue)

        if (!matches) {
          report.issues.push({
            type: 'VISUAL',
            severity: 'LOW',
            description: `Visual inconsistency: ${check.property} changed from ${originalValue} to ${migratedValue}`,
            component: testConfig.componentName,
            recommendation: 'Review styling and ensure visual consistency'
          })
        }

      } catch (error) {
        report.issues.push({
          type: 'VISUAL',
          severity: 'LOW',
          description: `Visual test error: ${error.message}`,
          component: testConfig.componentName,
          recommendation: 'Review visual test configuration'
        })
      }
    }

    report.testResults.push({
      testName: 'Visual Consistency',
      status: report.issues.filter(i => i.type === 'VISUAL').length === 0 ? 'PASSED' : 'WARNING',
      duration: 0
    })
  }

  /**
   * Measure component render time
   */
  private async measureRenderTime(Component: React.ComponentType<any>, props: any): Promise<number> {
    const startTime = performance.now()
    const { unmount } = render(React.createElement(Component, props))
    const endTime = performance.now()
    unmount()
    return endTime - startTime
  }

  /**
   * Measure test execution time
   */
  private async measureTestTime(testFn: () => Promise<any>): Promise<{
    success: boolean
    duration: number
    error?: string
  }> {
    const startTime = performance.now()
    try {
      await testFn()
      return {
        success: true,
        duration: performance.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        duration: performance.now() - startTime,
        error: error.message
      }
    }
  }

  /**
   * Determine overall migration status
   */
  private determineOverallStatus(report: MigrationTestReport): 'PASSED' | 'FAILED' | 'WARNING' {
    const criticalIssues = report.issues.filter(i => i.severity === 'CRITICAL')
    const failedTests = report.testResults.filter(t => t.status === 'FAILED')

    if (criticalIssues.length > 0 || failedTests.length > 0) {
      return 'FAILED'
    }

    const highIssues = report.issues.filter(i => i.severity === 'HIGH')
    if (highIssues.length > 0) {
      return 'WARNING'
    }

    return 'PASSED'
  }

  /**
   * Generate comprehensive test report
   */
  generateReport(): {
    summary: TestSummary
    reports: MigrationTestReport[]
    recommendations: string[]
  } {
    const summary: TestSummary = {
      totalComponents: this.testReports.length,
      passed: this.testReports.filter(r => r.migrationStatus === 'PASSED').length,
      failed: this.testReports.filter(r => r.migrationStatus === 'FAILED').length,
      warnings: this.testReports.filter(r => r.migrationStatus === 'WARNING').length,
      totalIssues: this.testReports.reduce((sum, r) => sum + r.issues.length, 0),
      averagePerformance: this.calculateAveragePerformance()
    }

    const recommendations = this.generateRecommendations()

    return {
      summary,
      reports: this.testReports,
      recommendations
    }
  }

  private calculateAveragePerformance(): PerformanceMetrics {
    if (this.testReports.length === 0) {
      return { renderTime: 0, memoryUsage: 0, bundleSize: 0, accessibility: 0 }
    }

    return {
      renderTime: this.testReports.reduce((sum, r) => sum + r.performanceMetrics.renderTime, 0) / this.testReports.length,
      memoryUsage: this.testReports.reduce((sum, r) => sum + r.performanceMetrics.memoryUsage, 0) / this.testReports.length,
      bundleSize: this.testReports.reduce((sum, r) => sum + r.performanceMetrics.bundleSize, 0) / this.testReports.length,
      accessibility: this.testReports.reduce((sum, r) => sum + r.performanceMetrics.accessibility, 0) / this.testReports.length
    }
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = []
    
    // Analyze common issues
    const criticalComponents = this.testReports.filter(r => 
      r.issues.some(i => i.severity === 'CRITICAL')
    )
    
    if (criticalComponents.length > 0) {
      recommendations.push(`🚨 ${criticalComponents.length} components have critical issues requiring immediate attention`)
    }

    // Performance recommendations
    const slowComponents = this.testReports.filter(r => r.performanceMetrics.renderTime > 100)
    if (slowComponents.length > 0) {
      recommendations.push(`⚡ ${slowComponents.length} components have slow render times (>100ms) - consider performance optimization`)
    }

    // Accessibility recommendations
    const accessibilityIssues = this.testReports.filter(r => r.performanceMetrics.accessibility < 90)
    if (accessibilityIssues.length > 0) {
      recommendations.push(`♿ ${accessibilityIssues.length} components have accessibility issues - review WCAG compliance`)
    }

    return recommendations
  }
}

export interface TestSummary {
  totalComponents: number
  passed: number
  failed: number
  warnings: number
  totalIssues: number
  averagePerformance: PerformanceMetrics
}

// ============================================================================
// Test Utilities
// ============================================================================

/**
 * Create test configuration for common component types
 */
export const createTestConfig = {
  button: (props = {}): ComponentTestConfig => ({
    componentName: 'Button',
    testId: 'button',
    props: { children: 'Test Button', ...props },
    expectedBehaviors: ['renders_without_crashing', 'responds_to_click', 'handles_keyboard_navigation'],
    performanceThresholds: { renderTime: 50, memoryUsage: 1, bundleSize: 10 },
    visualConsistencyChecks: [
      { selector: 'button', property: 'backgroundColor', expectedValue: /rgb\(\d+, \d+, \d+\)/ },
      { selector: 'button', property: 'padding', expectedValue: /\d+px/ }
    ]
  }),

  input: (props = {}): ComponentTestConfig => ({
    componentName: 'Input',
    testId: 'input',
    props: { placeholder: 'Test input', ...props },
    expectedBehaviors: ['renders_without_crashing', 'validates_form_input', 'handles_keyboard_navigation'],
    performanceThresholds: { renderTime: 30, memoryUsage: 0.5, bundleSize: 8 },
    visualConsistencyChecks: [
      { selector: 'input', property: 'border', expectedValue: /1px solid/ },
      { selector: 'input', property: 'borderRadius', expectedValue: /\d+px/ }
    ]
  }),

  card: (props = {}): ComponentTestConfig => ({
    componentName: 'Card',
    testId: 'card',
    props: { children: 'Test content', ...props },
    expectedBehaviors: ['renders_without_crashing'],
    performanceThresholds: { renderTime: 20, memoryUsage: 0.3, bundleSize: 5 },
    visualConsistencyChecks: [
      { selector: '[data-testid="card"]', property: 'backgroundColor', expectedValue: /rgb\(\d+, \d+, \d+\)/ },
      { selector: '[data-testid="card"]', property: 'boxShadow', expectedValue: /rgba?\(/ }
    ]
  })
}

export default ComponentMigrationTester