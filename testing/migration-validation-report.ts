/**
 * Migration Validation Report Generator
 * ====================================
 * Analyzes actual codebase to validate completed migrations
 * Identifies regressions and migration completeness
 */

import { execSync } from 'child_process'
import { readdirSync, readFileSync, statSync, writeFileSync } from 'fs'
import { join, relative } from 'path'

// ============================================================================
// Validation Types
// ============================================================================

export interface MigrationValidationReport {
  appName: string
  migrationStatus: MigrationStatus
  componentAnalysis: ComponentAnalysis
  codebaseHealth: CodebaseHealth
  regressions: RegressionAnalysis[]
  recommendations: ValidationRecommendation[]
  completionScore: number
  timestamp: string
}

export interface MigrationStatus {
  completionPercentage: number
  migratedComponents: number
  totalComponents: number
  legacyComponents: string[]
  newSharedComponents: string[]
  status: 'COMPLETE' | 'IN_PROGRESS' | 'NOT_STARTED'
}

export interface ComponentAnalysis {
  sharedUIImports: ImportAnalysis[]
  localComponentUsage: ImportAnalysis[]
  deprecatedPatterns: DeprecatedPattern[]
  migrationOpportunities: string[]
}

export interface ImportAnalysis {
  file: string
  imports: string[]
  source: string
  migrationStatus: 'MIGRATED' | 'PARTIAL' | 'LEGACY'
}

export interface DeprecatedPattern {
  file: string
  pattern: string
  lineNumber: number
  suggestion: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH'
}

export interface CodebaseHealth {
  testCoverage: number
  buildStatus: 'PASSING' | 'FAILING' | 'UNSTABLE'
  lintingErrors: number
  typeErrors: number
  performanceIssues: number
  securityVulnerabilities: number
}

export interface RegressionAnalysis {
  type: 'FUNCTIONAL' | 'VISUAL' | 'PERFORMANCE' | 'ACCESSIBILITY'
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'
  description: string
  affectedFiles: string[]
  testFailures: string[]
  recommendation: string
}

export interface ValidationRecommendation {
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW'
  category: 'MIGRATION' | 'TESTING' | 'PERFORMANCE' | 'MAINTENANCE'
  title: string
  description: string
  actionItems: string[]
  estimatedEffort: string
}

// ============================================================================
// Migration Validator Class
// ============================================================================

export class MigrationValidator {
  private projectRoot: string
  private sharedUIPath: string

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot
    this.sharedUIPath = join(projectRoot, 'packages', 'shared-ui')
  }

  /**
   * Validate all app migrations
   */
  async validateAllMigrations(): Promise<{ [appName: string]: MigrationValidationReport }> {
    console.log('🔍 Starting comprehensive migration validation...\n')

    const appsPath = join(this.projectRoot, 'apps')
    const apps = readdirSync(appsPath).filter(app => {
      const appPath = join(appsPath, app)
      return statSync(appPath).isDirectory() && app !== 'python-services'
    })

    const results: { [appName: string]: MigrationValidationReport } = {}

    for (const app of apps) {
      console.log(`📱 Validating ${app}...`)
      try {
        results[app] = await this.validateAppMigration(app)
        console.log(`✅ ${app} validation completed`)
      } catch (error) {
        console.error(`❌ ${app} validation failed: ${error.message}`)
      }
      console.log()
    }

    // Generate summary report
    await this.generateSummaryReport(results)

    return results
  }

  /**
   * Validate specific app migration
   */
  private async validateAppMigration(appName: string): Promise<MigrationValidationReport> {
    const appPath = join(this.projectRoot, 'apps', appName)
    
    const report: MigrationValidationReport = {
      appName,
      migrationStatus: await this.analyzeMigrationStatus(appPath),
      componentAnalysis: await this.analyzeComponents(appPath),
      codebaseHealth: await this.analyzeCodebaseHealth(appPath),
      regressions: await this.analyzeRegressions(appPath),
      recommendations: [],
      completionScore: 0,
      timestamp: new Date().toISOString()
    }

    // Calculate completion score
    report.completionScore = this.calculateCompletionScore(report)

    // Generate recommendations
    report.recommendations = this.generateRecommendations(report)

    return report
  }

  /**
   * Analyze migration status by examining imports and component usage
   */
  private async analyzeMigrationStatus(appPath: string): Promise<MigrationStatus> {
    const srcPath = join(appPath, 'src')
    if (!this.directoryExists(srcPath)) {
      return {
        completionPercentage: 0,
        migratedComponents: 0,
        totalComponents: 0,
        legacyComponents: [],
        newSharedComponents: [],
        status: 'NOT_STARTED'
      }
    }

    // Find all TypeScript/JSX files
    const sourceFiles = this.findSourceFiles(srcPath)
    
    let migratedComponents = 0
    let totalComponents = 0
    const legacyComponents: string[] = []
    const newSharedComponents: string[] = []

    for (const file of sourceFiles) {
      const content = readFileSync(file, 'utf8')
      
      // Count total component definitions
      const componentMatches = content.match(/(?:function|const|class)\s+[A-Z]\w*(?:Component)?/g)
      if (componentMatches) {
        totalComponents += componentMatches.length
      }

      // Check for shared-ui imports (migrated)
      if (content.includes('@luminar/shared-ui')) {
        const sharedImports = this.extractImports(content, '@luminar/shared-ui')
        migratedComponents += sharedImports.length
        newSharedComponents.push(...sharedImports.map(imp => `${relative(this.projectRoot, file)}: ${imp}`))
      }

      // Check for local UI components (legacy)
      const localUIMatches = content.match(/from\s+['"`]\.\.?\/.*(?:components|ui)\/.*['"`]/g)
      if (localUIMatches) {
        legacyComponents.push(...localUIMatches.map(match => `${relative(this.projectRoot, file)}: ${match}`))
      }
    }

    const completionPercentage = totalComponents > 0 ? (migratedComponents / totalComponents) * 100 : 0
    
    let status: 'COMPLETE' | 'IN_PROGRESS' | 'NOT_STARTED' = 'NOT_STARTED'
    if (completionPercentage >= 95) status = 'COMPLETE'
    else if (completionPercentage > 0) status = 'IN_PROGRESS'

    return {
      completionPercentage: Math.round(completionPercentage),
      migratedComponents,
      totalComponents,
      legacyComponents,
      newSharedComponents,
      status
    }
  }

  /**
   * Analyze component usage patterns
   */
  private async analyzeComponents(appPath: string): Promise<ComponentAnalysis> {
    const srcPath = join(appPath, 'src')
    const sourceFiles = this.findSourceFiles(srcPath)

    const sharedUIImports: ImportAnalysis[] = []
    const localComponentUsage: ImportAnalysis[] = []
    const deprecatedPatterns: DeprecatedPattern[] = []
    const migrationOpportunities: string[] = []

    for (const file of sourceFiles) {
      const content = readFileSync(file, 'utf8')
      const lines = content.split('\n')

      // Analyze shared-ui imports
      const sharedImports = this.extractImports(content, '@luminar/shared-ui')
      if (sharedImports.length > 0) {
        sharedUIImports.push({
          file: relative(this.projectRoot, file),
          imports: sharedImports,
          source: '@luminar/shared-ui',
          migrationStatus: 'MIGRATED'
        })
      }

      // Analyze local component usage
      const localImports = this.extractLocalComponentImports(content)
      if (localImports.length > 0) {
        localComponentUsage.push({
          file: relative(this.projectRoot, file),
          imports: localImports,
          source: 'local',
          migrationStatus: 'LEGACY'
        })
      }

      // Find deprecated patterns
      lines.forEach((line, index) => {
        // Check for deprecated React patterns
        if (line.includes('React.FC') || line.includes('React.FunctionComponent')) {
          deprecatedPatterns.push({
            file: relative(this.projectRoot, file),
            pattern: 'React.FC usage',
            lineNumber: index + 1,
            suggestion: 'Use direct function components with TypeScript typing',
            severity: 'LOW'
          })
        }

        // Check for inline styling
        if (line.includes('style={{') && !line.includes('// @ignore-inline-style')) {
          deprecatedPatterns.push({
            file: relative(this.projectRoot, file),
            pattern: 'Inline styling',
            lineNumber: index + 1,
            suggestion: 'Use Tailwind classes or shared-ui component variants',
            severity: 'MEDIUM'
          })
        }

        // Check for hardcoded colors
        if (line.match(/#[0-9a-fA-F]{3,6}/) || line.match(/rgb\(\d+,\s*\d+,\s*\d+\)/)) {
          deprecatedPatterns.push({
            file: relative(this.projectRoot, file),
            pattern: 'Hardcoded colors',
            lineNumber: index + 1,
            suggestion: 'Use Tailwind color classes or design system tokens',
            severity: 'MEDIUM'
          })
        }
      })

      // Identify migration opportunities
      if (content.includes('className="btn') || content.includes('button')) {
        migrationOpportunities.push(`${relative(this.projectRoot, file)}: Button components can be migrated to shared-ui`)
      }
      if (content.includes('input') || content.includes('form')) {
        migrationOpportunities.push(`${relative(this.projectRoot, file)}: Form components can be migrated to shared-ui`)
      }
      if (content.includes('modal') || content.includes('dialog')) {
        migrationOpportunities.push(`${relative(this.projectRoot, file)}: Modal components can be migrated to shared-ui`)
      }
    }

    return {
      sharedUIImports,
      localComponentUsage,
      deprecatedPatterns,
      migrationOpportunities
    }
  }

  /**
   * Analyze codebase health metrics
   */
  private async analyzeCodebaseHealth(appPath: string): Promise<CodebaseHealth> {
    let testCoverage = 0
    let buildStatus: 'PASSING' | 'FAILING' | 'UNSTABLE' = 'PASSING'
    let lintingErrors = 0
    let typeErrors = 0
    let performanceIssues = 0
    let securityVulnerabilities = 0

    try {
      // Check if package.json exists
      const packageJsonPath = join(appPath, 'package.json')
      if (this.fileExists(packageJsonPath)) {
        // Try to get test coverage
        try {
          const coverageOutput = execSync('npm run test:coverage 2>/dev/null || echo "no-coverage"', {
            cwd: appPath,
            encoding: 'utf8',
            timeout: 10000
          })
          
          const coverageMatch = coverageOutput.match(/(\d+(?:\.\d+)?)%/)
          if (coverageMatch) {
            testCoverage = parseFloat(coverageMatch[1])
          }
        } catch {
          // Coverage command failed
        }

        // Check TypeScript errors
        try {
          execSync('npx tsc --noEmit --skipLibCheck', {
            cwd: appPath,
            stdio: 'pipe',
            timeout: 15000
          })
        } catch (error) {
          const errorOutput = error.stdout?.toString() || error.stderr?.toString() || ''
          const errorMatches = errorOutput.match(/error TS\d+/g)
          typeErrors = errorMatches ? errorMatches.length : 1
          buildStatus = 'FAILING'
        }

        // Check linting errors
        try {
          execSync('npx eslint src --ext .ts,.tsx --format json', {
            cwd: appPath,
            stdio: 'pipe',
            timeout: 10000
          })
        } catch (error) {
          const errorOutput = error.stdout?.toString() || ''
          try {
            const lintResults = JSON.parse(errorOutput)
            lintingErrors = lintResults.reduce((total: number, file: any) => total + file.errorCount, 0)
          } catch {
            lintingErrors = 1 // Assume at least one error if command failed
          }
        }
      }

      // Analyze performance issues (basic heuristics)
      const srcPath = join(appPath, 'src')
      if (this.directoryExists(srcPath)) {
        const sourceFiles = this.findSourceFiles(srcPath)
        for (const file of sourceFiles) {
          const content = readFileSync(file, 'utf8')
          
          // Check for potential performance issues
          if (content.includes('useEffect(() => {') && content.includes('}, [])') === false) {
            performanceIssues++ // Potentially missing dependency array
          }
          if (content.match(/\.map\(.*\.map\(/)) {
            performanceIssues++ // Nested maps
          }
          if (content.includes('JSON.parse') && content.includes('JSON.stringify')) {
            performanceIssues++ // Potential unnecessary serialization
          }
        }
      }

    } catch (error) {
      console.warn(`Health analysis failed for ${appPath}: ${error.message}`)
      buildStatus = 'UNSTABLE'
    }

    return {
      testCoverage,
      buildStatus,
      lintingErrors,
      typeErrors,
      performanceIssues,
      securityVulnerabilities
    }
  }

  /**
   * Analyze potential regressions
   */
  private async analyzeRegressions(appPath: string): Promise<RegressionAnalysis[]> {
    const regressions: RegressionAnalysis[] = []
    const srcPath = join(appPath, 'src')

    if (!this.directoryExists(srcPath)) {
      return regressions
    }

    const sourceFiles = this.findSourceFiles(srcPath)

    for (const file of sourceFiles) {
      const content = readFileSync(file, 'utf8')
      const relativePath = relative(this.projectRoot, file)

      // Check for functional regressions
      if (content.includes('// TODO') && content.includes('migration')) {
        regressions.push({
          type: 'FUNCTIONAL',
          severity: 'MEDIUM',
          description: 'Migration TODO comments indicate incomplete functionality',
          affectedFiles: [relativePath],
          testFailures: [],
          recommendation: 'Complete migration TODOs and add comprehensive tests'
        })
      }

      // Check for visual regressions
      if (content.includes('style=') && content.includes('{')) {
        regressions.push({
          type: 'VISUAL',
          severity: 'LOW',
          description: 'Inline styles may cause visual inconsistencies',
          affectedFiles: [relativePath],
          testFailures: [],
          recommendation: 'Replace inline styles with Tailwind classes or component variants'
        })
      }

      // Check for performance regressions
      if (content.includes('useMemo') === false && content.includes('expensive')) {
        regressions.push({
          type: 'PERFORMANCE',
          severity: 'MEDIUM',
          description: 'Expensive operations without memoization',
          affectedFiles: [relativePath],
          testFailures: [],
          recommendation: 'Add memoization for expensive calculations'
        })
      }

      // Check for accessibility regressions
      if (content.includes('<button') && !content.includes('aria-')) {
        regressions.push({
          type: 'ACCESSIBILITY',
          severity: 'HIGH',
          description: 'Button elements missing accessibility attributes',
          affectedFiles: [relativePath],
          testFailures: [],
          recommendation: 'Add proper ARIA labels and keyboard navigation support'
        })
      }
    }

    return regressions
  }

  /**
   * Calculate migration completion score
   */
  private calculateCompletionScore(report: MigrationValidationReport): number {
    let score = 0

    // Migration completeness (40% weight)
    score += report.migrationStatus.completionPercentage * 0.4

    // Code quality (30% weight)
    const qualityScore = Math.max(0, 100 - (
      report.codebaseHealth.lintingErrors * 2 +
      report.codebaseHealth.typeErrors * 3 +
      report.codebaseHealth.performanceIssues * 1
    ))
    score += qualityScore * 0.3

    // Regression count (20% weight)
    const criticalRegressions = report.regressions.filter(r => r.severity === 'CRITICAL').length
    const highRegressions = report.regressions.filter(r => r.severity === 'HIGH').length
    const regressionPenalty = criticalRegressions * 20 + highRegressions * 10
    score += Math.max(0, 100 - regressionPenalty) * 0.2

    // Test coverage (10% weight)
    score += report.codebaseHealth.testCoverage * 0.1

    return Math.round(Math.max(0, Math.min(100, score)))
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(report: MigrationValidationReport): ValidationRecommendation[] {
    const recommendations: ValidationRecommendation[] = []

    // Migration completion recommendations
    if (report.migrationStatus.completionPercentage < 100) {
      recommendations.push({
        priority: 'HIGH',
        category: 'MIGRATION',
        title: 'Complete Component Migration',
        description: `${100 - report.migrationStatus.completionPercentage}% of components still need migration`,
        actionItems: [
          'Migrate remaining local components to shared-ui',
          'Update import statements',
          'Test component functionality after migration',
          'Remove unused local component files'
        ],
        estimatedEffort: '2-5 days'
      })
    }

    // Code quality recommendations
    if (report.codebaseHealth.typeErrors > 0) {
      recommendations.push({
        priority: 'HIGH',
        category: 'MAINTENANCE',
        title: 'Fix TypeScript Errors',
        description: `${report.codebaseHealth.typeErrors} TypeScript errors need resolution`,
        actionItems: [
          'Review and fix type errors',
          'Add proper type definitions',
          'Update deprecated type usage',
          'Enable strict mode if not already enabled'
        ],
        estimatedEffort: '1-3 days'
      })
    }

    // Performance recommendations
    if (report.codebaseHealth.performanceIssues > 0) {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'PERFORMANCE',
        title: 'Address Performance Issues',
        description: `${report.codebaseHealth.performanceIssues} potential performance issues identified`,
        actionItems: [
          'Add React.memo for expensive components',
          'Optimize useEffect dependencies',
          'Implement code splitting where appropriate',
          'Review and optimize rendering patterns'
        ],
        estimatedEffort: '2-4 days'
      })
    }

    // Testing recommendations
    if (report.codebaseHealth.testCoverage < 80) {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'TESTING',
        title: 'Improve Test Coverage',
        description: `Test coverage is ${report.codebaseHealth.testCoverage}%, should be above 80%`,
        actionItems: [
          'Add unit tests for components',
          'Add integration tests for critical paths',
          'Set up automated testing in CI/CD',
          'Add visual regression tests'
        ],
        estimatedEffort: '3-7 days'
      })
    }

    // Critical regression recommendations
    const criticalRegressions = report.regressions.filter(r => r.severity === 'CRITICAL')
    if (criticalRegressions.length > 0) {
      recommendations.push({
        priority: 'CRITICAL',
        category: 'MIGRATION',
        title: 'Fix Critical Regressions',
        description: `${criticalRegressions.length} critical regressions require immediate attention`,
        actionItems: criticalRegressions.map(r => r.recommendation),
        estimatedEffort: '1-2 days'
      })
    }

    return recommendations
  }

  /**
   * Generate summary report for all apps
   */
  private async generateSummaryReport(results: { [appName: string]: MigrationValidationReport }): Promise<void> {
    const summary = {
      timestamp: new Date().toISOString(),
      totalApps: Object.keys(results).length,
      completedMigrations: Object.values(results).filter(r => r.migrationStatus.status === 'COMPLETE').length,
      inProgressMigrations: Object.values(results).filter(r => r.migrationStatus.status === 'IN_PROGRESS').length,
      notStartedMigrations: Object.values(results).filter(r => r.migrationStatus.status === 'NOT_STARTED').length,
      averageCompletionScore: Math.round(
        Object.values(results).reduce((sum, r) => sum + r.completionScore, 0) / Object.keys(results).length
      ),
      totalRegressions: Object.values(results).reduce((sum, r) => sum + r.regressions.length, 0),
      criticalRegressions: Object.values(results).reduce((sum, r) => 
        sum + r.regressions.filter(reg => reg.severity === 'CRITICAL').length, 0
      ),
      appDetails: results
    }

    const reportPath = join(this.projectRoot, 'testing', 'migration-validation-summary.json')
    writeFileSync(reportPath, JSON.stringify(summary, null, 2))
    
    console.log('\n' + '='.repeat(80))
    console.log('📊 MIGRATION VALIDATION SUMMARY')
    console.log('='.repeat(80))
    console.log(`📱 Total Apps: ${summary.totalApps}`)
    console.log(`✅ Completed: ${summary.completedMigrations}`)
    console.log(`🔄 In Progress: ${summary.inProgressMigrations}`)
    console.log(`⏸️  Not Started: ${summary.notStartedMigrations}`)
    console.log(`🎯 Average Score: ${summary.averageCompletionScore}/100`)
    console.log(`🐛 Total Regressions: ${summary.totalRegressions}`)
    console.log(`🚨 Critical Issues: ${summary.criticalRegressions}`)
    console.log(`📄 Detailed report: ${reportPath}`)
    console.log('='.repeat(80))
  }

  // Helper methods
  private directoryExists(path: string): boolean {
    try {
      return statSync(path).isDirectory()
    } catch {
      return false
    }
  }

  private fileExists(path: string): boolean {
    try {
      return statSync(path).isFile()
    } catch {
      return false
    }
  }

  private findSourceFiles(dir: string): string[] {
    const files: string[] = []
    
    try {
      const items = readdirSync(dir)
      
      for (const item of items) {
        const itemPath = join(dir, item)
        const stat = statSync(itemPath)
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.findSourceFiles(itemPath))
        } else if (stat.isFile() && /\.(tsx?|jsx?)$/.test(item)) {
          files.push(itemPath)
        }
      }
    } catch (error) {
      console.warn(`Could not read directory ${dir}: ${error.message}`)
    }
    
    return files
  }

  private extractImports(content: string, source: string): string[] {
    const imports: string[] = []
    const importRegex = new RegExp(`import\\s+(?:{([^}]+)}|([^\\s]+))\\s+from\\s+['"\`]${source.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]`, 'g')
    
    let match
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        // Named imports
        const namedImports = match[1].split(',').map(imp => imp.trim())
        imports.push(...namedImports)
      } else if (match[2]) {
        // Default import
        imports.push(match[2].trim())
      }
    }
    
    return imports
  }

  private extractLocalComponentImports(content: string): string[] {
    const imports: string[] = []
    const importRegex = /import\s+(?:{([^}]+)}|([^\s]+))\s+from\s+['"`]\.\.?\/.*(?:components|ui)\/.*['"`]/g
    
    let match
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        const namedImports = match[1].split(',').map(imp => imp.trim())
        imports.push(...namedImports)
      } else if (match[2]) {
        imports.push(match[2].trim())
      }
    }
    
    return imports
  }
}

export default MigrationValidator