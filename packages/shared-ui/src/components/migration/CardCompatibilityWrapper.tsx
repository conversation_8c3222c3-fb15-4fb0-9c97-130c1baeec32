import React, { forwardRef, type ReactNode } from 'react'
import { LuminarCard, type LuminarCardProps } from '../ui/display/card'
import { cn } from '../../lib/utils'

/**
 * Card Compatibility Wrapper
 * 
 * Provides a backward-compatible API that matches the local Card pattern
 * while leveraging LuminarCard's advanced features under the hood.
 * 
 * This enables gradual migration with minimal breaking changes.
 */

// Base Card component that maps to LuminarCard
interface CompatCardProps extends Omit<LuminarCardProps, 'children'> {
  children?: ReactNode
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void
}

const Card = forwardRef<HTMLDivElement, CompatCardProps>(
  ({ children, onClick, className, ...props }, ref) => {
    return (
      <LuminarCard
        ref={ref}
        className={cn('flex flex-col', className)}
        clickable={!!onClick}
        onClick={onClick}
        {...props}
      >
        {children}
      </LuminarCard>
    )
  }
)
Card.displayName = 'Card'

// CardHeader with consistent styling
interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: ReactNode
}

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-col space-y-1.5 p-6',
          // Adaptive styling based on parent glass state
          '[.glass-card_&]:bg-white/5 [.glass-card_&]:backdrop-blur-sm',
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
CardHeader.displayName = 'CardHeader'

// CardTitle with enhanced typography
interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children?: ReactNode
}

const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <h3
        ref={ref}
        className={cn(
          'text-2xl font-semibold leading-none tracking-tight',
          // Enhanced readability on glass backgrounds
          '[.glass-card_&]:text-white [.glass-card_&]:text-shadow-sm',
          className
        )}
        {...props}
      >
        {children}
      </h3>
    )
  }
)
CardTitle.displayName = 'CardTitle'

// CardDescription with adaptive contrast
interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children?: ReactNode
}

const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn(
          'text-sm text-muted-foreground',
          // Enhanced contrast on glass backgrounds
          '[.glass-card_&]:text-white/80',
          className
        )}
        {...props}
      >
        {children}
      </p>
    )
  }
)
CardDescription.displayName = 'CardDescription'

// CardContent with flexible layout
interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: ReactNode
}

const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'p-6 pt-0 flex-1',
          // Ensure content is readable on glass
          '[.glass-card_&]:text-white/90',
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
CardContent.displayName = 'CardContent'

// CardFooter with consistent spacing
interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: ReactNode
}

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center p-6 pt-0',
          // Footer styling for glass cards
          '[.glass-card_&]:text-white/70 [.glass-card_&]:border-t [.glass-card_&]:border-white/10',
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
CardFooter.displayName = 'CardFooter'

// Optional: CardAction for compatibility with some local implementations
interface CardActionProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: ReactNode
}

const CardAction = forwardRef<HTMLDivElement, CardActionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'col-start-2 row-span-2 row-start-1 self-start justify-self-end',
          '[.glass-card_&]:text-white/90',
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
CardAction.displayName = 'CardAction'

// Enhanced Card with intelligent defaults
interface EnhancedCardProps extends CompatCardProps {
  /** Enable glass morphism effect */
  enhanced?: boolean
  /** Auto-detect interactive state from children */
  autoInteractive?: boolean
  /** Preset animation style */
  animationPreset?: 'subtle' | 'standard' | 'dynamic'
}

const EnhancedCard = forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ 
    enhanced = false, 
    autoInteractive = true, 
    animationPreset = 'standard',
    children, 
    className,
    ...props 
  }, ref) => {
    // Auto-detect if card should be interactive based on onClick or buttons
    const hasInteractiveContent = autoInteractive && (
      props.onClick || 
      React.Children.toArray(children).some(child => 
        React.isValidElement(child) && 
        (child.props.onClick || child.type === 'button')
      )
    )

    // Animation presets mapping
    const animationMap = {
      subtle: 'fadeIn' as const,
      standard: 'slideUp' as const,
      dynamic: 'bounceIn' as const,
    }

    return (
      <Card
        ref={ref}
        className={cn(
          enhanced && 'glass-card',
          className
        )}
        glass={enhanced}
        interactive={hasInteractiveContent}
        animation={animationMap[animationPreset]}
        hoverable={true}
        {...props}
      >
        {children}
      </Card>
    )
  }
)
EnhancedCard.displayName = 'EnhancedCard'

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  CardAction,
  EnhancedCard,
  type CompatCardProps,
  type CardHeaderProps,
  type CardTitleProps,
  type CardDescriptionProps,
  type CardContentProps,
  type CardFooterProps,
  type CardActionProps,
  type EnhancedCardProps,
}