#!/usr/bin/env node

/**
 * Card Migration Tool
 * 
 * Automated tool to assist in migrating from local Card patterns 
 * to LuminarCard or the compatibility wrapper.
 * 
 * Usage:
 *   node card-migration-tool.js --strategy=[direct|wrapper|hybrid] --app=[app-name] [--dry-run]
 */

import fs from 'fs'
import path from 'path'
import { glob } from 'glob'

const MIGRATION_STRATEGIES = {
  direct: 'Direct migration to LuminarCard',
  wrapper: 'Use compatibility wrapper',
  hybrid: 'Selective migration based on complexity'
}

class CardMigrationTool {
  constructor(options = {}) {
    this.strategy = options.strategy || 'wrapper'
    this.appName = options.appName
    this.dryRun = options.dryRun || false
    this.stats = {
      filesAnalyzed: 0,
      filesModified: 0,
      complexityLevels: { low: 0, medium: 0, high: 0 },
      errors: []
    }
  }

  async analyzeCardUsage(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // Pattern matching for Card usage
    const cardImportPattern = /import.*{[^}]*Card[^}]*}.*from.*['"].*card['"]|import.*Card.*from.*['"].*card['"]/g
    const cardUsagePattern = /<Card[^>]*>/g
    const subComponentPattern = /<(CardHeader|CardTitle|CardDescription|CardContent|CardFooter|CardAction)[^>]*>/g
    
    const cardImports = content.match(cardImportPattern) || []
    const cardUsages = content.match(cardUsagePattern) || []
    const subComponents = content.match(subComponentPattern) || []
    
    // Complexity assessment
    let complexity = 'low'
    
    if (subComponents.length > 8 || content.includes('CardAction')) {
      complexity = 'high'
    } else if (subComponents.length > 3 || cardUsages.length > 2) {
      complexity = 'medium'
    }
    
    // Interactive elements detection
    const hasInteractivity = content.includes('onClick') || content.includes('onSelect') || content.includes('Button')
    
    // Custom styling detection
    const hasCustomStyling = content.includes('className=') && content.includes('Card')
    
    return {
      filePath,
      cardImports: cardImports.length,
      cardUsages: cardUsages.length,
      subComponents: subComponents.length,
      complexity,
      hasInteractivity,
      hasCustomStyling,
      content
    }
  }

  generateDirectMigration(analysis) {
    let { content } = analysis
    
    // Replace Card import with LuminarCard
    content = content.replace(
      /import.*{[^}]*Card[^}]*}.*from.*['"].*\/ui\/card['"]|import.*Card.*from.*['"].*\/ui\/card['"]/g,
      "import { LuminarCard } from '@luminar/shared-ui'"
    )
    
    // Convert Card usage to LuminarCard
    content = content.replace(
      /<Card([^>]*)>/g,
      (match, props) => {
        // Extract className and other props
        const classNameMatch = props.match(/className={cn\(([^}]+)\)}|className=['"]([^'"]+)['"]/)
        const onClickMatch = props.match(/onClick={([^}]+)}/)
        
        let luminarProps = []
        
        if (classNameMatch) {
          luminarProps.push(`className={cn('flex flex-col', ${classNameMatch[1] || `'${classNameMatch[2]}'`})}`)
        } else {
          luminarProps.push(`className="flex flex-col"`)
        }
        
        if (onClickMatch) {
          luminarProps.push(`clickable={true} onClick={${onClickMatch[1]}}`)
        }
        
        // Add default enhancements
        if (analysis.hasInteractivity) {
          luminarProps.push('interactive={true} hoverable={true}')
        }
        
        luminarProps.push('animation="slideUp" elevation={1}')
        
        return `<LuminarCard ${luminarProps.join(' ')}>`
      }
    )
    
    // Remove Card sub-components and flatten content
    content = content.replace(
      /<CardHeader[^>]*>(.*?)<\/CardHeader>/gs,
      '<div className="p-6 border-b border-border/20">$1</div>'
    )
    
    content = content.replace(
      /<CardContent[^>]*>(.*?)<\/CardContent>/gs,
      '<div className="p-6 flex-1">$1</div>'
    )
    
    content = content.replace(
      /<CardFooter[^>]*>(.*?)<\/CardFooter>/gs,
      '<div className="p-6 pt-0 border-t border-border/20">$1</div>'
    )
    
    // Replace Card closing tag
    content = content.replace(/<\/Card>/g, '</LuminarCard>')
    
    return content
  }

  generateWrapperMigration(analysis) {
    let { content } = analysis
    
    // Replace Card import with compatibility wrapper
    content = content.replace(
      /import.*{[^}]*Card[^}]*}.*from.*['"].*\/ui\/card['"]|import.*Card.*from.*['"].*\/ui\/card['"]/g,
      "import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, CardAction } from '@luminar/shared-ui/migration'"
    )
    
    // Add enhanced features for interactive cards
    if (analysis.hasInteractivity) {
      content = content.replace(
        /<Card([^>]*)>/g,
        (match, props) => {
          if (!props.includes('enhanced')) {
            return `<Card${props} enhanced={true} animationPreset="standard">`
          }
          return match
        }
      )
    }
    
    return content
  }

  generateHybridMigration(analysis) {
    // Use direct migration for low complexity, wrapper for medium/high
    if (analysis.complexity === 'low' && !analysis.hasCustomStyling) {
      return this.generateDirectMigration(analysis)
    } else {
      return this.generateWrapperMigration(analysis)
    }
  }

  async migrateFile(filePath) {
    try {
      const analysis = await this.analyzeCardUsage(filePath)
      this.stats.filesAnalyzed++
      this.stats.complexityLevels[analysis.complexity]++
      
      console.log(`📄 Analyzing: ${filePath}`)
      console.log(`   Complexity: ${analysis.complexity}`)
      console.log(`   Card usages: ${analysis.cardUsages}`)
      console.log(`   Sub-components: ${analysis.subComponents}`)
      
      if (analysis.cardUsages === 0) {
        console.log(`   ✅ No Card usage found, skipping`)
        return
      }
      
      let migratedContent
      
      switch (this.strategy) {
        case 'direct':
          migratedContent = this.generateDirectMigration(analysis)
          break
        case 'wrapper':
          migratedContent = this.generateWrapperMigration(analysis)
          break
        case 'hybrid':
          migratedContent = this.generateHybridMigration(analysis)
          break
        default:
          throw new Error(`Unknown strategy: ${this.strategy}`)
      }
      
      if (!this.dryRun) {
        // Create backup
        const backupPath = `${filePath}.pre-card-migration`
        fs.writeFileSync(backupPath, analysis.content)
        
        // Write migrated content
        fs.writeFileSync(filePath, migratedContent)
        this.stats.filesModified++
        console.log(`   ✅ Migrated successfully (backup: ${backupPath})`)
      } else {
        console.log(`   ✅ Migration prepared (dry-run mode)`)
      }
      
    } catch (error) {
      console.error(`   ❌ Error migrating ${filePath}:`, error.message)
      this.stats.errors.push({ filePath, error: error.message })
    }
  }

  async run() {
    console.log(`🚀 Starting Card Migration Tool`)
    console.log(`   Strategy: ${this.strategy} (${MIGRATION_STRATEGIES[this.strategy]})`)
    console.log(`   App: ${this.appName || 'all apps'}`)
    console.log(`   Mode: ${this.dryRun ? 'dry-run' : 'live migration'}`)
    console.log()
    
    // Find all TypeScript/TSX files with Card usage
    const searchPattern = this.appName 
      ? `apps/${this.appName}/src/**/*.{ts,tsx}`
      : `apps/*/src/**/*.{ts,tsx}`
    
    const files = await glob(searchPattern, { 
      ignore: ['**/node_modules/**', '**/dist/**', '**/*.test.*', '**/*.spec.*'] 
    })
    
    console.log(`📋 Found ${files.length} files to analyze`)
    console.log()
    
    // Process files
    for (const file of files) {
      await this.migrateFile(file)
    }
    
    // Print summary
    console.log()
    console.log(`📊 Migration Summary`)
    console.log(`   Files analyzed: ${this.stats.filesAnalyzed}`)
    console.log(`   Files modified: ${this.stats.filesModified}`)
    console.log(`   Complexity distribution:`)
    console.log(`     Low: ${this.stats.complexityLevels.low}`)
    console.log(`     Medium: ${this.stats.complexityLevels.medium}`)
    console.log(`     High: ${this.stats.complexityLevels.high}`)
    
    if (this.stats.errors.length > 0) {
      console.log(`   ❌ Errors: ${this.stats.errors.length}`)
      this.stats.errors.forEach(({ filePath, error }) => {
        console.log(`     ${filePath}: ${error}`)
      })
    }
    
    console.log()
    console.log(this.dryRun ? '✅ Dry-run completed' : '✅ Migration completed')
    
    if (!this.dryRun && this.stats.filesModified > 0) {
      console.log(`💡 Don't forget to run tests and update imports as needed!`)
    }
  }
}

// CLI interface
function parseArgs() {
  const args = process.argv.slice(2)
  const options = {}
  
  args.forEach(arg => {
    if (arg.startsWith('--strategy=')) {
      options.strategy = arg.split('=')[1]
    } else if (arg.startsWith('--app=')) {
      options.appName = arg.split('=')[1]
    } else if (arg === '--dry-run') {
      options.dryRun = true
    }
  })
  
  return options
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const options = parseArgs()
  
  if (options.strategy && !MIGRATION_STRATEGIES[options.strategy]) {
    console.error(`❌ Invalid strategy: ${options.strategy}`)
    console.error(`   Available strategies: ${Object.keys(MIGRATION_STRATEGIES).join(', ')}`)
    process.exit(1)
  }
  
  const tool = new CardMigrationTool(options)
  tool.run().catch(error => {
    console.error('❌ Migration tool failed:', error)
    process.exit(1)
  })
}

export { CardMigrationTool }