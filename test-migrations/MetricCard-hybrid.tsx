// MIGRATION TEST: Hybrid Approach - Selective Migration
// Original: apps/vendors/src/components/analytics/MetricCard.tsx

import { ReactNode } from 'react'
import { LuminarCard } from '@luminar/shared-ui' // Direct migration for this simple case
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { cn } from '~/lib/utils'

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeLabel?: string
  icon?: ReactNode
  trend?: 'up' | 'down' | 'neutral'
  className?: string
  loading?: boolean
  formatValue?: (value: string | number) => string
}

export function MetricCard({
  title,
  value,
  change,
  changeLabel = 'vs last period',
  icon,
  trend,
  className,
  loading = false,
  formatValue,
}: MetricCardProps) {
  const displayValue = formatValue ? formatValue(value) : value

  // Trend icon mapping
  const trendIcon = {
    up: <TrendingUp className="w-4 h-4 text-green-500" />,
    down: <TrendingDown className="w-4 h-4 text-red-500" />,
    neutral: <Minus className="w-4 h-4 text-gray-500" />
  }[trend || 'neutral']

  // Trend colors
  const trendColor = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600'
  }[trend || 'neutral']

  if (loading) {
    return (
      <LuminarCard
        className={cn('p-6', className)}
        animation="fadeIn"
        glass={false}
      >
        <div className="animate-pulse space-y-4">
          <div className="flex items-center justify-between">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-6 w-6 bg-gray-200 rounded"></div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        </div>
      </LuminarCard>
    )
  }

  return (
    <LuminarCard
      className={cn('p-6', className)}
      animation="slideUp"
      interactive={true}
      hoverable={true}
      elevation={1}
      glass={trend === 'up'} // Glass effect for positive metrics
      glassIntensity="subtle"
    >
      {/* Header with title and icon */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        {icon && <div className="text-gray-400">{icon}</div>}
      </div>

      {/* Main value */}
      <div className="mb-2">
        <p className="text-2xl font-bold text-gray-900">{displayValue}</p>
      </div>

      {/* Change indicator */}
      {change !== undefined && (
        <div className="flex items-center space-x-2">
          {trendIcon}
          <span className={cn('text-sm font-medium', trendColor)}>
            {change > 0 ? '+' : ''}{change}%
          </span>
          <span className="text-xs text-gray-500">{changeLabel}</span>
        </div>
      )}
    </LuminarCard>
  )
}

/*
MIGRATION ANALYSIS - HYBRID APPROACH:

🎯 SELECTION CRITERIA FOR DIRECT MIGRATION:
✅ Simple component structure (no complex nested elements)
✅ Low customization requirements
✅ Benefits from LuminarCard features (glass effect for positive metrics)
✅ Performance-sensitive component (frequently rendered)

📊 COMPLEXITY ASSESSMENT: LOW
- Original component was already simple
- Limited sub-component usage
- Easy to restructure as single card container
- Clear prop mapping opportunities

💡 HYBRID DECISION FACTORS:
1. **Structural Simplicity**: No CardHeader/CardContent pattern
2. **Feature Enhancement**: Glass effect adds visual value
3. **Performance Gain**: Metrics cards render frequently in dashboards
4. **Low Risk**: Simple migration with clear fallback

🔄 COMPARISON WITH ALTERNATIVES:
- **vs Wrapper**: Direct approach provides better performance for this use case
- **vs Complex Cards**: Would use wrapper for cards with complex layouts
- **vs Interactive Cards**: Direct approach leverages LuminarCard's interaction features

✅ BENEFITS ACHIEVED:
- Glass morphism for positive trends
- Smooth animations on render
- Interactive hover states
- Performance optimizations
- Consistent elevation/shadow system

⚠️ CONSIDERATIONS:
- Required manual layout adjustment
- Custom loading state implementation
- Prop mapping for glass effects

🎯 RECOMMENDED PATTERN FOR:
- Metric/dashboard cards
- Simple display components
- Performance-critical renders
- Components benefiting from visual enhancements
*/