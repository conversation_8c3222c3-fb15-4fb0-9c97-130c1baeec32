// MIGRATION TEST: Compatibility Wrapper Migration
// Original: apps/lighthouse/src/components/DocumentCard.tsx

import { Link } from '@tanstack/react-router'
import { useState } from 'react'
import { 
  EnhancedCard as Card, 
  CardHeader, 
  CardTitle, 
  CardContent 
} from '@luminar/shared-ui/migration'
import { cn } from '~/lib/utils'
import type { ProcessedDocument, SummaryResponse } from '~/types'
import { Button, LuminarBadge } from '@luminar/shared-ui'

interface DocumentCardProps {
  document: ProcessedDocument
  onDelete?: (id: string) => void
  onSelect?: (id: string) => void
  selected?: boolean
  className?: string
}

export function DocumentCard({
  document,
  onDelete,
  onSelect,
  selected,
  className,
}: DocumentCardProps) {
  const [showSummary, setShowSummary] = useState(false)
  const [summary, setSummary] = useState<SummaryResponse | null>(null)
  const [loadingSummary, setLoadingSummary] = useState(false)

  const generateSummary = async (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent card selection
    if (summary) {
      setShowSummary(!showSummary)
      return
    }

    setLoadingSummary(true)
    try {
      const response = await fetch('/api/ai/summarize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ documentId: document.id }),
      })

      if (response.ok) {
        const data = await response.json()
        setSummary(data)
        setShowSummary(true)
      }
    } catch (error) {
      console.error('Failed to generate summary:', error)
    } finally {
      setLoadingSummary(false)
    }
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete?.(document.id)
  }

  return (
    <Link to="/documents/$documentId" params={{ documentId: document.id }}>
      <Card
        className={cn(
          'hover:shadow-lg transition-shadow cursor-pointer',
          selected && 'ring-2 ring-blue-500 shadow-lg',
          className
        )}
        onClick={() => onSelect?.(document.id)}
        enhanced={selected} // Enables glass effect for selected cards
        animationPreset="standard"
        autoInteractive={true}
      >
        <CardHeader>
          <CardTitle className="text-lg">{document.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              Type: {document.type ? document.type.toUpperCase() : 'N/A'}
            </p>
            <p className="text-sm text-gray-600">
              Size: {(document.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <p className="text-sm text-gray-600">
              Added: {new Date(document.createdAt).toLocaleDateString()}
            </p>

            <div className="flex flex-wrap gap-1 mt-2">
              {document.tags.map((tag) => (
                <LuminarBadge key={tag} variant="secondary">
                  {tag}
                </LuminarBadge>
              ))}
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                size="sm"
                variant="outline"
                onClick={generateSummary}
                disabled={loadingSummary}
              >
                {loadingSummary ? 'Generating...' : showSummary ? 'Hide Summary' : 'View Summary'}
              </Button>
              {onDelete && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDelete}
                  className="text-red-600 hover:text-red-700"
                >
                  Delete
                </Button>
              )}
            </div>

            {showSummary && summary && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Summary</h4>
                <p className="text-sm mb-3">{summary.summary}</p>

                <h5 className="font-medium mb-1">Key Takeaways:</h5>
                <ul className="list-disc list-inside text-sm mb-3">
                  {summary.keyTakeaways.map((takeaway, index) => (
                    <li key={index}>{takeaway}</li>
                  ))}
                </ul>

                <div className="flex gap-1 flex-wrap">
                  {summary.topics.map((topic) => (
                    <LuminarBadge key={topic} variant="outline" className="text-xs">
                      {topic}
                    </LuminarBadge>
                  ))}
                </div>

                <p className="text-xs text-gray-500 mt-2">
                  Estimated reading time: {summary.readingTime} minutes
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

/*
MIGRATION ANALYSIS - WRAPPER APPROACH:

✅ BENEFITS:
- Minimal code changes required (mostly import updates)
- Maintains familiar Card API (Header, Title, Content, etc.)
- Automatic feature enhancement via EnhancedCard
- Backward compatibility with existing patterns
- Gradual adoption of LuminarCard features

⚠️ TRADE-OFFS:
- Additional abstraction layer
- Slightly larger bundle size
- Some LuminarCard features require wrapper updates
- Dependency on maintaining compatibility layer

📊 COMPLEXITY: LOW
- Simple import path changes
- Optional enhancement props
- Preserved component structure

🎯 RECOMMENDED FOR:
- Bulk migration scenarios (109+ files)
- Maintaining existing code patterns
- Teams preferring incremental adoption
- Preserving development velocity during migration

💡 ENHANCED FEATURES GAINED:
- Glass morphism on selected cards
- Smooth animations
- Auto-detected interactivity
- Performance optimizations
*/