import * as React from 'react'
import { Search, X, Clock, Filter, Save, ChevronDown, Plus, Trash2 } from 'lucide-react'
import { Button, Input, Label, Badge } from '~/lib/ui-components'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './dialog'
import { cn } from '@/lib/utils'

export type SearchField = {
  id: string
  label: string
  type: 'text' | 'number' | 'date' | 'select' | 'dateRange'
  placeholder?: string
  options?: { label: string; value: string }[]
}

export type SearchFilter = {
  fieldId: string
  operator: 'equals' | 'contains' | 'gt' | 'lt' | 'between' | 'in'
  value: any
  logic?: 'AND' | 'OR'
}

export type SearchPreset = {
  id: string
  name: string
  filters: SearchFilter[]
  isDefault?: boolean
}

interface AdvancedSearchProps {
  fields: SearchField[]
  presets?: SearchPreset[]
  recentSearches?: string[]
  suggestions?: string[]
  onSearch: (filters: SearchFilter[]) => void
  onSaveSearch?: (name: string, filters: SearchFilter[]) => void
  onClearAll?: () => void
  className?: string
}

export function AdvancedSearch({
  fields,
  presets = [],
  recentSearches = [],
  suggestions = [],
  onSearch,
  onSaveSearch,
  onClearAll,
  className,
}: AdvancedSearchProps) {
  const [filters, setFilters] = React.useState<SearchFilter[]>([])
  const [quickSearch, setQuickSearch] = React.useState('')
  const [showFilters, setShowFilters] = React.useState(false)
  const [showSuggestions, setShowSuggestions] = React.useState(false)
  const [selectedPreset, setSelectedPreset] = React.useState<string | null>(null)
  const [saveDialogOpen, setSaveDialogOpen] = React.useState(false)
  const [searchName, setSearchName] = React.useState('')

  // Add a new filter
  const addFilter = () => {
    const newFilter: SearchFilter = {
      fieldId: fields[0].id,
      operator: 'contains',
      value: '',
      logic: filters.length > 0 ? 'AND' : undefined,
    }
    setFilters([...filters, newFilter])
  }

  // Update a filter
  const updateFilter = (index: number, updates: Partial<SearchFilter>) => {
    const newFilters = [...filters]
    newFilters[index] = { ...newFilters[index], ...updates }
    setFilters(newFilters)
  }

  // Remove a filter
  const removeFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index))
  }

  // Apply preset
  const applyPreset = (presetId: string) => {
    const preset = presets.find((p) => p.id === presetId)
    if (preset) {
      setFilters(preset.filters)
      setSelectedPreset(presetId)
    }
  }

  // Clear all filters
  const handleClearAll = () => {
    setFilters([])
    setQuickSearch('')
    setSelectedPreset(null)
    if (onClearAll) {
      onClearAll()
    }
  }

  // Handle search
  const handleSearch = () => {
    const allFilters: SearchFilter[] = []

    // Add quick search if present
    if (quickSearch) {
      allFilters.push({
        fieldId: fields[0].id, // Use first field as default
        operator: 'contains',
        value: quickSearch,
      })
    }

    // Add advanced filters
    allFilters.push(...filters)

    onSearch(allFilters)
  }

  // Save search
  const handleSaveSearch = () => {
    if (onSaveSearch && searchName) {
      onSaveSearch(searchName, filters)
      setSaveDialogOpen(false)
      setSearchName('')
    }
  }

  // Get operator options based on field type
  const getOperatorOptions = (fieldType: SearchField['type']) => {
    switch (fieldType) {
      case 'text':
        return [
          { label: 'Contains', value: 'contains' },
          { label: 'Equals', value: 'equals' },
        ]
      case 'number':
        return [
          { label: 'Equals', value: 'equals' },
          { label: 'Greater than', value: 'gt' },
          { label: 'Less than', value: 'lt' },
          { label: 'Between', value: 'between' },
        ]
      case 'date':
      case 'dateRange':
        return [
          { label: 'Equals', value: 'equals' },
          { label: 'After', value: 'gt' },
          { label: 'Before', value: 'lt' },
          { label: 'Between', value: 'between' },
        ]
      case 'select':
        return [
          { label: 'Equals', value: 'equals' },
          { label: 'In', value: 'in' },
        ]
      default:
        return [{ label: 'Equals', value: 'equals' }]
    }
  }

  const hasActiveFilters = filters.length > 0 || quickSearch !== ''

  return (
    <div className={cn('space-y-4', className)}>
      {/* Quick Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Quick search..."
            value={quickSearch}
            onChange={(e) => setQuickSearch(e.target.value)}
            onFocus={() => setShowSuggestions(true)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch()
              }
            }}
            className="pl-9 pr-9"
          />
          {quickSearch && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0"
              onClick={() => setQuickSearch('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {/* Suggestions Dropdown */}
          {showSuggestions && (suggestions.length > 0 || recentSearches.length > 0) && (
            <div className="absolute top-full z-50 mt-1 w-full rounded-md border bg-popover p-2 shadow-md">
              {recentSearches.length > 0 && (
                <div className="space-y-1">
                  <div className="flex items-center space-x-2 px-2 py-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>Recent searches</span>
                  </div>
                  {recentSearches.map((search, index) => (
                    <button
                      key={index}
                      className="w-full rounded px-2 py-1 text-left text-sm hover:bg-accent"
                      onClick={() => {
                        setQuickSearch(search)
                        setShowSuggestions(false)
                      }}
                    >
                      {search}
                    </button>
                  ))}
                </div>
              )}
              {suggestions.length > 0 && (
                <div className="space-y-1 border-t pt-2">
                  <div className="px-2 py-1 text-xs text-muted-foreground">Suggestions</div>
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      className="w-full rounded px-2 py-1 text-left text-sm hover:bg-accent"
                      onClick={() => {
                        setQuickSearch(suggestion)
                        setShowSuggestions(false)
                      }}
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Filter Button */}
        <Popover open={showFilters} onOpenChange={setShowFilters}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filters
              {filters.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {filters.length}
                </Badge>
              )}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent align="end" className="w-[600px] p-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Advanced Filters</h4>
                <Button variant="ghost" size="sm" onClick={addFilter}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Filter
                </Button>
              </div>

              {/* Filter List */}
              <div className="space-y-2">
                {filters.map((filter, index) => {
                  const field = fields.find((f) => f.id === filter.fieldId)
                  if (!field) return null

                  return (
                    <div key={index} className="flex items-end space-x-2">
                      {index > 0 && (
                        <Select
                          value={filter.logic}
                          onValueChange={(value) =>
                            updateFilter(index, { logic: value as 'AND' | 'OR' })
                          }
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="AND">AND</SelectItem>
                            <SelectItem value="OR">OR</SelectItem>
                          </SelectContent>
                        </Select>
                      )}

                      <div className="flex-1 space-y-1">
                        <Label className="text-xs">{field.label}</Label>
                        <Select
                          value={filter.fieldId}
                          onValueChange={(value) => updateFilter(index, { fieldId: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {fields.map((f) => (
                              <SelectItem key={f.id} value={f.id}>
                                {f.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="w-32">
                        <Select
                          value={filter.operator}
                          onValueChange={(value) => updateFilter(index, { operator: value as any })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getOperatorOptions(field.type).map((op) => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex-1">
                        {field.type === 'select' ? (
                          <Select
                            value={filter.value}
                            onValueChange={(value) => updateFilter(index, { value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select value" />
                            </SelectTrigger>
                            <SelectContent>
                              {field.options?.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <Input
                            type={field.type === 'number' ? 'number' : 'text'}
                            placeholder={field.placeholder || 'Enter value'}
                            value={filter.value}
                            onChange={(e) => updateFilter(index, { value: e.target.value })}
                          />
                        )}
                      </div>

                      <Button variant="ghost" size="sm" onClick={() => removeFilter(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )
                })}

                {filters.length === 0 && (
                  <div className="py-8 text-center text-sm text-muted-foreground">
                    No filters added. Click "Add Filter" to get started.
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-2 border-t pt-4">
                <Button variant="ghost" size="sm" onClick={() => setShowFilters(false)}>
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    handleSearch()
                    setShowFilters(false)
                  }}
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Presets */}
        {presets.length > 0 && (
          <Select value={selectedPreset || ''} onValueChange={applyPreset}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Presets" />
            </SelectTrigger>
            <SelectContent>
              {presets.map((preset) => (
                <SelectItem key={preset.id} value={preset.id}>
                  {preset.name}
                  {preset.isDefault && (
                    <Badge variant="secondary" className="ml-2">
                      Default
                    </Badge>
                  )}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Actions */}
        <Button onClick={handleSearch} size="sm">
          Search
        </Button>

        {onSaveSearch && filters.length > 0 && (
          <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm">
                <Save className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Save Search</DialogTitle>
                <DialogDescription>
                  Save your current search filters for quick access later.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="search-name">Search Name</Label>
                  <Input
                    id="search-name"
                    value={searchName}
                    onChange={(e) => setSearchName(e.target.value)}
                    placeholder="Enter a name for this search"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="ghost" onClick={() => setSaveDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveSearch}>Save Search</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={handleClearAll}>
            Clear all
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {quickSearch && (
            <Badge variant="secondary" className="gap-1">
              Quick search: {quickSearch}
              <button
                onClick={() => setQuickSearch('')}
                className="ml-1 rounded-full hover:bg-muted"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {filters.map((filter, index) => {
            const field = fields.find((f) => f.id === filter.fieldId)
            return (
              <Badge key={index} variant="secondary" className="gap-1">
                {field?.label} {filter.operator} {filter.value}
                <button
                  onClick={() => removeFilter(index)}
                  className="ml-1 rounded-full hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}
