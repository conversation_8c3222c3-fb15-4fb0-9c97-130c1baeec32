import * as React from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  RowSelectionState,
  FilterFn,
} from '@tanstack/react-table'
import { ArrowUpDown, ChevronDown, Download, Eye, EyeOff, Search, X } from 'lucide-react'
import { Button, Input } from '~/lib/ui-components'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from './dropdown-menu'
import { Select, Badge, Checkbox, Table } from '~/lib/ui-components'
import { SelectContent, SelectItem, SelectTrigger, SelectValue } from './select'
import { TableBody, TableCell, TableHead, TableHeader, TableRow } from './table'
import { cn } from '@/lib/utils'

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  searchKey?: string
  pageSizeOptions?: number[]
  loading?: boolean
  emptyMessage?: string
  onRowSelection?: (selectedRows: TData[]) => void
  onExport?: (data: TData[]) => void
  bulkActions?: {
    label: string
    action: (selectedRows: TData[]) => void
    variant?: 'default' | 'destructive'
  }[]
}

// Custom filter functions
const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const itemValue = row.getValue(columnId)
  if (typeof itemValue === 'string') {
    return itemValue.toLowerCase().includes(value.toLowerCase())
  }
  return false
}

const numberRangeFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const [min, max] = value.split('-').map(Number)
  const rowValue = row.getValue(columnId) as number
  return rowValue >= min && rowValue <= max
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchKey = '',
  pageSizeOptions = [10, 20, 30, 40, 50],
  loading = false,
  emptyMessage = 'No results found.',
  onRowSelection,
  onExport,
  bulkActions = [],
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [globalFilter, setGlobalFilter] = React.useState('')

  // Enhanced columns with selection
  const enhancedColumns = React.useMemo(() => {
    const selectColumn: ColumnDef<TData, TValue> = {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    }

    return onRowSelection ? [selectColumn, ...columns] : columns
  }, [columns, onRowSelection])

  const table = useReactTable({
    data,
    columns: enhancedColumns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    filterFns: {
      fuzzy: fuzzyFilter,
      numberRange: numberRangeFilter,
    },
  })

  // Handle row selection changes
  React.useEffect(() => {
    if (onRowSelection) {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map((row) => row.original)
      onRowSelection(selectedRows)
    }
  }, [rowSelection, onRowSelection, table])

  const handleExport = () => {
    if (onExport) {
      const exportData = table.getFilteredRowModel().rows.map((row) => row.original)
      onExport(exportData)
    }
  }

  const handleClearFilters = () => {
    setColumnFilters([])
    setGlobalFilter('')
    setSorting([])
  }

  const hasActiveFilters = columnFilters.length > 0 || globalFilter !== ''

  if (loading) {
    return <TableSkeleton />
  }

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          {/* Global Search */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={`Search${searchKey ? ` by ${searchKey}` : ''}...`}
              value={globalFilter}
              onChange={(event) => setGlobalFilter(event.target.value)}
              className="pl-8"
            />
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button variant="ghost" onClick={handleClearFilters} className="h-8 px-2 lg:px-3">
              Clear
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Bulk Actions */}
          {Object.keys(rowSelection).length > 0 && bulkActions.length > 0 && (
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">{Object.keys(rowSelection).length} selected</Badge>
              {bulkActions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'default'}
                  size="sm"
                  onClick={() => {
                    const selectedRows = table
                      .getFilteredSelectedRowModel()
                      .rows.map((row) => row.original)
                    action.action(selectedRows)
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          )}

          {/* Export */}
          {onExport && (
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          )}

          {/* Column Visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Eye className="mr-2 h-4 w-4" />
                Columns
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(header.column.getCanSort() && 'cursor-pointer select-none')}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div className="flex items-center space-x-2">
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getCanSort() && <ArrowUpDown className="h-4 w-4" />}
                      </div>
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  {emptyMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-2">
        <div className="flex items-center space-x-2">
          <p className="text-sm text-muted-foreground">Rows per page</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value) => {
              table.setPageSize(Number(value))
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}

// Loading skeleton component
function TableSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="h-10 w-full max-w-sm animate-pulse bg-muted rounded-md" />
        <div className="h-10 w-32 animate-pulse bg-muted rounded-md" />
      </div>
      <div className="rounded-md border">
        <div className="h-[400px] animate-pulse bg-muted/30" />
      </div>
      <div className="flex items-center justify-between">
        <div className="h-8 w-48 animate-pulse bg-muted rounded-md" />
        <div className="flex space-x-2">
          <div className="h-8 w-20 animate-pulse bg-muted rounded-md" />
          <div className="h-8 w-20 animate-pulse bg-muted rounded-md" />
        </div>
      </div>
    </div>
  )
}
