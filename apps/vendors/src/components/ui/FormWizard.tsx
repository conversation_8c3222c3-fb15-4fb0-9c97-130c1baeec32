import * as React from 'react'
import { <PERSON>, ChevronLeft, ChevronRight, Loader2 } from 'lucide-react'
import { Button, Progress } from '~/lib/ui-components'
import { cn } from '@/lib/utils'

export interface WizardStep {
  id: string
  title: string
  description?: string
  component: React.ComponentType<any>
  validation?: (data: any) => Promise<boolean> | boolean
  isOptional?: boolean
}

interface FormWizardProps {
  steps: WizardStep[]
  onComplete: (data: any) => void | Promise<void>
  onStepChange?: (step: number, data: any) => void
  initialData?: any
  showProgress?: boolean
  showStepIndicator?: boolean
  allowStepClick?: boolean
  autoSave?: boolean
  autoSaveDelay?: number
  className?: string
}

export function FormWizard({
  steps,
  onComplete,
  onStepChange,
  initialData = {},
  showProgress = true,
  showStepIndicator = true,
  allowStepClick = false,
  autoSave = false,
  autoSaveDelay = 1000,
  className,
}: FormWizardProps) {
  const [currentStep, setCurrentStep] = React.useState(0)
  const [formData, setFormData] = React.useState(initialData)
  const [visitedSteps, setVisitedSteps] = React.useState<Set<number>>(new Set([0]))
  const [isValidating, setIsValidating] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const [stepErrors, setStepErrors] = React.useState<Record<number, string>>({})
  const autoSaveTimeoutRef = React.useRef<NodeJS.Timeout>()

  const currentStepData = steps[currentStep]
  const isFirstStep = currentStep === 0
  const isLastStep = currentStep === steps.length - 1
  const progress = ((currentStep + 1) / steps.length) * 100

  // Auto-save functionality
  React.useEffect(() => {
    if (autoSave && onStepChange) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        onStepChange(currentStep, formData)
      }, autoSaveDelay)

      return () => {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current)
        }
      }
    }
  }, [formData, currentStep, autoSave, autoSaveDelay, onStepChange])

  // Update form data for the current step
  const updateStepData = (data: any) => {
    setFormData((prev) => ({
      ...prev,
      [currentStepData.id]: {
        ...prev[currentStepData.id],
        ...data,
      },
    }))
  }

  // Validate current step
  const validateStep = async () => {
    if (!currentStepData.validation) return true

    setIsValidating(true)
    try {
      const isValid = await currentStepData.validation(formData[currentStepData.id] || {})
      if (!isValid) {
        setStepErrors({ ...stepErrors, [currentStep]: 'Please complete all required fields' })
      } else {
        const newErrors = { ...stepErrors }
        delete newErrors[currentStep]
        setStepErrors(newErrors)
      }
      return isValid
    } catch (error) {
      setStepErrors({ ...stepErrors, [currentStep]: 'Validation error occurred' })
      return false
    } finally {
      setIsValidating(false)
    }
  }

  // Navigate to next step
  const handleNext = async () => {
    const isValid = await validateStep()
    if (!isValid && !currentStepData.isOptional) return

    if (isLastStep) {
      setIsSubmitting(true)
      try {
        await onComplete(formData)
      } finally {
        setIsSubmitting(false)
      }
    } else {
      const nextStep = currentStep + 1
      setCurrentStep(nextStep)
      setVisitedSteps((prev) => new Set([...prev, nextStep]))
      if (onStepChange) {
        onStepChange(nextStep, formData)
      }
    }
  }

  // Navigate to previous step
  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStep = currentStep - 1
      setCurrentStep(prevStep)
      if (onStepChange) {
        onStepChange(prevStep, formData)
      }
    }
  }

  // Navigate to specific step (if allowed)
  const handleStepClick = async (stepIndex: number) => {
    if (!allowStepClick || stepIndex === currentStep) return

    // Only allow navigation to visited steps or the next step
    if (!visitedSteps.has(stepIndex) && stepIndex !== currentStep + 1) return

    // Validate current step before moving
    if (stepIndex > currentStep) {
      const isValid = await validateStep()
      if (!isValid && !currentStepData.isOptional) return
    }

    setCurrentStep(stepIndex)
    setVisitedSteps((prev) => new Set([...prev, stepIndex]))
    if (onStepChange) {
      onStepChange(stepIndex, formData)
    }
  }

  // Get step status
  const getStepStatus = (stepIndex: number) => {
    if (stepIndex === currentStep) return 'current'
    if (visitedSteps.has(stepIndex)) return 'completed'
    if (stepErrors[stepIndex]) return 'error'
    return 'upcoming'
  }

  const StepComponent = currentStepData.component

  return (
    <div className={cn('space-y-8', className)}>
      {/* Progress Bar */}
      {showProgress && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Step {currentStep + 1} of {steps.length}
            </span>
            <span className="font-medium">{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Step Indicator */}
      {showStepIndicator && (
        <nav aria-label="Progress">
          <ol className="flex items-center justify-between">
            {steps.map((step, index) => {
              const status = getStepStatus(index)
              const isClickable =
                allowStepClick && (visitedSteps.has(index) || index === currentStep + 1)

              return (
                <li key={step.id} className="flex items-center">
                  <button
                    className={cn(
                      'flex flex-col items-center',
                      isClickable && 'cursor-pointer',
                      !isClickable && 'cursor-default'
                    )}
                    onClick={() => isClickable && handleStepClick(index)}
                    disabled={!isClickable}
                  >
                    <span
                      className={cn(
                        'flex h-10 w-10 items-center justify-center rounded-full border-2 text-sm font-medium transition-colors',
                        status === 'current' && 'border-primary bg-primary text-primary-foreground',
                        status === 'completed' &&
                          'border-primary bg-primary text-primary-foreground',
                        status === 'error' &&
                          'border-destructive bg-destructive text-destructive-foreground',
                        status === 'upcoming' && 'border-muted-foreground text-muted-foreground'
                      )}
                    >
                      {status === 'completed' ? <Check className="h-5 w-5" /> : index + 1}
                    </span>
                    <span
                      className={cn(
                        'mt-2 text-sm font-medium',
                        status === 'current' && 'text-foreground',
                        status === 'completed' && 'text-foreground',
                        status === 'error' && 'text-destructive',
                        status === 'upcoming' && 'text-muted-foreground'
                      )}
                    >
                      {step.title}
                    </span>
                    {step.description && (
                      <span className="mt-1 text-xs text-muted-foreground">{step.description}</span>
                    )}
                  </button>

                  {index < steps.length - 1 && (
                    <div
                      className={cn(
                        'mx-4 h-0.5 w-full bg-muted-foreground/20',
                        visitedSteps.has(index + 1) && 'bg-primary'
                      )}
                    />
                  )}
                </li>
              )
            })}
          </ol>
        </nav>
      )}

      {/* Step Content */}
      <div className="min-h-[400px]">
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold">{currentStepData.title}</h2>
            {currentStepData.description && (
              <p className="mt-2 text-muted-foreground">{currentStepData.description}</p>
            )}
          </div>

          {stepErrors[currentStep] && (
            <div className="rounded-md border border-destructive bg-destructive/10 p-3 text-sm text-destructive">
              {stepErrors[currentStep]}
            </div>
          )}

          <StepComponent
            data={formData[currentStepData.id] || {}}
            onChange={updateStepData}
            formData={formData}
          />
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between border-t pt-6">
        <Button variant="outline" onClick={handlePrevious} disabled={isFirstStep}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        <div className="flex items-center space-x-2">
          {currentStepData.isOptional && (
            <span className="text-sm text-muted-foreground">Optional</span>
          )}

          {autoSave && <span className="text-sm text-muted-foreground">Auto-saving...</span>}
        </div>

        <Button onClick={handleNext} disabled={isValidating || isSubmitting}>
          {isValidating || isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isValidating ? 'Validating...' : 'Submitting...'}
            </>
          ) : isLastStep ? (
            'Complete'
          ) : (
            <>
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>

      {/* Summary for last step */}
      {isLastStep && (
        <div className="rounded-md border bg-muted/30 p-4">
          <h3 className="mb-3 font-medium">Review Your Information</h3>
          <dl className="space-y-2 text-sm">
            {steps.slice(0, -1).map((step, index) => {
              const stepData = formData[step.id]
              if (!stepData || Object.keys(stepData).length === 0) return null

              return (
                <div key={step.id} className="flex flex-col space-y-1">
                  <dt className="font-medium">{step.title}:</dt>
                  <dd className="text-muted-foreground">
                    {typeof stepData === 'object'
                      ? Object.entries(stepData)
                          .map(([key, value]) => `${key}: ${value}`)
                          .join(', ')
                      : String(stepData)}
                  </dd>
                </div>
              )
            })}
          </dl>
        </div>
      )}
    </div>
  )
}
