import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { MetricCard } from './MetricCard'

describe('MetricCard', () => {
  const defaultProps = {
    title: 'Total Vendors',
    value: '125',
    icon: '🏢',
    trend: 'up' as const,
    change: 12.5,
    changeLabel: 'vs last period',
  }

  it('renders metric title and value', () => {
    render(<MetricCard {...defaultProps} />)

    expect(screen.getByText('Total Vendors')).toBeInTheDocument()
    expect(screen.getByText('125')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<MetricCard {...defaultProps} loading={true} />)

    expect(screen.queryByText('125')).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<MetricCard {...defaultProps} className="custom-class" />)

    const card = screen.getByRole('generic')
    expect(card).toHaveClass('custom-class')
  })

  it('handles formatValue function', () => {
    const formatValue = (value: string | number) => `$${value}`
    render(<MetricCard {...defaultProps} formatValue={formatValue} />)

    expect(screen.getByText('$125')).toBeInTheDocument()
  })
})