import { useQuery } from '@tanstack/react-query'
import { ChartContainer } from './ChartContainer'
import { LineChart } from './LineChart'
import { Star } from 'lucide-react'
import { format } from 'date-fns'
import { useState } from 'react'
import { Button } from '~/lib/ui-components'

interface RatingTrendsChartProps {
  dateRange?: { from: Date; to: Date }
  height?: number
}

export function RatingTrendsChart({ dateRange, height = 350 }: RatingTrendsChartProps) {
  const [showFilled, setShowFilled] = useState(true)

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['rating-trends', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.from.toISOString())
        params.append('endDate', dateRange.to.toISOString())
      }

      const response = await fetch(`/api/analytics/reviews?${params}`)
      if (!response.ok) throw new Error('Failed to fetch rating trends')
      const result = await response.json()

      // Transform monthly review data for the chart
      const monthlyData = Object.entries(result.monthlyReviews || {})
        .map(([month, data]: [string, any]) => ({
          month,
          date: new Date(month + '-01'),
          avgRating: Number(data.avgRating.toFixed(2)),
          count: data.count,
        }))
        .sort((a, b) => a.date.getTime() - b.date.getTime())
        .slice(-12) // Last 12 months

      // Calculate category trends
      const categoryTrends = Object.entries(result.avgRatingByCategory || {}).map(
        ([category, rating]) => ({
          category,
          rating: Number((rating as number).toFixed(2)),
        })
      )

      return { monthlyData, categoryTrends }
    },
  })

  const chartData = data?.monthlyData || []

  return (
    <ChartContainer
      title="Rating Trends Over Time"
      subtitle="Average vendor ratings by month"
      icon={<Star className="h-5 w-5" />}
      onRefresh={() => refetch()}
      isLoading={isLoading}
      error={error}
    >
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Current average: {data?.monthlyData?.[data.monthlyData.length - 1]?.avgRating || 'N/A'}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={() => setShowFilled(!showFilled)}>
              {showFilled ? 'Hide' : 'Show'} Fill
            </Button>
          </div>
        </div>

        {chartData.length > 0 ? (
          <>
            <LineChart
              data={chartData}
              dataKey="avgRating"
              xAxisKey="month"
              height={height}
              filled={showFilled}
              formatXAxis={(value) => format(new Date(value + '-01'), 'MMM')}
              formatYAxis={(value) => value.toFixed(1)}
              showDots={true}
            />

            {data?.categoryTrends && data.categoryTrends.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium mb-3">Average Rating by Category</h4>
                <div className="grid grid-cols-2 gap-3">
                  {data.categoryTrends
                    .sort((a, b) => b.rating - a.rating)
                    .map((category) => (
                      <div
                        key={category.category}
                        className="flex items-center justify-between p-2 bg-muted rounded-lg"
                      >
                        <span className="text-sm">{category.category}</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium">{category.rating}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No rating data available</p>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}
