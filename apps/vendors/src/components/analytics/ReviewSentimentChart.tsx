import { useQuery } from '@tanstack/react-query'
import { ChartContainer } from './ChartContainer'
import { Donut<PERSON>hart } from './DonutChart'
import { <PERSON><PERSON><PERSON> } from './BarChart'
import { MessageSquare } from 'lucide-react'
import { useState } from 'react'
import { <PERSON><PERSON> } from '~/lib/ui-components'

interface ReviewSentimentChartProps {
  dateRange?: { from: Date; to: Date }
  height?: number
}

export function ReviewSentimentChart({ dateRange, height = 350 }: ReviewSentimentChartProps) {
  const [view, setView] = useState<'sentiment' | 'distribution'>('sentiment')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['review-sentiment', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.from.toISOString())
        params.append('endDate', dateRange.to.toISOString())
      }

      const response = await fetch(`/api/analytics/reviews?${params}`)
      if (!response.ok) throw new Error('Failed to fetch review sentiment data')
      const result = await response.json()

      // Transform sentiment data
      const sentimentData = [
        { name: 'Positive', value: result.sentimentAnalysis?.positive || 0, color: '#10b981' },
        { name: 'Neutral', value: result.sentimentAnalysis?.neutral || 0, color: '#f59e0b' },
        { name: 'Negative', value: result.sentimentAnalysis?.negative || 0, color: '#ef4444' },
      ]

      // Transform rating distribution
      const ratingData = Object.entries(result.ratingDistribution || {})
        .map(([rating, count]) => ({
          rating: `${rating} Stars`,
          count: count as number,
        }))
        .sort((a, b) => b.rating.localeCompare(a.rating))

      return {
        sentimentData,
        ratingData,
        totalReviews: result.totalReviews || 0,
        averageRating: result.averageRating || 0,
      }
    },
  })

  const getSentimentColors = () => ['#10b981', '#f59e0b', '#ef4444']
  const getRatingColor = (rating: string) => {
    const stars = parseInt(rating)
    if (stars >= 4) return '#10b981'
    if (stars === 3) return '#f59e0b'
    return '#ef4444'
  }

  return (
    <ChartContainer
      title="Review Sentiment Analysis"
      subtitle="Understanding customer feedback patterns"
      icon={<MessageSquare className="h-5 w-5" />}
      onRefresh={() => refetch()}
      isLoading={isLoading}
      error={error}
    >
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Total reviews: {data?.totalReviews || 0} | Avg rating: {data?.averageRating || 'N/A'}
          </div>
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={view === 'sentiment' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('sentiment')}
            >
              Sentiment
            </Button>
            <Button
              variant={view === 'distribution' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('distribution')}
            >
              Distribution
            </Button>
          </div>
        </div>

        {view === 'sentiment' ? (
          data?.sentimentData && data.sentimentData.length > 0 ? (
            <DonutChart
              data={data.sentimentData}
              height={height}
              colors={getSentimentColors()}
              centerLabel={{
                value: `${data.averageRating}`,
                label: 'Avg Rating',
              }}
              formatValue={(value) => `${value} reviews`}
            />
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-sm text-muted-foreground">No sentiment data available</p>
            </div>
          )
        ) : data?.ratingData && data.ratingData.length > 0 ? (
          <BarChart
            data={data.ratingData}
            dataKey="count"
            xAxisKey="rating"
            height={height}
            colors={data.ratingData.map((item) => getRatingColor(item.rating))}
            layout="vertical"
            formatYAxis={(value) => value.toString()}
          />
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No rating distribution data available</p>
          </div>
        )}

        {/* Sentiment Legend */}
        {view === 'sentiment' && data?.sentimentData && (
          <div className="flex justify-center gap-6 mt-4">
            {data.sentimentData.map((item) => (
              <div key={item.name} className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }} />
                <span className="text-sm text-muted-foreground">
                  {item.name}: {((item.value / data.totalReviews) * 100).toFixed(1)}%
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </ChartContainer>
  )
}
