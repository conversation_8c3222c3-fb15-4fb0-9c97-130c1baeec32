import { Button } from '~/lib/ui-components'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import { Download, FileImage, FileJson, FileSpreadsheet, FileText } from 'lucide-react'
import { useToast } from '~/hooks/use-toast'

interface ChartExportProps {
  data: any
  filename?: string
  chartRef?: React.RefObject<HTMLDivElement>
  onExport?: (format: string) => void
}

export function ChartExport({
  data,
  filename = 'chart-export',
  chartRef,
  onExport,
}: ChartExportProps) {
  const { toast } = useToast()

  const exportAsJSON = () => {
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    toast({
      title: 'Export successful',
      description: 'Chart data exported as JSON',
    })

    onExport?.('json')
  }

  const exportAsCSV = () => {
    // Convert data to CSV format
    const headers = Object.keys(data[0] || {})
    const csvContent = [
      headers.join(','),
      ...data.map((row: any) =>
        headers
          .map((header) => {
            const value = row[header]
            return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
          })
          .join(',')
      ),
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    toast({
      title: 'Export successful',
      description: 'Chart data exported as CSV',
    })

    onExport?.('csv')
  }

  const exportAsPNG = async () => {
    if (!chartRef?.current) {
      toast({
        title: 'Export failed',
        description: 'Chart reference not available',
        variant: 'destructive',
      })
      return
    }

    try {
      // Import html2canvas dynamically
      const { default: html2canvas } = await import('html2canvas')

      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: null,
        scale: 2, // Higher quality
      })

      canvas.toBlob((blob) => {
        if (!blob) return

        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${filename}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        toast({
          title: 'Export successful',
          description: 'Chart exported as PNG image',
        })

        onExport?.('png')
      })
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Failed to export chart as image',
        variant: 'destructive',
      })
    }
  }

  const exportAsPDF = async () => {
    if (!chartRef?.current) {
      toast({
        title: 'Export failed',
        description: 'Chart reference not available',
        variant: 'destructive',
      })
      return
    }

    try {
      // Import dependencies dynamically
      const { default: html2canvas } = await import('html2canvas')
      const { default: jsPDF } = await import('jspdf')

      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: null,
        scale: 2,
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'px',
        format: [canvas.width, canvas.height],
      })

      pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height)
      pdf.save(`${filename}.pdf`)

      toast({
        title: 'Export successful',
        description: 'Chart exported as PDF',
      })

      onExport?.('pdf')
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Failed to export chart as PDF',
        variant: 'destructive',
      })
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Export Format</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={exportAsJSON}>
          <FileJson className="h-4 w-4 mr-2" />
          Export as JSON
        </DropdownMenuItem>
        <DropdownMenuItem onClick={exportAsCSV}>
          <FileSpreadsheet className="h-4 w-4 mr-2" />
          Export as CSV
        </DropdownMenuItem>
        {chartRef && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={exportAsPNG}>
              <FileImage className="h-4 w-4 mr-2" />
              Export as PNG
            </DropdownMenuItem>
            <DropdownMenuItem onClick={exportAsPDF}>
              <FileText className="h-4 w-4 mr-2" />
              Export as PDF
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
