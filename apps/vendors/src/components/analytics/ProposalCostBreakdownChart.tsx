import { useQuery } from '@tanstack/react-query'
import { ChartContainer } from './ChartContainer'
import { Donut<PERSON>hart } from './DonutChart'
import { Bar<PERSON>hart } from './BarChart'
import { DollarSign } from 'lucide-react'
import { useState } from 'react'
import { But<PERSON> } from '~/lib/ui-components'
import type { CostItem } from '~/types/vendor-management'

interface ProposalCostBreakdownChartProps {
  proposalId?: string
  costs?: CostItem[]
  totalCost?: number
  height?: number
}

export function ProposalCostBreakdownChart({
  proposalId,
  costs: staticCosts,
  totalCost: staticTotalCost,
  height = 350,
}: ProposalCostBreakdownChartProps) {
  const [view, setView] = useState<'donut' | 'bar'>('donut')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['proposal-cost-breakdown', proposalId],
    queryFn: async () => {
      if (!proposalId) return null
      const response = await fetch(`/api/proposals/${proposalId}`)
      if (!response.ok) throw new Error('Failed to fetch proposal data')
      const proposal = await response.json()
      return {
        costs: proposal.costs,
        totalCost: proposal.totalCost,
      }
    },
    enabled: !!proposalId && !staticCosts,
  })

  const costs = staticCosts || data?.costs || []
  const totalCost = staticTotalCost || data?.totalCost || 0

  // Transform costs for charts
  const chartData = costs.map((item) => ({
    name: item.item,
    value: item.amount,
    percentage: totalCost > 0 ? ((item.amount / totalCost) * 100).toFixed(1) : '0',
  }))

  // Sort by value for bar chart
  const sortedData = [...chartData].sort((a, b) => b.value - a.value)

  const loading = proposalId ? isLoading : false

  return (
    <ChartContainer
      title="Cost Breakdown"
      subtitle={`Total: ${new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(totalCost)}`}
      icon={<DollarSign className="h-5 w-5" />}
      onRefresh={proposalId ? () => refetch() : undefined}
      isLoading={loading}
      error={error}
    >
      <div className="space-y-4">
        <div className="flex justify-end">
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={view === 'donut' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('donut')}
            >
              Donut
            </Button>
            <Button
              variant={view === 'bar' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('bar')}
            >
              Bar
            </Button>
          </div>
        </div>

        {chartData.length > 0 ? (
          <>
            {view === 'donut' ? (
              <DonutChart
                data={chartData}
                height={height}
                centerLabel={{
                  value: new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(totalCost),
                  label: 'Total Cost',
                }}
                formatValue={(value) =>
                  new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                  }).format(value)
                }
              />
            ) : (
              <BarChart
                data={sortedData}
                dataKey="value"
                xAxisKey="name"
                height={height}
                layout="horizontal"
                formatYAxis={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
            )}

            {/* Cost Items List */}
            <div className="mt-6 space-y-2">
              <h4 className="text-sm font-medium mb-3">Cost Details</h4>
              {sortedData.map((item) => (
                <div
                  key={item.name}
                  className="flex items-center justify-between p-3 bg-muted rounded-lg"
                >
                  <div>
                    <p className="text-sm font-medium">{item.name}</p>
                    <p className="text-xs text-muted-foreground">{item.percentage}% of total</p>
                  </div>
                  <p className="text-sm font-medium">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'USD',
                    }).format(item.value)}
                  </p>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No cost breakdown available</p>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}
