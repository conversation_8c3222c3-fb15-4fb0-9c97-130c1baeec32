import { LuminarLineChart, type LineChartData } from '@luminar/shared-ui/charts'
import { cn } from '~/lib/utils'

interface LineChartProps {
  data: any[]
  dataKey: string | string[]
  xAxisKey: string
  height?: number
  colors?: string[]
  showGrid?: boolean
  showLegend?: boolean
  showDots?: boolean
  curved?: boolean
  filled?: boolean
  customTooltip?: React.ComponentType<any>
  formatXAxis?: (value: any) => string
  formatYAxis?: (value: any) => string
  className?: string
  strokeWidth?: number
}

const defaultColors = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#06b6d4', // cyan-500
  '#84cc16', // lime-500
]

export function LineChart({
  data,
  dataKey,
  xAxisKey,
  height = 300,
  colors = defaultColors,
  showGrid = true,
  showLegend = true,
  showDots = true,
  curved = true,
  filled = false,
  customTooltip,
  formatXAxis,
  formatYAxis,
  className,
  strokeWidth = 2,
}: LineChartProps) {
  // Transform data to LuminarLineChart format
  const chartData: LineChartData[] = data.map((item, index) => {
    const dataKeys = Array.isArray(dataKey) ? dataKey : [dataKey]
    const value = typeof item[dataKeys[0]] === 'number' ? item[dataKeys[0]] : 0

    return {
      label: formatXAxis ? formatXAxis(item[xAxisKey]) : String(item[xAxisKey]),
      value,
      color: colors[index % colors.length],
    }
  })

  return (
    <LuminarLineChart
      data={chartData}
      height={height}
      showGrid={showGrid}
      showLegend={showLegend}
      showDots={showDots}
      curved={curved}
      filled={filled}
      strokeWidth={strokeWidth}
      glass={true}
      animated={true}
      className={cn('w-full', className)}
    />
  )
}
