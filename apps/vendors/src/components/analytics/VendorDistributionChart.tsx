import { useQuery } from '@tanstack/react-query'
import { ChartContainer } from './ChartContainer'
import { <PERSON><PERSON><PERSON> } from './PieChart'
import { Donut<PERSON>hart } from './DonutChart'
import { Building2 } from 'lucide-react'
import { useState } from 'react'
import { But<PERSON> } from '~/lib/ui-components'

interface VendorDistributionChartProps {
  dateRange?: { from: Date; to: Date }
  height?: number
}

export function VendorDistributionChart({ dateRange, height = 350 }: VendorDistributionChartProps) {
  const [chartType, setChartType] = useState<'pie' | 'donut'>('donut')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['vendor-distribution', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.from.toISOString())
        params.append('endDate', dateRange.to.toISOString())
      }

      const response = await fetch(`/api/analytics/vendors?${params}`)
      if (!response.ok) throw new Error('Failed to fetch vendor distribution')
      const result = await response.json()

      // Transform data for the chart
      return Object.entries(result.categoryBreakdown || {}).map(([category, count]) => ({
        name: category,
        value: count as number,
      }))
    },
  })

  const handleExport = () => {
    // Export functionality will be handled by ChartExport component
    console.log('Exporting vendor distribution data')
  }

  const totalVendors = data?.reduce((sum, item) => sum + item.value, 0) || 0

  return (
    <ChartContainer
      title="Vendor Distribution by Category"
      subtitle="Active vendors across different service categories"
      icon={<Building2 className="h-5 w-5" />}
      onRefresh={() => refetch()}
      onExport={handleExport}
      isLoading={isLoading}
      error={error}
    >
      <div className="space-y-4">
        <div className="flex justify-end">
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={chartType === 'pie' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setChartType('pie')}
            >
              Pie
            </Button>
            <Button
              variant={chartType === 'donut' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setChartType('donut')}
            >
              Donut
            </Button>
          </div>
        </div>

        {data && data.length > 0 ? (
          chartType === 'donut' ? (
            <DonutChart
              data={data}
              height={height}
              centerLabel={{
                value: totalVendors,
                label: 'Total Vendors',
              }}
              formatValue={(value) => `${value} vendors`}
            />
          ) : (
            <PieChart data={data} height={height} formatValue={(value) => `${value} vendors`} />
          )
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No vendor data available</p>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}
