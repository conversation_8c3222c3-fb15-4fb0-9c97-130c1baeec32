import { LuminarDonutChart, type DonutChartData } from '@luminar/shared-ui/charts'
import { cn } from '~/lib/utils'

interface DonutChartProps {
  data: Array<{
    name: string
    value: number
    [key: string]: any
  }>
  height?: number
  colors?: string[]
  innerRadius?: number
  outerRadius?: number
  showLegend?: boolean
  showLabels?: boolean
  customTooltip?: React.ComponentType<any>
  className?: string
  formatValue?: (value: number) => string
  interactive?: boolean
  centerLabel?: {
    value: string | number
    label?: string
  }
}

const defaultColors = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#06b6d4', // cyan-500
  '#84cc16', // lime-500
]

export function DonutChart({
  data,
  height = 300,
  colors = defaultColors,
  innerRadius = 60,
  outerRadius = 80,
  showLegend = true,
  showLabels = true,
  customTooltip,
  className,
  formatValue,
  interactive = true,
  centerLabel,
}: DonutChartProps) {
  // Transform data to LuminarDonutChart format
  const chartData: DonutChartData[] = data.map((item, index) => ({
    label: item.name,
    value: item.value,
    color: colors[index % colors.length],
  }))

  const totalValue = data.reduce((sum, entry) => sum + entry.value, 0)

  return (
    <div className="relative">
      <LuminarDonutChart
        data={chartData}
        height={height}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        showLegend={showLegend}
        showLabels={showLabels}
        interactive={interactive}
        glass={true}
        animated={true}
        className={cn('w-full', className)}
      />

      {centerLabel && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            <div className="text-2xl font-bold">{centerLabel.value}</div>
            {centerLabel.label && (
              <div className="text-sm text-muted-foreground">{centerLabel.label}</div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
