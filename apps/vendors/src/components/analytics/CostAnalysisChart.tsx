import { useQuery } from '@tanstack/react-query'
import { ChartContainer } from './ChartContainer'
import { <PERSON><PERSON><PERSON> } from './BarChart'
import { LineChart } from './LineChart'
import { DollarSign } from 'lucide-react'
import { format } from 'date-fns'
import { useState } from 'react'
import { Button } from '~/lib/ui-components'

interface CostAnalysisChartProps {
  dateRange?: { from: Date; to: Date }
  height?: number
}

export function CostAnalysisChart({ dateRange, height = 350 }: CostAnalysisChartProps) {
  const [chartType, setChartType] = useState<'bar' | 'line'>('bar')
  const [view, setView] = useState<'monthly' | 'category'>('monthly')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['cost-analysis', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.from.toISOString())
        params.append('endDate', dateRange.to.toISOString())
      }

      const [proposalsRes, vendorsRes] = await Promise.all([
        fetch(`/api/analytics/proposals?${params}`),
        fetch(`/api/analytics/vendors?${params}`),
      ])

      if (!proposalsRes.ok || !vendorsRes.ok) {
        throw new Error('Failed to fetch cost analysis data')
      }

      const proposalsData = await proposalsRes.json()
      const vendorsData = await vendorsRes.json()

      // Transform monthly data
      const monthlySpending = Object.entries(proposalsData.monthlyData || {})
        .map(([month, data]: [string, any]) => ({
          month,
          date: new Date(month + '-01'),
          totalSpent: data.totalValue,
          approved: data.approved,
          avgValue: data.avgValue,
        }))
        .sort((a, b) => a.date.getTime() - b.date.getTime())
        .slice(-12)

      // Calculate spending by category
      const categorySpending = vendorsData.vendorPerformance?.reduce((acc: any, vendor: any) => {
        if (!acc[vendor.category]) {
          acc[vendor.category] = {
            category: vendor.category,
            totalValue: 0,
            count: 0,
          }
        }
        acc[vendor.category].totalValue += vendor.totalValue
        acc[vendor.category].count += vendor.approvedProposals
        return acc
      }, {})

      const categoryData = Object.values(categorySpending || {}) as any[]

      // Cost ranges
      const costRanges = Object.entries(proposalsData.costRanges || {}).map(([range, count]) => ({
        range: range.replace(/_/g, ' ').replace(/k/g, 'K'),
        count: count as number,
      }))

      return { monthlySpending, categoryData, costRanges }
    },
  })

  const monthlyData = data?.monthlySpending || []
  const categoryData = data?.categoryData || []

  const chartData = view === 'monthly' ? monthlyData : categoryData
  const xAxisKey = view === 'monthly' ? 'month' : 'category'
  const dataKey = view === 'monthly' ? 'totalSpent' : 'totalValue'

  return (
    <ChartContainer
      title="Cost Analysis"
      subtitle="Spending patterns and cost distribution"
      icon={<DollarSign className="h-5 w-5" />}
      onRefresh={() => refetch()}
      isLoading={isLoading}
      error={error}
    >
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={view === 'monthly' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('monthly')}
            >
              Monthly
            </Button>
            <Button
              variant={view === 'category' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('category')}
            >
              By Category
            </Button>
          </div>
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={chartType === 'bar' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setChartType('bar')}
            >
              Bar
            </Button>
            <Button
              variant={chartType === 'line' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setChartType('line')}
              disabled={view === 'category'}
            >
              Line
            </Button>
          </div>
        </div>

        {chartData.length > 0 ? (
          <>
            {chartType === 'bar' || view === 'category' ? (
              <BarChart
                data={chartData}
                dataKey={dataKey}
                xAxisKey={xAxisKey}
                height={height}
                formatXAxis={
                  view === 'monthly' ? (value) => format(new Date(value + '-01'), 'MMM') : undefined
                }
                formatYAxis={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
            ) : (
              <LineChart
                data={chartData}
                dataKey={dataKey}
                xAxisKey={xAxisKey}
                height={height}
                formatXAxis={(value) => format(new Date(value + '-01'), 'MMM')}
                formatYAxis={(value) => `$${(value / 1000).toFixed(0)}k`}
                filled={true}
              />
            )}

            {data?.costRanges && data.costRanges.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium mb-3">Proposal Cost Distribution</h4>
                <div className="space-y-2">
                  {data.costRanges.map((range) => {
                    const total = data.costRanges.reduce((sum: number, r: any) => sum + r.count, 0)
                    const percentage = total > 0 ? ((range.count / total) * 100).toFixed(1) : '0'

                    return (
                      <div key={range.range} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>{range.range}</span>
                          <span className="text-muted-foreground">
                            {range.count} proposals ({percentage}%)
                          </span>
                        </div>
                        <div className="w-full bg-secondary rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No cost data available</p>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}
