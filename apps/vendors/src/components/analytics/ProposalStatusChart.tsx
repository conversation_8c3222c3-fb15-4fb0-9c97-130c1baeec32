import { useQuery } from '@tanstack/react-query'
import { ChartContainer } from './ChartContainer'
import { <PERSON><PERSON><PERSON> } from './BarChart'
import { FileText } from 'lucide-react'
import { useState } from 'react'
import { Button } from '~/lib/ui-components'

interface ProposalStatusChartProps {
  dateRange?: { from: Date; to: Date }
  height?: number
}

export function ProposalStatusChart({ dateRange, height = 350 }: ProposalStatusChartProps) {
  const [view, setView] = useState<'count' | 'value'>('count')

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['proposal-status', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.from.toISOString())
        params.append('endDate', dateRange.to.toISOString())
      }

      const response = await fetch(`/api/analytics/proposals?${params}`)
      if (!response.ok) throw new Error('Failed to fetch proposal data')
      const result = await response.json()

      // Transform data for the chart
      const statusData = Object.entries(result.statusDistribution || {}).map(([status, count]) => {
        // Calculate average value per status (mock data for now)
        const avgValues: Record<string, number> = {
          approved: 85000,
          rejected: 45000,
          negotiation: 65000,
          submitted: 55000,
          draft: 35000,
        }

        return {
          status: status.charAt(0).toUpperCase() + status.slice(1),
          count: count as number,
          value: (count as number) * (avgValues[status] || 50000),
        }
      })

      return statusData.sort((a, b) => b.count - a.count)
    },
  })

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      Approved: '#10b981',
      Rejected: '#ef4444',
      Negotiation: '#f59e0b',
      Submitted: '#3b82f6',
      Draft: '#6b7280',
    }
    return colors[status] || '#6b7280'
  }

  const chartData = data || []
  const colors = chartData.map((item) => getStatusColor(item.status))

  return (
    <ChartContainer
      title="Proposal Status Breakdown"
      subtitle="Distribution of proposals by current status"
      icon={<FileText className="h-5 w-5" />}
      onRefresh={() => refetch()}
      isLoading={isLoading}
      error={error}
    >
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Total proposals: {chartData.reduce((sum, item) => sum + item.count, 0)}
          </div>
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            <Button
              variant={view === 'count' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('count')}
            >
              Count
            </Button>
            <Button
              variant={view === 'value' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setView('value')}
            >
              Value
            </Button>
          </div>
        </div>

        {chartData.length > 0 ? (
          <BarChart
            data={chartData}
            dataKey={view}
            xAxisKey="status"
            height={height}
            colors={colors}
            formatYAxis={view === 'value' ? (value) => `$${(value / 1000).toFixed(0)}k` : undefined}
            layout="horizontal"
          />
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No proposal data available</p>
          </div>
        )}
      </div>
    </ChartContainer>
  )
}
