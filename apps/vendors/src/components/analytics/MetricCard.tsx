import { ReactNode } from 'react'
import { MetricCard as LuminarMetricCard } from '~/lib/ui-components'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { cn } from '~/lib/utils'

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  changeLabel?: string
  icon?: ReactNode
  trend?: 'up' | 'down' | 'neutral'
  className?: string
  loading?: boolean
  formatValue?: (value: string | number) => string
}

export function MetricCard({
  title,
  value,
  change,
  changeLabel = 'vs last period',
  icon,
  trend,
  className,
  loading = false,
  formatValue,
}: MetricCardProps) {
  const displayValue = formatValue ? formatValue(value) : value

  // Try to use LuminarMetricCard if it matches our interface
  return (
    <LuminarMetricCard
      title={title}
      value={displayValue}
      change={change}
      trend={trend as any}
      icon={icon}
      loading={loading}
      className={className}
    />
  )
}
