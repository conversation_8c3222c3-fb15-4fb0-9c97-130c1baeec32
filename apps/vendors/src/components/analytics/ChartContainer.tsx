import { ReactNode } from 'react'
import { <PERSON>, But<PERSON> } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Download, Maximize2, RefreshCw } from 'lucide-react'
import { cn } from '~/lib/utils'

interface ChartContainerProps {
  title: string
  subtitle?: string
  icon?: ReactNode
  children: ReactNode
  className?: string
  onRefresh?: () => void
  onExport?: () => void
  onFullscreen?: () => void
  isLoading?: boolean
  error?: Error | null
}

export function ChartContainer({
  title,
  subtitle,
  icon,
  children,
  className,
  onRefresh,
  onExport,
  onFullscreen,
  isLoading = false,
  error = null,
}: ChartContainerProps) {
  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div className="flex items-center gap-2">
          {icon && <div className="text-muted-foreground">{icon}</div>}
          <div>
            <CardTitle className="text-lg font-semibold">{title}</CardTitle>
            {subtitle && <p className="text-sm text-muted-foreground mt-1">{subtitle}</p>}
          </div>
        </div>
        <div className="flex items-center gap-1">
          {onRefresh && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={onRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
            </Button>
          )}
          {onFullscreen && (
            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onFullscreen}>
              <Maximize2 className="h-4 w-4" />
            </Button>
          )}
          {onExport && (
            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onExport}>
              <Download className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Error loading chart</p>
              <p className="text-xs text-red-500 mt-1">{error.message}</p>
            </div>
          </div>
        ) : isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mx-auto" />
              <p className="text-sm text-muted-foreground mt-2">Loading chart data...</p>
            </div>
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  )
}
