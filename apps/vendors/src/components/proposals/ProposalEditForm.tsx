import { useEffect, useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useUpdateProposal, useProposal } from '~/hooks/useProposals'
import { useVendors } from '~/hooks/useVendors'
import { Button } from '~/lib/ui-components'
import { Input } from '~/lib/ui-components'
import { Textarea } from '~/lib/ui-components'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { Card } from '~/lib/ui-components'
import { CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { Badge } from '~/lib/ui-components'
import { FileUpload } from '~/components/ui/FileUpload'
import { useToast } from '~/hooks/use-toast'
import { Calendar } from '~/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '~/lib/utils'
import { Loader2, Plus, X, Save, CalendarIcon, DollarSign, AlertCircle } from 'lucide-react'
import type { ProposalStatus } from '~/types/vendor-management'

const costItemSchema = z.object({
  item: z.string().min(1, 'Item description is required'),
  amount: z.number().min(0, 'Amount must be positive'),
  currency: z.string().default('USD'),
})

const proposalSchema = z.object({
  vendorId: z.string().min(1, 'Vendor is required'),
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  costs: z.array(costItemSchema).min(1, 'At least one cost item is required'),
  status: z.enum(['draft', 'submitted', 'negotiation', 'approved', 'rejected'] as const),
  attachments: z.array(z.string()).default([]),
  validUntil: z.date(),
  notes: z.string().optional(),
})

type ProposalFormData = z.infer<typeof proposalSchema>

interface ProposalEditFormProps {
  proposalId: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface VersionHistory {
  version: number
  updatedAt: Date
  updatedBy: string
  changes: string[]
}

export function ProposalEditForm({ proposalId, open, onOpenChange }: ProposalEditFormProps) {
  const { toast } = useToast()
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([])
  const [isDraft, setIsDraft] = useState(false)
  const [versionHistory, setVersionHistory] = useState<VersionHistory[]>([])

  const { data: proposal, isLoading } = useProposal(proposalId)
  const { data: vendors } = useVendors()
  const updateProposal = useUpdateProposal()

  const form = useForm<ProposalFormData>({
    resolver: zodResolver(proposalSchema),
    defaultValues: {
      vendorId: '',
      title: '',
      description: '',
      costs: [{ item: '', amount: 0, currency: 'USD' }],
      status: 'draft',
      attachments: [],
      validUntil: new Date(),
      notes: '',
    },
  })

  const {
    fields: costFields,
    append: appendCost,
    remove: removeCost,
  } = useFieldArray({
    control: form.control,
    name: 'costs',
  })

  // Load proposal data when available
  useEffect(() => {
    if (proposal) {
      form.reset({
        vendorId: proposal.vendorId,
        title: proposal.title,
        description: proposal.description,
        costs:
          proposal.costs.length > 0 ? proposal.costs : [{ item: '', amount: 0, currency: 'USD' }],
        status: proposal.status,
        attachments: proposal.attachments || [],
        validUntil: new Date(proposal.validUntil),
        notes: '',
      })
      setUploadedFiles(proposal.attachments || [])
      setIsDraft(proposal.status === 'draft')
    }
  }, [proposal, form])

  // Auto-save functionality
  useEffect(() => {
    if (!autoSaveEnabled || !isDraft) return

    const subscription = form.watch((value) => {
      const timeoutId = setTimeout(() => {
        if (form.formState.isDirty && !form.formState.isSubmitting) {
          handleAutoSave(value as ProposalFormData)
        }
      }, 3000) // 3 second debounce

      return () => clearTimeout(timeoutId)
    })

    return () => subscription.unsubscribe()
  }, [form, autoSaveEnabled, isDraft])

  const handleAutoSave = async (data: ProposalFormData) => {
    try {
      // Calculate total cost
      const totalCost = data.costs.reduce((sum, item) => sum + item.amount, 0)

      await updateProposal.mutateAsync({
        id: proposalId,
        updates: {
          ...data,
          totalCost,
          attachments: uploadedFiles,
        },
      })

      setLastSaved(new Date())

      // Add to version history
      setVersionHistory((prev) => [
        ...prev,
        {
          version: prev.length + 1,
          updatedAt: new Date(),
          updatedBy: 'Current User',
          changes: ['Auto-saved draft'],
        },
      ])
    } catch (error) {
      console.error('Auto-save failed:', error)
    }
  }

  const onSubmit = async (data: ProposalFormData) => {
    try {
      // Calculate total cost
      const totalCost = data.costs.reduce((sum, item) => sum + item.amount, 0)

      await updateProposal.mutateAsync({
        id: proposalId,
        updates: {
          ...data,
          totalCost,
          attachments: uploadedFiles,
          submittedAt: data.status === 'submitted' ? new Date() : undefined,
        },
      })

      toast({
        title: 'Proposal updated',
        description: 'Proposal has been successfully updated',
      })

      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update proposal. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleAddCostItem = () => {
    appendCost({ item: '', amount: 0, currency: 'USD' })
  }

  const handleFileUpload = (files: string[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
  }

  const calculateTotalCost = () => {
    const costs = form.watch('costs')
    return costs.reduce((sum, item) => sum + (item.amount || 0), 0)
  }

  const handleStatusChange = (newStatus: ProposalStatus) => {
    form.setValue('status', newStatus)
    setIsDraft(newStatus === 'draft')

    // Add to version history
    setVersionHistory((prev) => [
      ...prev,
      {
        version: prev.length + 1,
        updatedAt: new Date(),
        updatedBy: 'Current User',
        changes: [`Status changed to ${newStatus}`],
      },
    ])
  }

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-5xl">
          <div className="flex items-center justify-center p-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  const totalCost = calculateTotalCost()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Proposal</DialogTitle>
          <DialogDescription>Update proposal details and manage costs</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="costs">Cost Breakdown</TabsTrigger>
                <TabsTrigger value="attachments">Attachments</TabsTrigger>
                <TabsTrigger value="status">Status & Workflow</TabsTrigger>
                <TabsTrigger value="history">Version History</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Proposal Information</CardTitle>
                    <CardDescription>Basic details about the proposal</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="vendorId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vendor</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a vendor" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {vendors?.map((vendor) => (
                                <SelectItem key={vendor.id} value={vendor.id}>
                                  {vendor.companyName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Proposal Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Q4 2024 Leadership Training Program" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Provide a detailed description of the proposal..."
                              className="min-h-[120px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="validUntil"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Valid Until</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    'w-full pl-3 text-left font-normal',
                                    !field.value && 'text-muted-foreground'
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, 'PPP')
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date < new Date()}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Internal Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Add any internal notes or comments..."
                              className="min-h-[80px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="costs" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Cost Breakdown</CardTitle>
                    <CardDescription>Itemized costs for this proposal</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50%]">Item Description</TableHead>
                          <TableHead>Currency</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead className="w-[50px]"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {costFields.map((field, index) => (
                          <TableRow key={field.id}>
                            <TableCell>
                              <FormField
                                control={form.control}
                                name={`costs.${index}.item`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input placeholder="Training materials" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </TableCell>
                            <TableCell>
                              <FormField
                                control={form.control}
                                name={`costs.${index}.currency`}
                                render={({ field }) => (
                                  <FormItem>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="w-[80px]">
                                          <SelectValue />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="USD">USD</SelectItem>
                                        <SelectItem value="EUR">EUR</SelectItem>
                                        <SelectItem value="GBP">GBP</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormItem>
                                )}
                              />
                            </TableCell>
                            <TableCell>
                              <FormField
                                control={form.control}
                                name={`costs.${index}.amount`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="0.00"
                                        {...field}
                                        onChange={(e) =>
                                          field.onChange(parseFloat(e.target.value) || 0)
                                        }
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </TableCell>
                            <TableCell>
                              {costFields.length > 1 && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removeCost(index)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>

                    <div className="mt-4 space-y-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleAddCostItem}
                        className="w-full"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Cost Item
                      </Button>

                      <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-5 w-5" />
                          <span className="text-lg font-semibold">Total Cost:</span>
                        </div>
                        <span className="text-2xl font-bold">${totalCost.toFixed(2)} USD</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="attachments" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Attachments</CardTitle>
                    <CardDescription>Upload supporting documents and files</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FileUpload
                      onUpload={handleFileUpload}
                      acceptedTypes={['.pdf', '.doc', '.docx', '.xlsx', '.ppt', '.pptx']}
                      maxFiles={20}
                      maxSize={25}
                    />

                    {uploadedFiles.length > 0 && (
                      <div className="mt-6 space-y-2">
                        <h4 className="text-sm font-medium">Attached Files</h4>
                        {uploadedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-muted rounded-lg"
                          >
                            <span className="text-sm truncate">{file}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setUploadedFiles((prev) => prev.filter((_, i) => i !== index))
                              }}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="status" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Status & Workflow</CardTitle>
                    <CardDescription>Manage proposal status and workflow</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Status</FormLabel>
                          <Select onValueChange={handleStatusChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {(
                                [
                                  'draft',
                                  'submitted',
                                  'negotiation',
                                  'approved',
                                  'rejected',
                                ] as ProposalStatus[]
                              ).map((status) => (
                                <SelectItem key={status} value={status}>
                                  <Badge
                                    variant={
                                      status === 'approved'
                                        ? 'default'
                                        : status === 'rejected'
                                          ? 'destructive'
                                          : status === 'submitted'
                                            ? 'secondary'
                                            : 'outline'
                                    }
                                  >
                                    {status.charAt(0).toUpperCase() + status.slice(1)}
                                  </Badge>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="rounded-lg border p-4 space-y-2">
                      <h4 className="font-medium flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        Workflow Rules
                      </h4>
                      <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                        <li>Draft proposals can be edited freely</li>
                        <li>Submitted proposals require approval to modify</li>
                        <li>Approved proposals are locked and cannot be edited</li>
                        <li>Rejected proposals can be revised and resubmitted</li>
                      </ul>
                    </div>

                    {proposal?.submittedAt && (
                      <div className="text-sm text-muted-foreground">
                        Submitted on: {format(new Date(proposal.submittedAt), 'PPP')}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Version History</CardTitle>
                    <CardDescription>Track changes and updates to this proposal</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {versionHistory.length > 0 ? (
                      <div className="space-y-4">
                        {versionHistory.map((version, index) => (
                          <div
                            key={index}
                            className="flex items-start gap-4 p-4 bg-muted rounded-lg"
                          >
                            <div className="flex-shrink-0">
                              <Badge variant="outline">v{version.version}</Badge>
                            </div>
                            <div className="flex-1 space-y-1">
                              <p className="text-sm font-medium">{version.updatedBy}</p>
                              <p className="text-xs text-muted-foreground">
                                {format(version.updatedAt, 'PPp')}
                              </p>
                              <ul className="text-sm list-disc list-inside">
                                {version.changes.map((change, i) => (
                                  <li key={i}>{change}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground text-center py-8">
                        No version history available
                      </p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                {isDraft && (
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={autoSaveEnabled}
                      onChange={(e) => setAutoSaveEnabled(e.target.checked)}
                      className="rounded"
                    />
                    Auto-save draft
                  </label>
                )}
                {lastSaved && <span>Last saved: {format(lastSaved, 'p')}</span>}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={updateProposal.isPending}>
                  {updateProposal.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
