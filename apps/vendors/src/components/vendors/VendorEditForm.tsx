import { useEffect, useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useUpdateVendor, useVendor } from '~/hooks/useVendors'
import { 
  Button, 
  Input, 
  Form, 
  Select, 
  Badge, 
  Card, 
  FileUpload 
} from '~/lib/ui-components'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { useToast } from '~/hooks/use-toast'
import { Loader2, Plus, X, Save } from 'lucide-react'
import type { VendorCategory, VendorStatus, PhoneNumberType } from '~/types/vendor-management'

const vendorSchema = z.object({
  companyName: z.string().min(2, 'Company name is required'),
  contactPerson: z.string().min(2, 'Contact person is required'),
  phoneNumbers: z
    .array(
      z.object({
        type: z.enum(['primary', 'secondary'] as const),
        number: z.string().regex(/^[\d\s\-\+\(\)]+$/, 'Invalid phone number format'),
      })
    )
    .min(1, 'At least one phone number is required'),
  email: z.string().email('Invalid email address'),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  category: z.enum(['Training', 'Assessment', 'Certification', 'Content', 'Consulting'] as const),
  status: z.enum(['active', 'inactive', 'pending'] as const),
  certifications: z.array(z.string()).default([]),
  documents: z.array(z.string()).optional(),
})

type VendorFormData = z.infer<typeof vendorSchema>

interface VendorEditFormProps {
  vendorId: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function VendorEditForm({ vendorId, open, onOpenChange }: VendorEditFormProps) {
  const { toast } = useToast()
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([])

  const { data: vendor, isLoading } = useVendor(vendorId)
  const updateVendor = useUpdateVendor()

  const form = useForm<VendorFormData>({
    resolver: zodResolver(vendorSchema),
    defaultValues: {
      companyName: '',
      contactPerson: '',
      phoneNumbers: [{ type: 'primary', number: '' }],
      email: '',
      website: '',
      category: 'Training',
      status: 'active',
      certifications: [],
      documents: [],
    },
  })

  const {
    fields: phoneFields,
    append: appendPhone,
    remove: removePhone,
  } = useFieldArray({
    control: form.control,
    name: 'phoneNumbers',
  })

  const {
    fields: certFields,
    append: appendCert,
    remove: removeCert,
  } = useFieldArray({
    control: form.control,
    name: 'certifications',
  })

  // Load vendor data when available
  useEffect(() => {
    if (vendor) {
      form.reset({
        companyName: vendor.companyName,
        contactPerson: vendor.contactPerson,
        phoneNumbers:
          vendor.phoneNumbers.length > 0 ? vendor.phoneNumbers : [{ type: 'primary', number: '' }],
        email: vendor.email,
        website: vendor.website || '',
        category: vendor.category,
        status: vendor.status,
        certifications: vendor.certifications || [],
        documents: [],
      })
    }
  }, [vendor, form])

  // Auto-save functionality
  useEffect(() => {
    if (!autoSaveEnabled) return

    const subscription = form.watch((value) => {
      const timeoutId = setTimeout(() => {
        if (form.formState.isDirty && !form.formState.isSubmitting) {
          handleAutoSave(value as VendorFormData)
        }
      }, 2000) // 2 second debounce

      return () => clearTimeout(timeoutId)
    })

    return () => subscription.unsubscribe()
  }, [form, autoSaveEnabled])

  const handleAutoSave = async (data: VendorFormData) => {
    try {
      await updateVendor.mutateAsync({
        id: vendorId,
        updates: data,
      })
      setLastSaved(new Date())
      toast({
        title: 'Auto-saved',
        description: 'Your changes have been saved automatically',
        duration: 2000,
      })
    } catch (error) {
      console.error('Auto-save failed:', error)
    }
  }

  const onSubmit = async (data: VendorFormData) => {
    try {
      await updateVendor.mutateAsync({
        id: vendorId,
        updates: {
          ...data,
          website: data.website || undefined,
        },
      })

      toast({
        title: 'Vendor updated',
        description: 'Vendor information has been successfully updated',
      })

      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update vendor. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleAddPhone = () => {
    const hasSecondary = phoneFields.some((field) => field.type === 'secondary')
    if (phoneFields.length < 2) {
      appendPhone({ type: hasSecondary ? 'primary' : 'secondary', number: '' })
    }
  }

  const handleAddCertification = () => {
    const newCert = prompt('Enter certification name:')
    if (newCert) {
      appendCert(newCert)
    }
  }

  const handleFileUpload = (files: string[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
    form.setValue('documents', [...uploadedFiles, ...files])
  }

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <div className="flex items-center justify-center p-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Vendor</DialogTitle>
          <DialogDescription>Update vendor information and manage certifications</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
                <TabsTrigger value="certifications">Certifications</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Company Information</CardTitle>
                    <CardDescription>Basic details about the vendor company</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Acme Training Solutions" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {(
                                  [
                                    'Training',
                                    'Assessment',
                                    'Certification',
                                    'Content',
                                    'Consulting',
                                  ] as VendorCategory[]
                                ).map((category) => (
                                  <SelectItem key={category} value={category}>
                                    {category}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {(['active', 'inactive', 'pending'] as VendorStatus[]).map(
                                  (status) => (
                                    <SelectItem key={status} value={status}>
                                      <Badge
                                        variant={
                                          status === 'active'
                                            ? 'default'
                                            : status === 'inactive'
                                              ? 'secondary'
                                              : 'outline'
                                        }
                                      >
                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                      </Badge>
                                    </SelectItem>
                                  )
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contact" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                    <CardDescription>How to reach this vendor</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="contactPerson"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Person</FormLabel>
                          <FormControl>
                            <Input placeholder="Jane Smith" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website (Optional)</FormLabel>
                          <FormControl>
                            <Input type="url" placeholder="https://vendor.com" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <FormLabel>Phone Numbers</FormLabel>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleAddPhone}
                          disabled={phoneFields.length >= 2}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Phone
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {phoneFields.map((field, index) => (
                          <div key={field.id} className="flex gap-2">
                            <FormField
                              control={form.control}
                              name={`phoneNumbers.${index}.type`}
                              render={({ field }) => (
                                <FormItem className="w-32">
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="primary">Primary</SelectItem>
                                      <SelectItem value="secondary">Secondary</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name={`phoneNumbers.${index}.number`}
                              render={({ field }) => (
                                <FormItem className="flex-1">
                                  <FormControl>
                                    <Input placeholder="+****************" {...field} />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                            {phoneFields.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => removePhone(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="certifications" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Certifications & Qualifications</CardTitle>
                    <CardDescription>
                      Professional certifications held by this vendor
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between mb-4">
                      <FormLabel>Certifications</FormLabel>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleAddCertification}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Certification
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {certFields.map((field, index) => (
                        <div
                          key={field.id}
                          className="flex items-center gap-2 p-3 bg-muted rounded-md"
                        >
                          <span className="flex-1">{form.watch(`certifications.${index}`)}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeCert(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {certFields.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No certifications added
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documents" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Documents & Files</CardTitle>
                    <CardDescription>
                      Upload vendor documents, contracts, and certifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FileUpload
                      onUpload={handleFileUpload}
                      acceptedTypes={['.pdf', '.doc', '.docx', '.xlsx', '.png', '.jpg']}
                      maxFiles={10}
                    />

                    {uploadedFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <h4 className="text-sm font-medium">Uploaded Files</h4>
                        {uploadedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-muted rounded"
                          >
                            <span className="text-sm truncate">{file}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setUploadedFiles((prev) => prev.filter((_, i) => i !== index))
                              }}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={autoSaveEnabled}
                    onChange={(e) => setAutoSaveEnabled(e.target.checked)}
                    className="rounded"
                  />
                  Auto-save
                </label>
                {lastSaved && <span>Last saved: {lastSaved.toLocaleTimeString()}</span>}
              </div>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={updateVendor.isPending}>
                  {updateVendor.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
