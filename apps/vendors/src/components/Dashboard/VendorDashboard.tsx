import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useVendorStore } from '@/lib/stores/vendor.store';
import { useAuthStore } from '@/lib/stores/auth.store';
import { 
  Building2, 
  TrendingUp, 
  DollarSign, 
  Award, 
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Star,
  Users,
  BarChart3,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Cell,
  LineChart,
  Line
} from 'recharts';

interface DashboardStats {
  totalVendors: number;
  activeContracts: number;
  pendingEvaluations: number;
  totalContractValue: number;
  averagePerformanceScore: number;
  contractsExpiringSoon: number;
  qualifiedVendors: number;
  riskVendors: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const VendorDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    vendors, 
    contracts, 
    evaluations, 
    analytics,
    fetchVendors, 
    fetchContracts, 
    fetchEvaluations,
    fetchAnalytics 
  } = useVendorStore();
  
  const [stats, setStats] = useState<DashboardStats>({
    totalVendors: 0,
    activeContracts: 0,
    pendingEvaluations: 0,
    totalContractValue: 0,
    averagePerformanceScore: 0,
    contractsExpiringSoon: 0,
    qualifiedVendors: 0,
    riskVendors: 0
  });
  
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        fetchVendors(),
        fetchContracts(),
        fetchEvaluations(),
        fetchAnalytics()
      ]);
      calculateStats();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = () => {
    const activeContracts = contracts.filter(c => c.status === 'active');
    const pendingEvaluations = evaluations.filter(e => e.status === 'pending');
    
    const totalContractValue = activeContracts.reduce((sum, contract) => sum + contract.value, 0);
    
    const completedEvaluations = evaluations.filter(e => e.status === 'completed');
    const averageScore = completedEvaluations.length > 0 
      ? completedEvaluations.reduce((sum, e) => sum + e.overallScore, 0) / completedEvaluations.length 
      : 0;

    const contractsExpiringSoon = activeContracts.filter(contract => {
      const endDate = new Date(contract.endDate);
      const now = new Date();
      const diffTime = endDate.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 30 && diffDays > 0;
    }).length;

    const qualifiedVendors = vendors.filter(v => v.qualificationStatus === 'qualified').length;
    const riskVendors = vendors.filter(v => v.riskLevel === 'high').length;

    setStats({
      totalVendors: vendors.length,
      activeContracts: activeContracts.length,
      pendingEvaluations: pendingEvaluations.length,
      totalContractValue,
      averagePerformanceScore: Math.round(averageScore),
      contractsExpiringSoon,
      qualifiedVendors,
      riskVendors
    });
  };

  const getVendorsByCategory = () => {
    const categoryCount: Record<string, number> = {};
    vendors.forEach(vendor => {
      categoryCount[vendor.category] = (categoryCount[vendor.category] || 0) + 1;
    });

    return Object.entries(categoryCount).map(([category, count]) => ({
      category,
      count,
      percentage: Math.round((count / vendors.length) * 100)
    }));
  };

  const getPerformanceTrends = () => {
    // Mock performance trends data
    return [
      { month: 'Jan', score: 85, contracts: 45 },
      { month: 'Feb', score: 87, contracts: 52 },
      { month: 'Mar', score: 83, contracts: 48 },
      { month: 'Apr', score: 89, contracts: 55 },
      { month: 'May', score: 91, contracts: 58 },
      { month: 'Jun', score: 88, contracts: 61 }
    ];
  };

  const getTopVendors = () => {
    const vendorScores = vendors
      .map(vendor => {
        const vendorEvaluations = evaluations.filter(e => e.vendorId === vendor.id && e.status === 'completed');
        const avgScore = vendorEvaluations.length > 0 
          ? vendorEvaluations.reduce((sum, e) => sum + e.overallScore, 0) / vendorEvaluations.length 
          : 0;
        return { ...vendor, avgScore };
      })
      .sort((a, b) => b.avgScore - a.avgScore)
      .slice(0, 5);

    return vendorScores;
  };

  const getRecentActivity = () => {
    const recentContracts = contracts
      .filter(c => c.status === 'active')
      .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
      .slice(0, 5);

    const recentEvaluations = evaluations
      .filter(e => e.status === 'completed')
      .sort((a, b) => new Date(b.completedAt || '').getTime() - new Date(a.completedAt || '').getTime())
      .slice(0, 5);

    return {
      recentContracts: recentContracts.map(contract => {
        const vendor = vendors.find(v => v.id === contract.vendorId);
        return {
          id: contract.id,
          vendor: vendor?.name || 'Unknown Vendor',
          type: 'Contract',
          value: contract.value,
          date: contract.startDate,
          status: contract.status
        };
      }),
      recentEvaluations: recentEvaluations.map(evaluation => {
        const vendor = vendors.find(v => v.id === evaluation.vendorId);
        return {
          id: evaluation.id,
          vendor: vendor?.name || 'Unknown Vendor',
          type: 'Evaluation',
          score: evaluation.overallScore,
          date: evaluation.completedAt || evaluation.createdAt,
          status: evaluation.status
        };
      })
    };
  };

  const getContractsByStatus = () => {
    const statusCount: Record<string, number> = {};
    contracts.forEach(contract => {
      statusCount[contract.status] = (statusCount[contract.status] || 0) + 1;
    });

    return Object.entries(statusCount).map(([status, count]) => ({
      status: status.charAt(0).toUpperCase() + status.slice(1),
      count,
      value: count
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Vendor Dashboard</h1>
          <p className="text-gray-600">Manage and monitor vendor relationships</p>
        </div>
        <Button onClick={loadDashboardData} disabled={isLoading}>
          Refresh Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Vendors</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVendors}</div>
            <p className="text-xs text-muted-foreground">
              {stats.qualifiedVendors} qualified
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Contracts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeContracts}</div>
            <p className="text-xs text-muted-foreground">
              {stats.contractsExpiringSoon} expiring soon
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contract Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${stats.totalContractValue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Total active value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Performance</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averagePerformanceScore}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.pendingEvaluations} pending reviews
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alert Cards */}
      {(stats.contractsExpiringSoon > 0 || stats.riskVendors > 0 || stats.pendingEvaluations > 0) && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.contractsExpiringSoon > 0 && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-orange-600" />
                  <div>
                    <h3 className="font-semibold text-orange-900">Contracts Expiring</h3>
                    <p className="text-sm text-orange-700">
                      {stats.contractsExpiringSoon} contracts expire within 30 days
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {stats.riskVendors > 0 && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <div>
                    <h3 className="font-semibold text-red-900">High Risk Vendors</h3>
                    <p className="text-sm text-red-700">
                      {stats.riskVendors} vendors flagged as high risk
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {stats.pendingEvaluations > 0 && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-blue-600" />
                  <div>
                    <h3 className="font-semibold text-blue-900">Pending Evaluations</h3>
                    <p className="text-sm text-blue-700">
                      {stats.pendingEvaluations} evaluations awaiting completion
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Vendor Categories */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Vendor Categories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      dataKey="count"
                      data={getVendorsByCategory()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }) => `${category} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                    >
                      {getVendorsByCategory().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Contract Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Contract Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={getContractsByStatus()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="status" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Vendors */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Top Performing Vendors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getTopVendors().map((vendor, index) => (
                  <div key={vendor.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{vendor.name}</h4>
                        <p className="text-sm text-gray-600">{vendor.category}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{vendor.avgScore.toFixed(1)}%</div>
                      <Badge variant={vendor.qualificationStatus === 'qualified' ? 'default' : 'secondary'}>
                        {vendor.qualificationStatus}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={getPerformanceTrends()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="right" dataKey="contracts" fill="#82ca9d" name="Active Contracts" />
                  <Line yAxisId="left" type="monotone" dataKey="score" stroke="#8884d8" strokeWidth={3} name="Average Score %" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Excellent (90-100%)</span>
                      <span className="font-medium">25%</span>
                    </div>
                    <Progress value={25} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Good (80-89%)</span>
                      <span className="font-medium">45%</span>
                    </div>
                    <Progress value={45} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Average (70-79%)</span>
                      <span className="font-medium">20%</span>
                    </div>
                    <Progress value={20} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Below Average (&lt;70%)</span>
                      <span className="font-medium">10%</span>
                    </div>
                    <Progress value={10} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Key Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>On-time Delivery</span>
                    <span className="font-bold text-green-600">92%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Quality Score</span>
                    <span className="font-bold text-blue-600">88%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Cost Efficiency</span>
                    <span className="font-bold text-purple-600">85%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Communication</span>
                    <span className="font-bold text-orange-600">91%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Innovation</span>
                    <span className="font-bold text-teal-600">78%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="contracts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Contract Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{stats.activeContracts}</div>
                  <div className="text-sm text-gray-600">Active Contracts</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    ${(stats.totalContractValue / 1000000).toFixed(1)}M
                  </div>
                  <div className="text-sm text-gray-600">Total Value</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">{stats.contractsExpiringSoon}</div>
                  <div className="text-sm text-gray-600">Expiring Soon</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Upcoming Contract Expirations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contracts
                  .filter(contract => {
                    const endDate = new Date(contract.endDate);
                    const now = new Date();
                    const diffTime = endDate.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays <= 90 && diffDays > 0;
                  })
                  .sort((a, b) => new Date(a.endDate).getTime() - new Date(b.endDate).getTime())
                  .slice(0, 5)
                  .map((contract) => {
                    const vendor = vendors.find(v => v.id === contract.vendorId);
                    const endDate = new Date(contract.endDate);
                    const now = new Date();
                    const diffTime = endDate.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    return (
                      <div key={contract.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-medium">{vendor?.name}</h4>
                          <p className="text-sm text-gray-600">
                            {contract.title} • ${contract.value.toLocaleString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant={diffDays <= 30 ? 'destructive' : 'secondary'}>
                            {diffDays} days
                          </Badge>
                          <p className="text-sm text-gray-600">
                            {format(endDate, 'MMM d, yyyy')}
                          </p>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Contracts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Recent Contracts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getRecentActivity().recentContracts.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{activity.vendor}</p>
                        <p className="text-sm text-gray-600">
                          Contract started {format(new Date(activity.date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">
                          ${activity.value.toLocaleString()}
                        </div>
                        <Badge variant="default">{activity.status}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Evaluations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Recent Evaluations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getRecentActivity().recentEvaluations.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{activity.vendor}</p>
                        <p className="text-sm text-gray-600">
                          Evaluated {format(new Date(activity.date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-blue-600">{activity.score}%</div>
                        <Badge variant="default">{activity.status}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};