import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useUpdateReview, useReview } from '~/hooks/useReviews'
import { useVendor } from '~/hooks/useVendors'
import { useProposal } from '~/hooks/useProposals'
import { Button } from '~/lib/ui-components'
import { Input } from '~/lib/ui-components'
import { Textarea } from '~/lib/ui-components'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '~/components/ui/form'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { Card } from '~/lib/ui-components'
import { CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/lib/ui-components'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { useToast } from '~/hooks/use-toast'
import { Star, Plus, X, Save, Loader2, Building, FileText } from 'lucide-react'

const reviewSchema = z.object({
  rating: z.number().min(1, 'Rating is required').max(5),
  reviewerName: z.string().min(2, 'Reviewer name is required'),
  reviewerRole: z.string().min(2, 'Reviewer role is required'),
  comment: z.string().min(10, 'Comment must be at least 10 characters'),
  strengths: z.array(z.string()).default([]),
  improvements: z.array(z.string()).default([]),
  vendorResponse: z.string().optional(),
  internalNotes: z.string().optional(),
})

type ReviewFormData = z.infer<typeof reviewSchema>

interface ReviewEditFormProps {
  reviewId: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

// Review templates for quick filling
const REVIEW_TEMPLATES = {
  excellent: {
    rating: 5,
    comment:
      'Exceptional service delivery with outstanding results. The vendor exceeded all expectations and delivered high-quality work on time and within budget.',
    strengths: [
      'Excellent communication throughout the project',
      'High-quality deliverables',
      'Proactive problem-solving',
      'Strong domain expertise',
    ],
    improvements: [],
  },
  good: {
    rating: 4,
    comment:
      'Strong performance with minor areas for improvement. The vendor delivered quality work and met most objectives effectively.',
    strengths: ['Good communication', 'Quality deliverables', 'Met deadlines'],
    improvements: ['Could improve response time', 'Documentation could be more comprehensive'],
  },
  average: {
    rating: 3,
    comment:
      'Adequate performance with several areas needing improvement. The vendor met basic requirements but there is room for enhancement.',
    strengths: ['Met basic requirements', 'Professional conduct'],
    improvements: [
      'Communication needs improvement',
      'Quality control processes',
      'Better adherence to timelines',
    ],
  },
}

export function ReviewEditForm({ reviewId, open, onOpenChange }: ReviewEditFormProps) {
  const { toast } = useToast()
  const [hoveredRating, setHoveredRating] = useState(0)
  const [newStrength, setNewStrength] = useState('')
  const [newImprovement, setNewImprovement] = useState('')

  const { data: review, isLoading } = useReview(reviewId)
  const { data: vendor } = useVendor(review?.vendorId)
  const { data: proposal } = useProposal(review?.proposalId)
  const updateReview = useUpdateReview()

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      rating: 0,
      reviewerName: '',
      reviewerRole: '',
      comment: '',
      strengths: [],
      improvements: [],
      vendorResponse: '',
      internalNotes: '',
    },
  })

  // Load review data when available
  useEffect(() => {
    if (review) {
      form.reset({
        rating: review.rating,
        reviewerName: review.reviewerName,
        reviewerRole: review.reviewerRole,
        comment: review.comment,
        strengths: review.strengths || [],
        improvements: review.improvements || [],
        vendorResponse: '',
        internalNotes: '',
      })
    }
  }, [review, form])

  const onSubmit = async (data: ReviewFormData) => {
    try {
      await updateReview.mutateAsync({
        id: reviewId,
        updates: {
          ...data,
          rating: data.rating as 1 | 2 | 3 | 4 | 5,
        },
      })

      toast({
        title: 'Review updated',
        description: 'Review has been successfully updated',
      })

      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update review. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const addStrength = () => {
    if (newStrength.trim()) {
      const current = form.getValues('strengths')
      form.setValue('strengths', [...current, newStrength.trim()])
      setNewStrength('')
    }
  }

  const removeStrength = (index: number) => {
    const current = form.getValues('strengths')
    form.setValue(
      'strengths',
      current.filter((_, i) => i !== index)
    )
  }

  const addImprovement = () => {
    if (newImprovement.trim()) {
      const current = form.getValues('improvements')
      form.setValue('improvements', [...current, newImprovement.trim()])
      setNewImprovement('')
    }
  }

  const removeImprovement = (index: number) => {
    const current = form.getValues('improvements')
    form.setValue(
      'improvements',
      current.filter((_, i) => i !== index)
    )
  }

  const applyTemplate = (templateKey: keyof typeof REVIEW_TEMPLATES) => {
    const template = REVIEW_TEMPLATES[templateKey]
    form.setValue('rating', template.rating)
    form.setValue('comment', template.comment)
    form.setValue('strengths', template.strengths)
    form.setValue('improvements', template.improvements)

    toast({
      title: 'Template applied',
      description: 'Review template has been applied. You can customize it further.',
    })
  }

  const selectedRating = form.watch('rating')

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl">
          <div className="flex items-center justify-center p-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Review</DialogTitle>
          <DialogDescription>Update review information and feedback</DialogDescription>
        </DialogHeader>

        {/* Vendor and Proposal Info */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          {vendor && (
            <Card>
              <CardHeader className="p-4">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Vendor
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <p className="font-medium">{vendor.companyName}</p>
                <p className="text-sm text-muted-foreground">{vendor.category}</p>
              </CardContent>
            </Card>
          )}

          {proposal && (
            <Card>
              <CardHeader className="p-4">
                <CardTitle className="text-sm flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Proposal
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <p className="font-medium text-sm">{proposal.title}</p>
                <p className="text-sm text-muted-foreground">${proposal.totalCost.toFixed(2)}</p>
              </CardContent>
            </Card>
          )}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="review" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="review">Review Details</TabsTrigger>
                <TabsTrigger value="feedback">Strengths & Improvements</TabsTrigger>
                <TabsTrigger value="notes">Additional Notes</TabsTrigger>
              </TabsList>

              <TabsContent value="review" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Review Information</CardTitle>
                    <CardDescription>Core review details and rating</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Quick Templates */}
                    <div>
                      <FormLabel>Quick Templates</FormLabel>
                      <div className="flex gap-2 mt-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyTemplate('excellent')}
                        >
                          Excellent (5★)
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyTemplate('good')}
                        >
                          Good (4★)
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => applyTemplate('average')}
                        >
                          Average (3★)
                        </Button>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="rating"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Rating</FormLabel>
                          <FormControl>
                            <div className="flex gap-1">
                              {[1, 2, 3, 4, 5].map((rating) => (
                                <button
                                  key={rating}
                                  type="button"
                                  onClick={() => field.onChange(rating)}
                                  onMouseEnter={() => setHoveredRating(rating)}
                                  onMouseLeave={() => setHoveredRating(0)}
                                  className="p-1 transition-transform hover:scale-110"
                                >
                                  <Star
                                    className={`h-10 w-10 transition-colors ${
                                      rating <= (hoveredRating || selectedRating)
                                        ? 'fill-yellow-400 text-yellow-400'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                </button>
                              ))}
                            </div>
                          </FormControl>
                          <FormDescription>
                            {selectedRating === 5 && 'Outstanding performance'}
                            {selectedRating === 4 && 'Good performance'}
                            {selectedRating === 3 && 'Average performance'}
                            {selectedRating === 2 && 'Below average performance'}
                            {selectedRating === 1 && 'Poor performance'}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="reviewerName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Reviewer Name</FormLabel>
                            <FormControl>
                              <Input placeholder="John Doe" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="reviewerRole"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Reviewer Role</FormLabel>
                            <FormControl>
                              <Input placeholder="L&D Manager" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="comment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Review Comment</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Share your detailed feedback about working with this vendor..."
                              className="min-h-[150px] resize-y"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Provide a comprehensive review of your experience
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="feedback" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Strengths & Improvements</CardTitle>
                    <CardDescription>
                      Highlight what worked well and areas for improvement
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <FormLabel>Strengths</FormLabel>
                        <Badge variant="default">{form.watch('strengths').length} items</Badge>
                      </div>
                      <div className="flex gap-2 mb-3">
                        <Input
                          placeholder="Add a strength..."
                          value={newStrength}
                          onChange={(e) => setNewStrength(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault()
                              addStrength()
                            }
                          }}
                        />
                        <Button type="button" onClick={addStrength} disabled={!newStrength.trim()}>
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {form.watch('strengths').map((strength, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950/20 rounded-md"
                          >
                            <span className="flex-1 text-sm">{strength}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeStrength(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        {form.watch('strengths').length === 0 && (
                          <p className="text-sm text-muted-foreground text-center py-4">
                            No strengths added yet
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <FormLabel>Areas for Improvement</FormLabel>
                        <Badge variant="secondary">{form.watch('improvements').length} items</Badge>
                      </div>
                      <div className="flex gap-2 mb-3">
                        <Input
                          placeholder="Add an area for improvement..."
                          value={newImprovement}
                          onChange={(e) => setNewImprovement(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault()
                              addImprovement()
                            }
                          }}
                        />
                        <Button
                          type="button"
                          onClick={addImprovement}
                          disabled={!newImprovement.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {form.watch('improvements').map((improvement, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-2 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-md"
                          >
                            <span className="flex-1 text-sm">{improvement}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeImprovement(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        {form.watch('improvements').length === 0 && (
                          <p className="text-sm text-muted-foreground text-center py-4">
                            No improvements added yet
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notes" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Information</CardTitle>
                    <CardDescription>Vendor responses and internal notes</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="vendorResponse"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vendor Response (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Record any response or feedback from the vendor..."
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Document vendor's response to this review
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="internalNotes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Internal Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Add any internal notes or follow-up actions..."
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>These notes are for internal use only</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={updateReview.isPending}>
                {updateReview.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
