import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useCreateReview } from '~/hooks/useReviews'
import { Button } from '~/lib/ui-components'
import { Input } from '~/lib/ui-components'
import { Textarea } from '~/lib/ui-components'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Star, Plus, X } from 'lucide-react'

const reviewSchema = z.object({
  rating: z.number().min(1, 'Rating is required').max(5),
  reviewerName: z.string().min(2, 'Reviewer name is required'),
  reviewerRole: z.string().min(2, 'Reviewer role is required'),
  comment: z.string().min(10, 'Comment must be at least 10 characters'),
  strengths: z.array(z.string()).default([]),
  improvements: z.array(z.string()).default([]),
})

type ReviewFormData = z.infer<typeof reviewSchema>

interface ReviewFormProps {
  vendorId: string
  proposalId?: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ReviewForm({ vendorId, proposalId, open, onOpenChange }: ReviewFormProps) {
  const [hoveredRating, setHoveredRating] = useState(0)
  const createReview = useCreateReview()

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      rating: 0,
      reviewerName: '',
      reviewerRole: '',
      comment: '',
      strengths: [],
      improvements: [],
    },
  })

  const onSubmit = async (data: ReviewFormData) => {
    try {
      await createReview.mutateAsync({
        vendorId,
        proposalId,
        ...data,
        rating: data.rating as 1 | 2 | 3 | 4 | 5,
        createdAt: new Date(),
      })

      form.reset()
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to create review:', error)
    }
  }

  const addStrength = () => {
    const strength = prompt('Enter a strength:')
    if (strength) {
      const current = form.getValues('strengths')
      form.setValue('strengths', [...current, strength])
    }
  }

  const removeStrength = (index: number) => {
    const current = form.getValues('strengths')
    form.setValue(
      'strengths',
      current.filter((_, i) => i !== index)
    )
  }

  const addImprovement = () => {
    const improvement = prompt('Enter an area for improvement:')
    if (improvement) {
      const current = form.getValues('improvements')
      form.setValue('improvements', [...current, improvement])
    }
  }

  const removeImprovement = (index: number) => {
    const current = form.getValues('improvements')
    form.setValue(
      'improvements',
      current.filter((_, i) => i !== index)
    )
  }

  const selectedRating = form.watch('rating')

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add Review</DialogTitle>
          <DialogDescription>
            Share your experience and feedback about this vendor
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="rating"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rating</FormLabel>
                  <FormControl>
                    <div className="flex gap-1">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => field.onChange(rating)}
                          onMouseEnter={() => setHoveredRating(rating)}
                          onMouseLeave={() => setHoveredRating(0)}
                          className="p-1"
                        >
                          <Star
                            className={`h-8 w-8 transition-colors ${
                              rating <= (hoveredRating || selectedRating)
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        </button>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reviewerName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Your Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reviewerRole"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Your Role</FormLabel>
                    <FormControl>
                      <Input placeholder="L&D Manager" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Review Comment</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Share your detailed feedback about working with this vendor..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <div className="flex items-center justify-between mb-2">
                <FormLabel>Strengths</FormLabel>
                <Button type="button" variant="outline" size="sm" onClick={addStrength}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Strength
                </Button>
              </div>
              <div className="space-y-2">
                {form.watch('strengths').map((strength, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                    <span className="flex-1 text-sm">{strength}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeStrength(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {form.watch('strengths').length === 0 && (
                  <p className="text-sm text-muted-foreground">No strengths added</p>
                )}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <FormLabel>Areas for Improvement</FormLabel>
                <Button type="button" variant="outline" size="sm" onClick={addImprovement}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Improvement
                </Button>
              </div>
              <div className="space-y-2">
                {form.watch('improvements').map((improvement, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                    <span className="flex-1 text-sm">{improvement}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeImprovement(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {form.watch('improvements').length === 0 && (
                  <p className="text-sm text-muted-foreground">No improvements added</p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={createReview.isPending}>
                {createReview.isPending ? 'Submitting...' : 'Submit Review'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
