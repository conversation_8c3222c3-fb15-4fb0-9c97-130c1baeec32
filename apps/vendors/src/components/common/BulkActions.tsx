import { useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '~/lib/ui-components'
import { Checkbox } from '~/lib/ui-components'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Progress } from '~/lib/ui-components'
import { useToast } from '~/hooks/use-toast'
import {
  Trash2,
  Download,
  Upload,
  Edit,
  AlertTriangle,
  CheckCircle,
  Loader2,
  FileSpreadsheet,
  FileText,
  Archive,
} from 'lucide-react'
import type { VendorStatus, ProposalStatus } from '~/types/vendor-management'

interface BulkActionsProps<T> {
  selectedItems: T[]
  onSelectionChange: (items: T[]) => void
  type: 'vendors' | 'proposals' | 'reviews'
  onDelete?: (ids: string[]) => Promise<void>
  onStatusUpdate?: (ids: string[], status: VendorStatus | ProposalStatus) => Promise<void>
  onExport?: (ids: string[], format: 'csv' | 'json' | 'pdf') => Promise<void>
  onArchive?: (ids: string[]) => Promise<void>
}

interface ProgressState {
  current: number
  total: number
  message: string
}

export function BulkActions<T extends { id: string }>({
  selectedItems,
  onSelectionChange,
  type,
  onDelete,
  onStatusUpdate,
  onExport,
  onArchive,
}: BulkActionsProps<T>) {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showStatusDialog, setShowStatusDialog] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState<ProgressState | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'json' | 'pdf'>('csv')

  const selectedIds = selectedItems.map((item) => item.id)
  const hasSelection = selectedItems.length > 0

  const handleBulkDelete = async () => {
    if (!onDelete) return

    setIsProcessing(true)
    setProgress({ current: 0, total: selectedIds.length, message: 'Deleting items...' })

    try {
      // Simulate progress for better UX
      for (let i = 0; i < selectedIds.length; i++) {
        setProgress({
          current: i + 1,
          total: selectedIds.length,
          message: `Deleting item ${i + 1} of ${selectedIds.length}...`,
        })

        // In a real implementation, you might delete items one by one
        // or in batches with progress updates
        await new Promise((resolve) => setTimeout(resolve, 100))
      }

      await onDelete(selectedIds)

      toast({
        title: 'Items deleted',
        description: `Successfully deleted ${selectedIds.length} ${type}`,
      })

      // Clear selection and close dialog
      onSelectionChange([])
      setShowDeleteDialog(false)

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: [type] })
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to delete ${type}. Please try again.`,
        variant: 'destructive',
      })
    } finally {
      setIsProcessing(false)
      setProgress(null)
    }
  }

  const handleBulkStatusUpdate = async () => {
    if (!onStatusUpdate || !selectedStatus) return

    setIsProcessing(true)
    setProgress({ current: 0, total: selectedIds.length, message: 'Updating status...' })

    try {
      // Simulate progress
      for (let i = 0; i < selectedIds.length; i++) {
        setProgress({
          current: i + 1,
          total: selectedIds.length,
          message: `Updating item ${i + 1} of ${selectedIds.length}...`,
        })
        await new Promise((resolve) => setTimeout(resolve, 100))
      }

      await onStatusUpdate(selectedIds, selectedStatus as any)

      toast({
        title: 'Status updated',
        description: `Successfully updated status for ${selectedIds.length} ${type}`,
      })

      onSelectionChange([])
      setShowStatusDialog(false)

      queryClient.invalidateQueries({ queryKey: [type] })
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to update status. Please try again.`,
        variant: 'destructive',
      })
    } finally {
      setIsProcessing(false)
      setProgress(null)
      setSelectedStatus('')
    }
  }

  const handleBulkExport = async () => {
    if (!onExport) return

    setIsProcessing(true)
    setProgress({
      current: 0,
      total: 1,
      message: `Preparing ${selectedFormat.toUpperCase()} export...`,
    })

    try {
      await onExport(selectedIds, selectedFormat)

      toast({
        title: 'Export completed',
        description: `Successfully exported ${selectedIds.length} ${type} as ${selectedFormat.toUpperCase()}`,
      })

      setShowExportDialog(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to export ${type}. Please try again.`,
        variant: 'destructive',
      })
    } finally {
      setIsProcessing(false)
      setProgress(null)
    }
  }

  const handleBulkArchive = async () => {
    if (!onArchive) return

    setIsProcessing(true)
    setProgress({ current: 0, total: selectedIds.length, message: 'Archiving items...' })

    try {
      for (let i = 0; i < selectedIds.length; i++) {
        setProgress({
          current: i + 1,
          total: selectedIds.length,
          message: `Archiving item ${i + 1} of ${selectedIds.length}...`,
        })
        await new Promise((resolve) => setTimeout(resolve, 100))
      }

      await onArchive(selectedIds)

      toast({
        title: 'Items archived',
        description: `Successfully archived ${selectedIds.length} ${type}`,
      })

      onSelectionChange([])
      queryClient.invalidateQueries({ queryKey: [type] })
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to archive ${type}. Please try again.`,
        variant: 'destructive',
      })
    } finally {
      setIsProcessing(false)
      setProgress(null)
    }
  }

  const getStatusOptions = () => {
    if (type === 'vendors') {
      return ['active', 'inactive', 'pending'] as VendorStatus[]
    } else if (type === 'proposals') {
      return ['draft', 'submitted', 'negotiation', 'approved', 'rejected'] as ProposalStatus[]
    }
    return []
  }

  const getExportIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <FileSpreadsheet className="h-4 w-4" />
      case 'pdf':
        return <FileText className="h-4 w-4" />
      default:
        return <Download className="h-4 w-4" />
    }
  }

  return (
    <>
      <div className="flex items-center gap-2 p-4 bg-muted/50 rounded-lg">
        <span className="text-sm font-medium">
          {selectedItems.length} {type} selected
        </span>

        <div className="flex gap-2 ml-auto">
          {onStatusUpdate && type !== 'reviews' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowStatusDialog(true)}
              disabled={!hasSelection}
            >
              <Edit className="mr-2 h-4 w-4" />
              Update Status
            </Button>
          )}

          {onExport && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExportDialog(true)}
              disabled={!hasSelection}
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          )}

          {onArchive && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkArchive}
              disabled={!hasSelection}
            >
              <Archive className="mr-2 h-4 w-4" />
              Archive
            </Button>
          )}

          {onDelete && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setShowDeleteDialog(true)}
              disabled={!hasSelection}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSelectionChange([])}
            disabled={!hasSelection}
          >
            Clear Selection
          </Button>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Confirm Bulk Delete
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedIds.length} {type}? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>

          {progress && (
            <div className="space-y-2">
              <Progress value={(progress.current / progress.total) * 100} />
              <p className="text-sm text-muted-foreground">{progress.message}</p>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleBulkDelete} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete {selectedIds.length} Items
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Status</DialogTitle>
            <DialogDescription>
              Select a new status for {selectedIds.length} {type}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select a status" />
              </SelectTrigger>
              <SelectContent>
                {getStatusOptions().map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {progress && (
              <div className="space-y-2">
                <Progress value={(progress.current / progress.total) * 100} />
                <p className="text-sm text-muted-foreground">{progress.message}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowStatusDialog(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button onClick={handleBulkStatusUpdate} disabled={isProcessing || !selectedStatus}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Update Status
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Data</DialogTitle>
            <DialogDescription>
              Choose export format for {selectedIds.length} {type}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              {(['csv', 'json', 'pdf'] as const).map((format) => (
                <button
                  key={format}
                  onClick={() => setSelectedFormat(format)}
                  className={`p-4 rounded-lg border-2 transition-colors ${
                    selectedFormat === format
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                >
                  <div className="flex flex-col items-center gap-2">
                    {getExportIcon(format)}
                    <span className="text-sm font-medium">{format.toUpperCase()}</span>
                  </div>
                </button>
              ))}
            </div>

            {progress && (
              <div className="space-y-2">
                <Progress value={(progress.current / progress.total) * 100} />
                <p className="text-sm text-muted-foreground">{progress.message}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button onClick={handleBulkExport} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export as {selectedFormat.toUpperCase()}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
