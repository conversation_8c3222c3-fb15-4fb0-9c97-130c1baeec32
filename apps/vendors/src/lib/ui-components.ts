/**
 * UI Components Import Aliases
 * 
 * This file provides backward-compatible imports from @luminar/shared-ui
 * to maintain existing component usage patterns while migrating to centralized components.
 */

// Actions
export { Button } from '@luminar/shared-ui/actions'
export { LuminarButton as AdvancedButton } from '@luminar/shared-ui/actions'
export { LuminarCommand as Command } from '@luminar/shared-ui/actions'
export { LuminarDropdown as Dropdown } from '@luminar/shared-ui/actions'

// Display Components
export { 
  LuminarCard as Card,
  // Re-export card sub-components if available in shared-ui
} from '@luminar/shared-ui/display'

export { LuminarBadge as Badge } from '@luminar/shared-ui/display'
export { LuminarAvatar as Avatar } from '@luminar/shared-ui/display'
export { LuminarTable as Table } from '@luminar/shared-ui/display'
export { LuminarDataTable as DataTable } from '@luminar/shared-ui/display'
export { Skeleton, LuminarSkeleton as LoadingSkeleton } from '@luminar/shared-ui/display'
export { ProgressBar as Progress } from '@luminar/shared-ui/display'
export { LuminarMetricCard as MetricCard } from '@luminar/shared-ui/display'

// Form Components  
export { LuminarInput as Input } from '@luminar/shared-ui/forms'
export { LuminarLabel as Label } from '@luminar/shared-ui/forms'
export { LuminarSelect as Select } from '@luminar/shared-ui/forms'
export { LuminarTextarea as Textarea } from '@luminar/shared-ui/forms'
export { LuminarCheckbox as Checkbox } from '@luminar/shared-ui/forms'
export { LuminarRadioGroup as RadioGroup } from '@luminar/shared-ui/forms'
export { LuminarSwitch as Switch } from '@luminar/shared-ui/forms'
export { LuminarForm as Form } from '@luminar/shared-ui/forms'
export { LuminarFileUpload as FileUpload } from '@luminar/shared-ui/forms'
export { LuminarDatePicker as DatePicker } from '@luminar/shared-ui/forms'

// Chart Components
export { LuminarBarChart as BarChart } from '@luminar/shared-ui/charts'
export { LuminarLineChart as LineChart } from '@luminar/shared-ui/charts'
export { LuminarPieChart as PieChart } from '@luminar/shared-ui/charts'
export { LuminarDonutChart as DonutChart } from '@luminar/shared-ui/charts'
export { LuminarAreaChart as AreaChart } from '@luminar/shared-ui/charts'

// Feedback Components (if available)
// export { Dialog, DialogContent, DialogHeader, DialogTitle } from '@luminar/shared-ui/feedback'

// Layout Components (if available)
// export { Tabs, TabsContent, TabsList, TabsTrigger } from '@luminar/shared-ui/layout'

// Auth Components
export { ProtectedRoute } from '@luminar/shared-auth/components'

// Re-export types
export type * from '@luminar/shared-ui/display'
export type * from '@luminar/shared-ui/actions'
export type * from '@luminar/shared-ui/forms'
export type * from '@luminar/shared-ui/charts'