import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useVendors } from '~/hooks/useVendors'
import { useCreateProposal } from '~/hooks/useProposals'
import { Button } from '~/lib/ui-components'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Input } from '~/lib/ui-components'
import { Textarea } from '~/lib/ui-components'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { ArrowLeft, Plus, X, DollarSign } from 'lucide-react'
import { addDays, format } from 'date-fns'

const proposalSchema = z.object({
  vendorId: z.string().min(1, 'Vendor is required'),
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  costs: z
    .array(
      z.object({
        item: z.string().min(1, 'Item description is required'),
        amount: z.number().min(0, 'Amount must be positive'),
        currency: z.string().default('USD'),
      })
    )
    .min(1, 'At least one cost item is required'),
  validUntil: z.string().min(1, 'Valid until date is required'),
  status: z.enum(['draft', 'submitted']),
})

type ProposalFormData = z.infer<typeof proposalSchema>

export const Route = createFileRoute('/proposals/new')({
  component: NewProposalPage,
})

function NewProposalPage() {
  const navigate = useNavigate()
  const { data: vendors, isLoading: vendorsLoading } = useVendors({ status: 'active' })
  const createProposal = useCreateProposal()

  const form = useForm<ProposalFormData>({
    resolver: zodResolver(proposalSchema),
    defaultValues: {
      vendorId: '',
      title: '',
      description: '',
      costs: [{ item: '', amount: 0, currency: 'USD' }],
      validUntil: format(addDays(new Date(), 30), 'yyyy-MM-dd'),
      status: 'draft',
    },
  })

  const onSubmit = async (data: ProposalFormData) => {
    try {
      const totalCost = data.costs.reduce((sum, cost) => sum + cost.amount, 0)

      await createProposal.mutateAsync({
        ...data,
        totalCost,
        attachments: [],
        validUntil: new Date(data.validUntil),
        submittedAt: data.status === 'submitted' ? new Date() : undefined,
      })

      navigate({ to: '/proposals' })
    } catch (error) {
      console.error('Failed to create proposal:', error)
    }
  }

  const addCostItem = () => {
    const currentCosts = form.getValues('costs')
    form.setValue('costs', [...currentCosts, { item: '', amount: 0, currency: 'USD' }])
  }

  const removeCostItem = (index: number) => {
    const currentCosts = form.getValues('costs')
    if (currentCosts.length > 1) {
      form.setValue(
        'costs',
        currentCosts.filter((_, i) => i !== index)
      )
    }
  }

  const calculateTotal = () => {
    const costs = form.watch('costs')
    return costs.reduce((sum, cost) => sum + (cost.amount || 0), 0)
  }

  return (
    <div className="container mx-auto p-6 max-w-3xl">
      <div className="flex items-center gap-4 mb-6">
        <Link to="/proposals">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Proposals
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Create New Proposal</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Proposal Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="vendorId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vendor</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={vendorsLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={vendorsLoading ? 'Loading vendors...' : 'Select a vendor'}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {vendors?.map((vendor) => (
                          <SelectItem key={vendor.id} value={vendor.id}>
                            {vendor.companyName} ({vendor.category})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proposal Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter proposal title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the proposal details, scope, and deliverables"
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <div className="flex items-center justify-between mb-4">
                  <FormLabel>Cost Breakdown</FormLabel>
                  <Button type="button" variant="outline" size="sm" onClick={addCostItem}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Item
                  </Button>
                </div>
                <div className="space-y-3">
                  {form.watch('costs').map((_, index) => (
                    <div key={index} className="flex gap-2">
                      <FormField
                        control={form.control}
                        name={`costs.${index}.item`}
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <FormControl>
                              <Input placeholder="Cost item description" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`costs.${index}.amount`}
                        render={({ field }) => (
                          <FormItem className="w-32">
                            <FormControl>
                              <div className="relative">
                                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                <Input
                                  type="number"
                                  placeholder="0.00"
                                  className="pl-8"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      {form.watch('costs').length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeCostItem(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-muted rounded-md">
                  <div className="flex justify-between items-center font-semibold">
                    <span>Total Cost:</span>
                    <span className="text-lg">${calculateTotal().toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <FormField
                control={form.control}
                name="validUntil"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valid Until</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormDescription>
                      The date until which this proposal remains valid
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Status</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="draft">Save as Draft</SelectItem>
                        <SelectItem value="submitted">Submit Immediately</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose whether to save as draft or submit immediately
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2">
                <Button type="submit" disabled={createProposal.isPending}>
                  {createProposal.isPending ? 'Creating...' : 'Create Proposal'}
                </Button>
                <Link to="/proposals">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
