import { createFileRoute, <PERSON>, useNavigate } from '@tanstack/react-router'
import { useProposal, useUpdateProposalStatus } from '~/hooks/useProposals'
import { useProposalReviews } from '~/hooks/useReviews'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/lib/ui-components'
import { Button } from '~/lib/ui-components'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import {
  ArrowLeft,
  Building2,
  Calendar,
  DollarSign,
  FileText,
  Star,
  CheckCircle,
  XCircle,
  Clock,
  Download,
} from 'lucide-react'
import { format } from 'date-fns'
import { ReviewForm } from '~/components/reviews/ReviewForm'
import { useState } from 'react'
import type { ProposalStatus } from '~/types/vendor-management'

export const Route = createFileRoute('/proposals/$proposalId')({
  component: ProposalDetailsPage,
})

function ProposalDetailsPage() {
  const { proposalId } = Route.useParams()
  const navigate = useNavigate()
  const [showReviewForm, setShowReviewForm] = useState(false)
  const { data: proposal, isLoading: proposalLoading } = useProposal(proposalId)
  const { data: reviews, isLoading: reviewsLoading } = useProposalReviews(proposalId)
  const updateStatus = useUpdateProposalStatus()

  if (proposalLoading) {
    return (
      <div className="container mx-auto p-6">
        <p className="text-muted-foreground">Loading proposal details...</p>
      </div>
    )
  }

  if (!proposal) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-destructive">Proposal not found</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getStatusBadgeVariant = (status: ProposalStatus) => {
    const variants = {
      draft: 'secondary',
      submitted: 'outline',
      negotiation: 'outline',
      approved: 'default',
      rejected: 'destructive',
    }
    return variants[status] as any
  }

  const getStatusIcon = (status: ProposalStatus) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />
      case 'rejected':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const handleStatusChange = async (newStatus: ProposalStatus) => {
    try {
      await updateStatus.mutateAsync({ id: proposalId, status: newStatus })
    } catch (error) {
      console.error('Failed to update status:', error)
    }
  }

  const canChangeStatus = () => {
    if (proposal.status === 'approved' || proposal.status === 'rejected') {
      return false
    }
    return true
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/proposals">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Proposals
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">{proposal.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(proposal.status)}
          <Badge variant={getStatusBadgeVariant(proposal.status)}>{proposal.status}</Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Proposal Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-1">Description</p>
                <p>{proposal.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Vendor</p>
                  <Link
                    to="/vendors/$vendorId"
                    params={{ vendorId: proposal.vendorId }}
                    className="flex items-center gap-2 hover:underline"
                  >
                    <Building2 className="h-4 w-4" />
                    {proposal.vendor?.companyName}
                  </Link>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Total Cost</p>
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    <span className="font-semibold text-lg">
                      {proposal.totalCost.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Valid Until</p>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{format(new Date(proposal.validUntil), 'MMMM d, yyyy')}</span>
                  </div>
                </div>
                {proposal.submittedAt && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Submitted On</p>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{format(new Date(proposal.submittedAt), 'MMMM d, yyyy')}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cost Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {proposal.costs.map((cost, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center py-2 border-b last:border-0"
                  >
                    <span>{cost.item}</span>
                    <span className="font-medium">
                      ${cost.amount.toLocaleString()} {cost.currency}
                    </span>
                  </div>
                ))}
                <div className="flex justify-between items-center pt-3 border-t font-semibold">
                  <span>Total</span>
                  <span className="text-lg">${proposal.totalCost.toLocaleString()} USD</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {proposal.attachments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Attachments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {proposal.attachments.map((attachment, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 rounded-md hover:bg-muted"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{attachment}</span>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              {reviewsLoading ? (
                <p className="text-muted-foreground">Loading reviews...</p>
              ) : reviews && reviews.length > 0 ? (
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="border-b pb-4 last:border-0">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <p className="font-medium">{review.reviewerName}</p>
                          <p className="text-sm text-muted-foreground">{review.reviewerRole}</p>
                        </div>
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-sm">{review.comment}</p>
                      <p className="text-xs text-muted-foreground mt-2">
                        {format(new Date(review.createdAt), 'MMM d, yyyy')}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No reviews yet</p>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {canChangeStatus() && (
                <>
                  {proposal.status === 'draft' && (
                    <Button
                      className="w-full"
                      onClick={() => handleStatusChange('submitted')}
                      disabled={updateStatus.isPending}
                    >
                      Submit Proposal
                    </Button>
                  )}
                  {proposal.status === 'submitted' && (
                    <>
                      <Button
                        className="w-full"
                        onClick={() => handleStatusChange('negotiation')}
                        disabled={updateStatus.isPending}
                      >
                        Start Negotiation
                      </Button>
                      <Button
                        className="w-full"
                        variant="default"
                        onClick={() => handleStatusChange('approved')}
                        disabled={updateStatus.isPending}
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Approve
                      </Button>
                      <Button
                        className="w-full"
                        variant="destructive"
                        onClick={() => handleStatusChange('rejected')}
                        disabled={updateStatus.isPending}
                      >
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject
                      </Button>
                    </>
                  )}
                  {proposal.status === 'negotiation' && (
                    <>
                      <Button
                        className="w-full"
                        variant="default"
                        onClick={() => handleStatusChange('approved')}
                        disabled={updateStatus.isPending}
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Approve
                      </Button>
                      <Button
                        className="w-full"
                        variant="destructive"
                        onClick={() => handleStatusChange('rejected')}
                        disabled={updateStatus.isPending}
                      >
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject
                      </Button>
                    </>
                  )}
                </>
              )}
              <Button className="w-full" variant="outline" onClick={() => setShowReviewForm(true)}>
                <Star className="mr-2 h-4 w-4" />
                Add Review
              </Button>
              <Button className="w-full" variant="outline">
                Edit Proposal
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-3">
                  <div className="flex flex-col items-center">
                    <div className="w-2 h-2 bg-primary rounded-full" />
                    <div className="w-0.5 h-full bg-muted" />
                  </div>
                  <div className="pb-4">
                    <p className="text-sm font-medium">Created</p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(proposal.createdAt), 'MMM d, yyyy h:mm a')}
                    </p>
                  </div>
                </div>
                {proposal.submittedAt && (
                  <div className="flex gap-3">
                    <div className="flex flex-col items-center">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      <div className="w-0.5 h-full bg-muted" />
                    </div>
                    <div className="pb-4">
                      <p className="text-sm font-medium">Submitted</p>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(proposal.submittedAt), 'MMM d, yyyy h:mm a')}
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex gap-3">
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-2 h-2 rounded-full ${proposal.status === 'approved' || proposal.status === 'rejected' ? 'bg-primary' : 'bg-muted'}`}
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Current Status</p>
                    <p className="text-xs text-muted-foreground capitalize">{proposal.status}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {proposal && (
        <ReviewForm
          vendorId={proposal.vendorId}
          proposalId={proposalId}
          open={showReviewForm}
          onOpenChange={setShowReviewForm}
        />
      )}
    </div>
  )
}
