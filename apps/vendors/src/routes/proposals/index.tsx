import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { useProposals, useDeleteProposal, useUpdateProposal } from '~/hooks/useProposals'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/lib/ui-components'
import { Button } from '~/lib/ui-components'
import {
  Plus,
  DollarSign,
  Calendar,
  Building2,
  Eye,
  Edit,
  Trash2,
  FileUp,
  Download,
} from 'lucide-react'
import { format } from 'date-fns'
import { DataTable } from '~/components/ui/DataTable'
import { AdvancedSearch } from '~/components/ui/AdvancedSearch'
import { BulkActions } from '~/components/common/BulkActions'
import { LoadingSkeleton } from '~/components/ui/LoadingSkeleton'
import { Breadcrumb } from '~/components/ui/Breadcrumb'
import { FileUpload } from '~/components/ui/FileUpload'
import { ProposalEditForm } from '~/components/proposals/ProposalEditForm'
import { ProgressIndicator } from '~/components/ui/ProgressIndicator'
import { useToast } from '~/hooks/use-toast'
import type { ProposalStatus, Proposal } from '~/types/vendor-management'
import type { ColumnDef } from '@tanstack/react-table'

export const Route = createFileRoute('/proposals/')({
  component: ProposalsPage,
})

function ProposalsPage() {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())
  const [showEditForm, setShowEditForm] = useState(false)
  const [editingProposal, setEditingProposal] = useState<Proposal | null>(null)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [filters, setFilters] = useState({
    search: '',
    status: 'all' as ProposalStatus | 'all',
    minCost: undefined as number | undefined,
    maxCost: undefined as number | undefined,
    dateRange: undefined as [Date, Date] | undefined,
  })

  const { toast } = useToast()
  const {
    data: proposals,
    isLoading,
    error,
  } = useProposals({
    search: filters.search,
    status: filters.status === 'all' ? undefined : filters.status,
    minCost: filters.minCost,
    maxCost: filters.maxCost,
  })
  const deleteProposal = useDeleteProposal()
  const updateProposal = useUpdateProposal()

  const getStatusBadgeVariant = (status: ProposalStatus) => {
    const variants = {
      draft: 'secondary',
      submitted: 'outline',
      negotiation: 'outline',
      approved: 'default',
      rejected: 'destructive',
    }
    return variants[status] as any
  }

  const getStatusColor = (status: ProposalStatus) => {
    const colors = {
      draft: 'text-gray-600',
      submitted: 'text-blue-600',
      negotiation: 'text-yellow-600',
      approved: 'text-green-600',
      rejected: 'text-red-600',
    }
    return colors[status]
  }

  const handleBulkAction = async (action: string) => {
    try {
      switch (action) {
        case 'delete':
          for (const id of selectedRows) {
            await deleteProposal.mutateAsync(id)
          }
          toast({
            title: 'Success',
            description: `Deleted ${selectedRows.size} proposals`,
          })
          setSelectedRows(new Set())
          break
        case 'export':
          toast({
            title: 'Export Started',
            description: `Exporting ${selectedRows.size} proposals...`,
          })
          break
        case 'approve':
          for (const id of selectedRows) {
            await updateProposal.mutateAsync({ id, updates: { status: 'approved' } })
          }
          toast({
            title: 'Success',
            description: `Approved ${selectedRows.size} proposals`,
          })
          setSelectedRows(new Set())
          break
        case 'reject':
          for (const id of selectedRows) {
            await updateProposal.mutateAsync({ id, updates: { status: 'rejected' } })
          }
          toast({
            title: 'Success',
            description: `Rejected ${selectedRows.size} proposals`,
          })
          setSelectedRows(new Set())
          break
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to complete bulk action',
        variant: 'destructive',
      })
    }
  }

  const handleFileUpload = (files: File[]) => {
    toast({
      title: 'Files Uploaded',
      description: `${files.length} file(s) uploaded successfully`,
    })
    setShowUploadDialog(false)
  }

  const columns: ColumnDef<Proposal>[] = [
    {
      accessorKey: 'title',
      header: 'Proposal',
      cell: ({ row }) => {
        const proposal = row.original
        return (
          <div>
            <p className="font-medium">{proposal.title}</p>
            <p className="text-sm text-muted-foreground line-clamp-1">{proposal.description}</p>
          </div>
        )
      },
    },
    {
      accessorKey: 'vendor.companyName',
      header: 'Vendor',
      cell: ({ row }) => {
        const proposal = row.original
        return (
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <Link
              to="/vendors/$vendorId"
              params={{ vendorId: proposal.vendorId }}
              className="hover:underline"
              onClick={(e) => e.stopPropagation()}
            >
              {proposal.vendor?.companyName}
            </Link>
          </div>
        )
      },
    },
    {
      accessorKey: 'totalCost',
      header: 'Total Cost',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.original.totalCost.toLocaleString()}</span>
        </div>
      ),
    },
    {
      accessorKey: 'validUntil',
      header: 'Valid Until',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {format(new Date(row.original.validUntil), 'MMM d, yyyy')}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge
          variant={getStatusBadgeVariant(row.original.status)}
          className={getStatusColor(row.original.status)}
        >
          {row.original.status}
        </Badge>
      ),
    },
    {
      id: 'progress',
      header: 'Progress',
      cell: ({ row }) => {
        const proposal = row.original
        const progress =
          proposal.status === 'approved'
            ? 100
            : proposal.status === 'rejected'
              ? 0
              : proposal.status === 'negotiation'
                ? 60
                : proposal.status === 'submitted'
                  ? 30
                  : 10

        return <ProgressIndicator value={progress} size="sm" showLabel={false} />
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Link to="/proposals/$proposalId" params={{ proposalId: row.original.id }}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              setEditingProposal(row.original)
              setShowEditForm(true)
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={async (e) => {
              e.stopPropagation()
              if (confirm('Are you sure you want to delete this proposal?')) {
                try {
                  await deleteProposal.mutateAsync(row.original.id)
                  toast({
                    title: 'Success',
                    description: 'Proposal deleted successfully',
                  })
                } catch (error) {
                  toast({
                    title: 'Error',
                    description: 'Failed to delete proposal',
                    variant: 'destructive',
                  })
                }
              }
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  // Filter proposals based on date range if provided - ensure proposals is an array
  const proposalsArray = Array.isArray(proposals) ? proposals : []
  const filteredProposals = proposalsArray.filter((p) => {
    if (!filters.dateRange) return true
    const validUntil = new Date(p.validUntil)
    return validUntil >= filters.dateRange[0] && validUntil <= filters.dateRange[1]
  })

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-destructive">Error loading proposals: {(error as Error).message}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb items={[{ label: 'Dashboard', href: '/' }, { label: 'Proposals' }]} />

      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Proposals</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowUploadDialog(true)}>
            <FileUp className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Link to="/proposals/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Proposal
            </Button>
          </Link>
        </div>
      </div>

      <AdvancedSearch
        onSearch={(searchFilters) => {
          setFilters({
            search: searchFilters.query || '',
            status: searchFilters.filters?.status || 'all',
            minCost: searchFilters.filters?.minCost,
            maxCost: searchFilters.filters?.maxCost,
            dateRange: searchFilters.filters?.dateRange,
          })
        }}
        searchPlaceholder="Search proposals by title, description, or vendor..."
        filters={[
          {
            key: 'status',
            label: 'Status',
            type: 'select',
            options: [
              { value: 'all', label: 'All Statuses' },
              { value: 'draft', label: 'Draft' },
              { value: 'submitted', label: 'Submitted' },
              { value: 'negotiation', label: 'Negotiation' },
              { value: 'approved', label: 'Approved' },
              { value: 'rejected', label: 'Rejected' },
            ],
          },
          {
            key: 'costRange',
            label: 'Cost Range',
            type: 'range',
            min: 0,
            max: 1000000,
            step: 1000,
            format: (value) => `$${value.toLocaleString()}`,
          },
          {
            key: 'dateRange',
            label: 'Valid Until',
            type: 'dateRange',
          },
        ]}
        showSaveSearch
        savedSearches={[
          { id: '1', name: 'Pending Approval', filters: { status: 'submitted' } },
          { id: '2', name: 'High Value Proposals', filters: { minCost: 50000 } },
          {
            id: '3',
            name: 'Expiring Soon',
            filters: { dateRange: [new Date(), new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)] },
          },
        ]}
      />

      {selectedRows.size > 0 && (
        <BulkActions
          selectedCount={selectedRows.size}
          actions={[
            { label: 'Delete', value: 'delete', variant: 'destructive' },
            { label: 'Export', value: 'export' },
            { label: 'Approve', value: 'approve' },
            { label: 'Reject', value: 'reject' },
          ]}
          onAction={handleBulkAction}
        />
      )}

      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <LoadingSkeleton type="table" rows={5} />
          ) : (
            <DataTable
              columns={columns}
              data={filteredProposals}
              searchable
              sortable
              selectable
              selectedRows={selectedRows}
              onSelectionChange={setSelectedRows}
              emptyMessage="No proposals found"
              onRowClick={(row) => {
                window.location.href = `/proposals/${row.id}`
              }}
            />
          )}
        </CardContent>
      </Card>

      {showEditForm && editingProposal && (
        <ProposalEditForm
          proposal={editingProposal}
          open={showEditForm}
          onOpenChange={setShowEditForm}
          onSuccess={() => {
            setShowEditForm(false)
            setEditingProposal(null)
            toast({
              title: 'Success',
              description: 'Proposal updated successfully',
            })
          }}
        />
      )}

      <FileUpload
        open={showUploadDialog}
        onOpenChange={setShowUploadDialog}
        onUpload={handleFileUpload}
        accept=".csv,.xlsx,.pdf"
        multiple
        maxSize={20 * 1024 * 1024} // 20MB
        title="Import Proposals"
        description="Upload CSV, Excel, or PDF files to import proposals"
      />
    </div>
  )
}
