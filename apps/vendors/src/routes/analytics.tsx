import { createFileRoute } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import {
  Metric<PERSON>ard,
  DateRangePicker,
  VendorDistributionChart,
  ProposalStatusChart,
  RatingTrendsChart,
  CostAnalysisChart,
  PerformanceMetricsChart,
  ReviewSentimentChart,
  VendorPerformanceChart,
  ProposalCostBreakdownChart,
  ReviewRatingDistributionChart,
  ChartExport,
  type DateRange,
} from '~/components/analytics'
import { TrendingUp, DollarSign, Building2, FileText, Star, Download, Filter } from 'lucide-react'
import { startOfMonth, endOfMonth } from 'date-fns'
import { Breadcrumb } from '~/components/ui/Breadcrumb'
import { AdvancedSearch } from '~/components/ui/AdvancedSearch'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/lib/ui-components'
import { LoadingSkeleton } from '~/components/ui/LoadingSkeleton'
import { useToast } from '~/hooks/use-toast'

export const Route = createFileRoute('/analytics')({
  component: AnalyticsPage,
})

function AnalyticsPage() {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  })
  const [filters, setFilters] = useState({
    vendorCategory: 'all',
    proposalStatus: 'all',
    minRating: 0,
  })
  const [exportingChart, setExportingChart] = useState<string | null>(null)
  const { toast } = useToast()

  // Fetch dashboard overview data
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ['analytics-dashboard', dateRange, filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: dateRange.from.toISOString(),
        endDate: dateRange.to.toISOString(),
        ...filters,
      })

      const response = await fetch(`/api/analytics/dashboard?${params}`)
      if (!response.ok) throw new Error('Failed to fetch dashboard data')
      return response.json()
    },
  })

  const overview = dashboardData?.overview || {}
  const trends = dashboardData?.trends || {}
  const growth = dashboardData?.growth || {}

  const handleChartExport = async (chartId: string, format: 'png' | 'svg' | 'csv') => {
    setExportingChart(chartId)
    try {
      // Simulate export functionality
      await new Promise((resolve) => setTimeout(resolve, 1000))
      toast({
        title: 'Export Successful',
        description: `Chart exported as ${format.toUpperCase()}`,
      })
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export chart',
        variant: 'destructive',
      })
    } finally {
      setExportingChart(null)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb items={[{ label: 'Dashboard', href: '/' }, { label: 'Analytics' }]} />

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
        <div className="flex items-center gap-2">
          <DateRangePicker value={dateRange} onChange={setDateRange} align="end" />
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <AdvancedSearch
        onSearch={(searchFilters) => {
          setFilters({
            vendorCategory: searchFilters.filters?.vendorCategory || 'all',
            proposalStatus: searchFilters.filters?.proposalStatus || 'all',
            minRating: searchFilters.filters?.minRating || 0,
          })
        }}
        searchPlaceholder="Filter analytics data..."
        filters={[
          {
            key: 'vendorCategory',
            label: 'Vendor Category',
            type: 'select',
            options: [
              { value: 'all', label: 'All Categories' },
              { value: 'Training', label: 'Training' },
              { value: 'Assessment', label: 'Assessment' },
              { value: 'Certification', label: 'Certification' },
              { value: 'Content', label: 'Content' },
              { value: 'Consulting', label: 'Consulting' },
            ],
          },
          {
            key: 'proposalStatus',
            label: 'Proposal Status',
            type: 'select',
            options: [
              { value: 'all', label: 'All Statuses' },
              { value: 'draft', label: 'Draft' },
              { value: 'submitted', label: 'Submitted' },
              { value: 'negotiation', label: 'Negotiation' },
              { value: 'approved', label: 'Approved' },
              { value: 'rejected', label: 'Rejected' },
            ],
          },
          {
            key: 'minRating',
            label: 'Minimum Rating',
            type: 'range',
            min: 0,
            max: 5,
            step: 0.5,
          },
        ]}
      />

      {/* Key Metrics */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <LoadingSkeleton key={i} type="card" />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Total Vendors"
            value={overview.totalVendors || 0}
            change={growth.vendorGrowth}
            trend={growth.vendorGrowth > 0 ? 'up' : growth.vendorGrowth < 0 ? 'down' : 'neutral'}
            icon={<Building2 className="h-4 w-4" />}
          />

          <MetricCard
            title="Proposal Success Rate"
            value={`${overview.approvalRate || 0}%`}
            change={growth.proposalGrowth}
            trend={
              growth.proposalGrowth > 0 ? 'up' : growth.proposalGrowth < 0 ? 'down' : 'neutral'
            }
            icon={<FileText className="h-4 w-4" />}
          />

          <MetricCard
            title="Average Rating"
            value={overview.averageRating || 0}
            change={2.3}
            trend="up"
            icon={<Star className="h-4 w-4" />}
            formatValue={(value) => Number(value).toFixed(1)}
          />

          <MetricCard
            title="Total Spending"
            value={overview.totalSpent || 0}
            change={growth.spendingGrowth}
            trend={
              growth.spendingGrowth > 0 ? 'up' : growth.spendingGrowth < 0 ? 'down' : 'neutral'
            }
            icon={<DollarSign className="h-4 w-4" />}
            formatValue={(value) => `$${(Number(value) / 1000).toFixed(0)}k`}
          />
        </div>
      )}

      {/* Primary Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Vendor Distribution</CardTitle>
            <ChartExport
              chartId="vendor-distribution"
              onExport={(format) => handleChartExport('vendor-distribution', format)}
              loading={exportingChart === 'vendor-distribution'}
            />
          </CardHeader>
          <CardContent>
            <VendorDistributionChart dateRange={dateRange} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Proposal Status Overview</CardTitle>
            <ChartExport
              chartId="proposal-status"
              onExport={(format) => handleChartExport('proposal-status', format)}
              loading={exportingChart === 'proposal-status'}
            />
          </CardHeader>
          <CardContent>
            <ProposalStatusChart dateRange={dateRange} />
          </CardContent>
        </Card>
      </div>

      {/* Trend Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Rating Trends</CardTitle>
            <ChartExport
              chartId="rating-trends"
              onExport={(format) => handleChartExport('rating-trends', format)}
              loading={exportingChart === 'rating-trends'}
            />
          </CardHeader>
          <CardContent>
            <RatingTrendsChart dateRange={dateRange} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Cost Analysis</CardTitle>
            <ChartExport
              chartId="cost-analysis"
              onExport={(format) => handleChartExport('cost-analysis', format)}
              loading={exportingChart === 'cost-analysis'}
            />
          </CardHeader>
          <CardContent>
            <CostAnalysisChart dateRange={dateRange} />
          </CardContent>
        </Card>
      </div>

      {/* Performance and Sentiment Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Performance Metrics</CardTitle>
            <ChartExport
              chartId="performance-metrics"
              onExport={(format) => handleChartExport('performance-metrics', format)}
              loading={exportingChart === 'performance-metrics'}
            />
          </CardHeader>
          <CardContent>
            <PerformanceMetricsChart dateRange={dateRange} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Review Sentiment</CardTitle>
            <ChartExport
              chartId="review-sentiment"
              onExport={(format) => handleChartExport('review-sentiment', format)}
              loading={exportingChart === 'review-sentiment'}
            />
          </CardHeader>
          <CardContent>
            <ReviewSentimentChart dateRange={dateRange} />
          </CardContent>
        </Card>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Vendor Performance</CardTitle>
            <ChartExport
              chartId="vendor-performance"
              onExport={(format) => handleChartExport('vendor-performance', format)}
              loading={exportingChart === 'vendor-performance'}
            />
          </CardHeader>
          <CardContent>
            <VendorPerformanceChart />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Proposal Cost Breakdown</CardTitle>
            <ChartExport
              chartId="proposal-cost-breakdown"
              onExport={(format) => handleChartExport('proposal-cost-breakdown', format)}
              loading={exportingChart === 'proposal-cost-breakdown'}
            />
          </CardHeader>
          <CardContent>
            <ProposalCostBreakdownChart data={dashboardData?.costBreakdown || []} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Rating Distribution</CardTitle>
            <ChartExport
              chartId="rating-distribution"
              onExport={(format) => handleChartExport('rating-distribution', format)}
              loading={exportingChart === 'rating-distribution'}
            />
          </CardHeader>
          <CardContent>
            <ReviewRatingDistributionChart data={dashboardData?.ratingDistribution || []} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
