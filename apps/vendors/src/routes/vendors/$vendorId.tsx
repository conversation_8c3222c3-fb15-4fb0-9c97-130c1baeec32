import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useVendor } from '~/hooks/useVendors'
import { useVendorProposals } from '~/hooks/useProposals'
import { useVendorReviews } from '~/hooks/useReviews'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/lib/ui-components'
import { Button } from '~/lib/ui-components'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import {
  Building2,
  Phone,
  Mail,
  Globe,
  Star,
  FileText,
  Calendar,
  DollarSign,
  Edit,
  ArrowLeft,
  Shield,
  Upload,
  Download,
  Clock,
} from 'lucide-react'
import { format } from 'date-fns'
import { ReviewForm } from '~/components/reviews/ReviewForm'
import { useState } from 'react'
import { LoadingSkeleton } from '~/components/ui/LoadingSkeleton'
import { Breadcrumb } from '~/components/ui/Breadcrumb'
import { FileUpload } from '~/components/ui/FileUpload'
import { StatusTimeline } from '~/components/ui/StatusTimeline'
import { VendorEditForm } from '~/components/vendors/VendorEditForm'
import { ProposalCostBreakdownChart } from '~/components/analytics/ProposalCostBreakdownChart'
import { useToast } from '~/hooks/use-toast'

export const Route = createFileRoute('/vendors/$vendorId')({
  component: VendorDetailsPage,
})

function VendorDetailsPage() {
  const { vendorId } = Route.useParams()
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const { toast } = useToast()
  const { data: vendor, isLoading: vendorLoading } = useVendor(vendorId)
  const { data: proposals, isLoading: proposalsLoading } = useVendorProposals(vendorId)
  const { data: reviews, isLoading: reviewsLoading } = useVendorReviews(vendorId)

  if (vendorLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <LoadingSkeleton type="text" lines={1} />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <LoadingSkeleton type="card" />
          </div>
          <div>
            <LoadingSkeleton type="card" />
          </div>
        </div>
      </div>
    )
  }

  if (!vendor) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-destructive">Vendor not found</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getStatusBadgeVariant = (status: typeof vendor.status) => {
    switch (status) {
      case 'active':
        return 'default'
      case 'inactive':
        return 'secondary'
      case 'pending':
        return 'outline'
    }
  }

  const getCategoryColor = (category: typeof vendor.category) => {
    const colors = {
      Training: 'bg-blue-100 text-blue-800',
      Assessment: 'bg-green-100 text-green-800',
      Certification: 'bg-purple-100 text-purple-800',
      Content: 'bg-yellow-100 text-yellow-800',
      Consulting: 'bg-pink-100 text-pink-800',
    }
    return colors[category]
  }

  const averageRating = reviews?.length
    ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
    : vendor.rating

  const handleFileUpload = (files: File[]) => {
    // Implement file upload logic
    toast({
      title: 'Files Uploaded',
      description: `${files.length} file(s) uploaded successfully`,
    })
    setShowFileUpload(false)
  }

  // Timeline events for vendor history
  const timelineEvents = [
    {
      date: new Date(vendor.createdAt),
      title: 'Vendor Added',
      description: 'Vendor profile created',
      type: 'created' as const,
    },
    ...(vendor.status === 'active'
      ? [
          {
            date: new Date(vendor.updatedAt),
            title: 'Status Updated',
            description: 'Vendor activated',
            type: 'success' as const,
          },
        ]
      : []),
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb
        items={[
          { label: 'Dashboard', href: '/' },
          { label: 'Vendors', href: '/vendors' },
          { label: vendor.companyName },
        ]}
      />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold">{vendor.companyName}</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowFileUpload(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Upload Documents
          </Button>
          <Button onClick={() => setShowEditForm(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Vendor
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Vendor Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Category</p>
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${getCategoryColor(vendor.category)}`}
                  >
                    {vendor.category}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <Badge variant={getStatusBadgeVariant(vendor.status)}>{vendor.status}</Badge>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground mb-2">Contact Information</p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span>{vendor.contactPerson}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <a href={`mailto:${vendor.email}`} className="hover:underline">
                      {vendor.email}
                    </a>
                  </div>
                  {vendor.phoneNumbers.map((phone, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{phone.number}</span>
                      <Badge variant="outline" className="text-xs">
                        {phone.type}
                      </Badge>
                    </div>
                  ))}
                  {vendor.website && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={vendor.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline"
                      >
                        {vendor.website}
                      </a>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground mb-2">Certifications</p>
                <div className="flex flex-wrap gap-2">
                  {vendor.certifications.map((cert, index) => (
                    <div key={index} className="flex items-center gap-1">
                      <Shield className="h-3 w-3 text-green-600" />
                      <span className="text-sm">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <p className="text-sm text-muted-foreground">Created</p>
                  <p className="text-sm">{format(new Date(vendor.createdAt), 'MMM d, yyyy')}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Last Updated</p>
                  <p className="text-sm">{format(new Date(vendor.updatedAt), 'MMM d, yyyy')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="proposals" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="proposals">Proposals</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>

            <TabsContent value="proposals" className="space-y-4">
              {proposalsLoading ? (
                <LoadingSkeleton type="card" rows={3} />
              ) : proposals && proposals.length > 0 ? (
                proposals.map((proposal) => (
                  <Card key={proposal.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <h4 className="font-semibold">{proposal.title}</h4>
                          <p className="text-sm text-muted-foreground">{proposal.description}</p>
                          <div className="flex items-center gap-4 mt-2">
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">
                                ${proposal.totalCost.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">
                                Valid until {format(new Date(proposal.validUntil), 'MMM d, yyyy')}
                              </span>
                            </div>
                          </div>
                        </div>
                        <Badge>{proposal.status}</Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-muted-foreground">No proposals yet</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="reviews" className="space-y-4">
              {reviewsLoading ? (
                <LoadingSkeleton type="card" rows={3} />
              ) : reviews && reviews.length > 0 ? (
                reviews.map((review) => (
                  <Card key={review.id}>
                    <CardContent className="p-6">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-semibold">{review.reviewerName}</p>
                            <p className="text-sm text-muted-foreground">{review.reviewerRole}</p>
                          </div>
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-sm">{review.comment}</p>
                        {review.strengths.length > 0 && (
                          <div>
                            <p className="text-sm font-medium">Strengths:</p>
                            <ul className="list-disc list-inside text-sm text-muted-foreground">
                              {review.strengths.map((strength, i) => (
                                <li key={i}>{strength}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {review.improvements.length > 0 && (
                          <div>
                            <p className="text-sm font-medium">Areas for Improvement:</p>
                            <ul className="list-disc list-inside text-sm text-muted-foreground">
                              {review.improvements.map((improvement, i) => (
                                <li key={i}>{improvement}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(review.createdAt), 'MMM d, yyyy')}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-muted-foreground">No reviews yet</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm text-muted-foreground">Average Rating</p>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-semibold">{averageRating.toFixed(1)}</span>
                  </div>
                </div>
                <div className="flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-6 w-6 ${i < Math.round(averageRating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
                    />
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Total Proposals</span>
                  <span className="font-medium">{proposals?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Approved Proposals</span>
                  <span className="font-medium">
                    {proposals?.filter((p) => p.status === 'approved').length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Total Reviews</span>
                  <span className="font-medium">{reviews?.length || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link to="/proposals/new" search={{ vendorId }}>
                <Button className="w-full" variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Create Proposal
                </Button>
              </Link>
              <Button className="w-full" variant="outline" onClick={() => setShowReviewForm(true)}>
                <Star className="mr-2 h-4 w-4" />
                Add Review
              </Button>
              <Button className="w-full" variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export Vendor Data
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Activity Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <StatusTimeline events={timelineEvents} />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Cost Breakdown for all proposals */}
      {proposals && proposals.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Combined Proposal Costs</CardTitle>
          </CardHeader>
          <CardContent>
            <ProposalCostBreakdownChart
              data={proposals.reduce((acc, proposal) => {
                proposal.costBreakdown.forEach((item) => {
                  const existing = acc.find((a) => a.category === item.category)
                  if (existing) {
                    existing.amount += item.amount
                  } else {
                    acc.push({ ...item })
                  }
                })
                return acc
              }, [] as any[])}
            />
          </CardContent>
        </Card>
      )}

      <ReviewForm vendorId={vendorId} open={showReviewForm} onOpenChange={setShowReviewForm} />

      {showEditForm && (
        <VendorEditForm
          vendor={vendor}
          open={showEditForm}
          onOpenChange={setShowEditForm}
          onSuccess={() => {
            setShowEditForm(false)
            toast({
              title: 'Success',
              description: 'Vendor updated successfully',
            })
          }}
        />
      )}

      <FileUpload
        open={showFileUpload}
        onOpenChange={setShowFileUpload}
        onUpload={handleFileUpload}
        accept=".pdf,.doc,.docx,.xlsx,.csv"
        multiple
        maxSize={10 * 1024 * 1024} // 10MB
        title="Upload Vendor Documents"
        description="Upload contracts, certifications, or other vendor-related documents"
      />
    </div>
  )
}
