import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useCreateVendor } from '~/hooks/useVendors'
import { <PERSON><PERSON> } from '~/lib/ui-components'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Input } from '~/lib/ui-components'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { ArrowLeft, Plus, X } from 'lucide-react'
import { Link } from '@tanstack/react-router'
import type { VendorCategory, VendorStatus } from '~/types/vendor-management'

const vendorSchema = z.object({
  companyName: z.string().min(2, 'Company name must be at least 2 characters'),
  contactPerson: z.string().min(2, 'Contact person name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumbers: z
    .array(
      z.object({
        type: z.enum(['primary', 'secondary']),
        number: z.string().min(10, 'Phone number must be at least 10 digits'),
      })
    )
    .min(1, 'At least one phone number is required'),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  category: z.enum(['Training', 'Assessment', 'Certification', 'Content', 'Consulting']),
  status: z.enum(['active', 'inactive', 'pending']),
  certifications: z.array(z.string()).default([]),
})

type VendorFormData = z.infer<typeof vendorSchema>

export const Route = createFileRoute('/vendors/new')({
  component: NewVendorPage,
})

function NewVendorPage() {
  const navigate = useNavigate()
  const createVendor = useCreateVendor()

  const form = useForm<VendorFormData>({
    resolver: zodResolver(vendorSchema),
    defaultValues: {
      companyName: '',
      contactPerson: '',
      email: '',
      phoneNumbers: [{ type: 'primary', number: '' }],
      website: '',
      category: 'Training',
      status: 'pending',
      certifications: [],
    },
  })

  const onSubmit = async (data: VendorFormData) => {
    try {
      const vendorData = {
        ...data,
        website: data.website || undefined,
        rating: 0,
      }

      await createVendor.mutateAsync(vendorData)
      navigate({ to: '/vendors' })
    } catch (error) {
      console.error('Failed to create vendor:', error)
    }
  }

  const addPhoneNumber = () => {
    const currentPhones = form.getValues('phoneNumbers')
    form.setValue('phoneNumbers', [...currentPhones, { type: 'secondary', number: '' }])
  }

  const removePhoneNumber = (index: number) => {
    const currentPhones = form.getValues('phoneNumbers')
    if (currentPhones.length > 1) {
      form.setValue(
        'phoneNumbers',
        currentPhones.filter((_, i) => i !== index)
      )
    }
  }

  const addCertification = () => {
    const cert = prompt('Enter certification name:')
    if (cert) {
      const currentCerts = form.getValues('certifications')
      form.setValue('certifications', [...currentCerts, cert])
    }
  }

  const removeCertification = (index: number) => {
    const currentCerts = form.getValues('certifications')
    form.setValue(
      'certifications',
      currentCerts.filter((_, i) => i !== index)
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <div className="flex items-center gap-4 mb-6">
        <Link to="/vendors">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Vendors
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Add New Vendor</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Vendor Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Person</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter contact person's name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <div className="flex items-center justify-between mb-2">
                  <FormLabel>Phone Numbers</FormLabel>
                  <Button type="button" variant="outline" size="sm" onClick={addPhoneNumber}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Phone
                  </Button>
                </div>
                {form.watch('phoneNumbers').map((_, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <FormField
                      control={form.control}
                      name={`phoneNumbers.${index}.type`}
                      render={({ field }) => (
                        <FormItem className="w-32">
                          <FormControl>
                            <Select value={field.value} onValueChange={field.onChange}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="primary">Primary</SelectItem>
                                <SelectItem value="secondary">Secondary</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name={`phoneNumbers.${index}.number`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormControl>
                            <Input placeholder="Phone number" {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    {form.watch('phoneNumbers').length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removePhoneNumber(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website (Optional)</FormLabel>
                    <FormControl>
                      <Input type="url" placeholder="https://company.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Training">Training</SelectItem>
                        <SelectItem value="Assessment">Assessment</SelectItem>
                        <SelectItem value="Certification">Certification</SelectItem>
                        <SelectItem value="Content">Content</SelectItem>
                        <SelectItem value="Consulting">Consulting</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <div className="flex items-center justify-between mb-2">
                  <FormLabel>Certifications</FormLabel>
                  <Button type="button" variant="outline" size="sm" onClick={addCertification}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Certification
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {form.watch('certifications').map((cert, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-1 bg-secondary text-secondary-foreground px-3 py-1 rounded-md"
                    >
                      <span className="text-sm">{cert}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0"
                        onClick={() => removeCertification(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  {form.watch('certifications').length === 0 && (
                    <p className="text-sm text-muted-foreground">No certifications added</p>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" disabled={createVendor.isPending}>
                  {createVendor.isPending ? 'Creating...' : 'Create Vendor'}
                </Button>
                <Link to="/vendors">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
