import { createFile<PERSON>out<PERSON>, <PERSON>, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { useReview, useDeleteReview } from '~/hooks/useReviews'
import { useVendor } from '~/hooks/useVendors'
import { useProposal } from '~/hooks/useProposals'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/lib/ui-components'
import { Button } from '~/lib/ui-components'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import {
  ArrowLeft,
  Building2,
  Calendar,
  FileText,
  Star,
  Edit,
  Trash2,
  User,
  MessageSquare,
  ThumbsUp,
  AlertTriangle,
} from 'lucide-react'
import { format } from 'date-fns'
import { ReviewEditForm } from '~/components/reviews/ReviewEditForm'
import { useToast } from '~/hooks/use-toast'
import { Breadcrumb } from '~/components/ui/Breadcrumb'

export const Route = createFileRoute('/reviews/$reviewId')({
  component: ReviewDetailsPage,
})

function ReviewDetailsPage() {
  const { reviewId } = Route.useParams()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [showEditForm, setShowEditForm] = useState(false)

  const { data: review, isLoading: reviewLoading } = useReview(reviewId)
  const { data: vendor } = useVendor(review?.vendorId)
  const { data: proposal } = useProposal(review?.proposalId)
  const deleteReview = useDeleteReview()

  if (reviewLoading) {
    return (
      <div className="container mx-auto p-6">
        <p className="text-muted-foreground">Loading review details...</p>
      </div>
    )
  }

  if (!review) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-destructive">Review not found</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      try {
        await deleteReview.mutateAsync(reviewId)
        toast({
          title: 'Success',
          description: 'Review deleted successfully',
        })
        navigate({ to: '/reviews' })
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to delete review',
          variant: 'destructive',
        })
      }
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb
        items={[
          { label: 'Dashboard', href: '/' },
          { label: 'Reviews', href: '/reviews' },
          { label: `Review by ${review.reviewerName}` },
        ]}
      />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/reviews">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Reviews
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Review Details</h1>
            <p className="text-muted-foreground">Review by {review.reviewerName}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowEditForm(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Review
          </Button>
          <Button variant="destructive" onClick={handleDelete} disabled={deleteReview.isPending}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Review Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Rating</p>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${i < review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
                        />
                      ))}
                    </div>
                    <span className="text-lg font-semibold">{review.rating}/5</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground mb-1">Review Date</p>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{format(new Date(review.createdAt), 'MMMM d, yyyy')}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Reviewer</p>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <div>
                      <p className="font-medium">{review.reviewerName}</p>
                      <p className="text-sm text-muted-foreground">{review.reviewerRole}</p>
                    </div>
                  </div>
                </div>
                {vendor && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Vendor</p>
                    <Link
                      to="/vendors/$vendorId"
                      params={{ vendorId: vendor.id }}
                      className="flex items-center gap-2 hover:underline"
                    >
                      <Building2 className="h-4 w-4" />
                      <div>
                        <p className="font-medium">{vendor.companyName}</p>
                        <p className="text-sm text-muted-foreground">{vendor.category}</p>
                      </div>
                    </Link>
                  </div>
                )}
              </div>

              {proposal && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Related Proposal</p>
                  <Link
                    to="/proposals/$proposalId"
                    params={{ proposalId: proposal.id }}
                    className="flex items-center gap-2 p-3 bg-muted rounded-md hover:bg-muted/80 transition-colors"
                  >
                    <FileText className="h-4 w-4" />
                    <div className="flex-1">
                      <p className="font-medium">{proposal.title}</p>
                      <p className="text-sm text-muted-foreground">
                        ${proposal.totalCost.toLocaleString()} • {proposal.status}
                      </p>
                    </div>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Review Comment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm leading-relaxed whitespace-pre-wrap">{review.comment}</p>
            </CardContent>
          </Card>

          <Tabs defaultValue="strengths" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="strengths">Strengths</TabsTrigger>
              <TabsTrigger value="improvements">Areas for Improvement</TabsTrigger>
            </TabsList>

            <TabsContent value="strengths">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ThumbsUp className="h-5 w-5 text-green-600" />
                    Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {review.strengths && review.strengths.length > 0 ? (
                    <ul className="space-y-2">
                      {review.strengths.map((strength, index) => (
                        <li
                          key={index}
                          className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-950/20 rounded-md"
                        >
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-sm">{strength}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">No strengths identified</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="improvements">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    Areas for Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {review.improvements && review.improvements.length > 0 ? (
                    <ul className="space-y-2">
                      {review.improvements.map((improvement, index) => (
                        <li
                          key={index}
                          className="flex items-start gap-3 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-md"
                        >
                          <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-sm">{improvement}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No areas for improvement identified
                    </p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Rating</span>
                <Badge
                  variant={
                    review.rating >= 4
                      ? 'default'
                      : review.rating >= 3
                        ? 'secondary'
                        : 'destructive'
                  }
                >
                  {review.rating} Star{review.rating !== 1 ? 's' : ''}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Strengths Listed</span>
                <span className="font-medium">{review.strengths?.length || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Improvements Listed</span>
                <span className="font-medium">{review.improvements?.length || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Comment Length</span>
                <span className="font-medium">{review.comment.length} chars</span>
              </div>
            </CardContent>
          </Card>

          {vendor && (
            <Card>
              <CardHeader>
                <CardTitle>Vendor Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium">{vendor.companyName}</p>
                  <p className="text-sm text-muted-foreground">{vendor.category}</p>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Overall Rating</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{vendor.rating.toFixed(1)}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <Badge variant={vendor.status === 'active' ? 'default' : 'secondary'}>
                    {vendor.status}
                  </Badge>
                </div>
                <Link to="/vendors/$vendorId" params={{ vendorId: vendor.id }}>
                  <Button variant="outline" size="sm" className="w-full">
                    View Vendor Details
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {review && (
        <ReviewEditForm reviewId={reviewId} open={showEditForm} onOpenChange={setShowEditForm} />
      )}
    </div>
  )
}
