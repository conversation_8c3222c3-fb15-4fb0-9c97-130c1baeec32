import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { useReviews, useDeleteReview } from '~/hooks/useReviews'
import { Card } from '~/lib/ui-components'
import { Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/lib/ui-components'
import { Button } from '~/lib/ui-components'
import {
  Star,
  Building2,
  FileText,
  TrendingUp,
  TrendingDown,
  Plus,
  Edit,
  Trash2,
  Eye,
} from 'lucide-react'
import { format } from 'date-fns'
import { DataTable } from '~/components/ui/DataTable'
import { AdvancedSearch } from '~/components/ui/AdvancedSearch'
import { LoadingSkeleton } from '~/components/ui/LoadingSkeleton'
import { Breadcrumb } from '~/components/ui/Breadcrumb'
import { ReviewEditForm } from '~/components/reviews/ReviewEditForm'
import { ReviewRatingDistributionChart } from '~/components/analytics/ReviewRatingDistributionChart'
import { ReviewSentimentChart } from '~/components/analytics/ReviewSentimentChart'
import { StatusTimeline } from '~/components/ui/StatusTimeline'
import { NotificationCenter } from '~/components/ui/NotificationCenter'
import { useToast } from '~/hooks/use-toast'
import type { Review } from '~/types/vendor-management'
import type { ColumnDef } from '@tanstack/react-table'

export const Route = createFileRoute('/reviews/')({
  component: ReviewsPage,
})

function ReviewsPage() {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())
  const [showEditForm, setShowEditForm] = useState(false)
  const [editingReview, setEditingReview] = useState<Review | null>(null)
  const [filters, setFilters] = useState({
    search: '',
    rating: [0, 5] as [number, number],
    dateRange: undefined as [Date, Date] | undefined,
  })

  const { toast } = useToast()
  const { data: reviews, isLoading, error } = useReviews()
  const deleteReview = useDeleteReview()

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-destructive">Error loading reviews: {(error as Error).message}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getRatingDistribution = () => {
    const reviewsArray = Array.isArray(reviews) ? reviews : []
    if (reviewsArray.length === 0) return []

    const distribution = [0, 0, 0, 0, 0]
    reviewsArray.forEach((review) => {
      distribution[review.rating - 1]++
    })

    return distribution.map((count, index) => ({
      rating: index + 1,
      count,
      percentage: reviewsArray.length > 0 ? (count / reviewsArray.length) * 100 : 0,
    }))
  }

  const reviewsArray = Array.isArray(reviews) ? reviews : []
  const averageRating =
    reviewsArray.length > 0
      ? reviewsArray.reduce((sum, r) => sum + r.rating, 0) / reviewsArray.length
      : 0

  // Filter reviews based on search and filters
  const filteredReviews = reviewsArray.filter((review) => {
    const matchesSearch =
      !filters.search ||
      review.reviewerName.toLowerCase().includes(filters.search.toLowerCase()) ||
      review.comment.toLowerCase().includes(filters.search.toLowerCase()) ||
      review.vendor?.companyName.toLowerCase().includes(filters.search.toLowerCase())

    const matchesRating = review.rating >= filters.rating[0] && review.rating <= filters.rating[1]

    const matchesDate =
      !filters.dateRange ||
      (new Date(review.createdAt) >= filters.dateRange[0] &&
        new Date(review.createdAt) <= filters.dateRange[1])

    return matchesSearch && matchesRating && matchesDate
  })

  const columns: ColumnDef<Review>[] = [
    {
      accessorKey: 'reviewerName',
      header: 'Reviewer',
      cell: ({ row }) => (
        <div>
          <p className="font-medium">{row.original.reviewerName}</p>
          <p className="text-sm text-muted-foreground">{row.original.reviewerRole}</p>
        </div>
      ),
    },
    {
      accessorKey: 'vendor.companyName',
      header: 'Vendor',
      cell: ({ row }) => {
        const review = row.original
        return review.vendor ? (
          <Link
            to="/vendors/$vendorId"
            params={{ vendorId: review.vendor.id }}
            className="flex items-center gap-2 hover:underline"
            onClick={(e) => e.stopPropagation()}
          >
            <Building2 className="h-4 w-4 text-muted-foreground" />
            {review.vendor.companyName}
          </Link>
        ) : (
          <span className="text-muted-foreground">-</span>
        )
      },
    },
    {
      accessorKey: 'proposal.title',
      header: 'Proposal',
      cell: ({ row }) => {
        const review = row.original
        return review.proposal ? (
          <Link
            to="/proposals/$proposalId"
            params={{ proposalId: review.proposal.id }}
            className="flex items-center gap-2 hover:underline"
            onClick={(e) => e.stopPropagation()}
          >
            <FileText className="h-4 w-4 text-muted-foreground" />
            {review.proposal.title}
          </Link>
        ) : (
          <span className="text-muted-foreground">-</span>
        )
      },
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${i < row.original.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
            />
          ))}
        </div>
      ),
    },
    {
      accessorKey: 'comment',
      header: 'Comment',
      cell: ({ row }) => <p className="text-sm line-clamp-2">{row.original.comment}</p>,
    },
    {
      accessorKey: 'createdAt',
      header: 'Date',
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {format(new Date(row.original.createdAt), 'MMM d, yyyy')}
        </span>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              setEditingReview(row.original)
              setShowEditForm(true)
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={async (e) => {
              e.stopPropagation()
              if (confirm('Are you sure you want to delete this review?')) {
                try {
                  await deleteReview.mutateAsync(row.original.id)
                  toast({
                    title: 'Success',
                    description: 'Review deleted successfully',
                  })
                } catch (error) {
                  toast({
                    title: 'Error',
                    description: 'Failed to delete review',
                    variant: 'destructive',
                  })
                }
              }
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Breadcrumb items={[{ label: 'Dashboard', href: '/' }, { label: 'Reviews' }]} />

      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Reviews</h1>
        <Link to="/reviews/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Review
          </Button>
        </Link>
      </div>

      <AdvancedSearch
        onSearch={(searchFilters) => {
          setFilters({
            search: searchFilters.query || '',
            rating: searchFilters.filters?.rating || [0, 5],
            dateRange: searchFilters.filters?.dateRange,
          })
        }}
        searchPlaceholder="Search reviews by reviewer, vendor, or content..."
        filters={[
          {
            key: 'rating',
            label: 'Rating',
            type: 'range',
            min: 0,
            max: 5,
            step: 1,
          },
          {
            key: 'dateRange',
            label: 'Date Range',
            type: 'dateRange',
          },
        ]}
        showSaveSearch
        savedSearches={[
          { id: '1', name: 'High Ratings', filters: { rating: [4, 5] } },
          {
            id: '2',
            name: 'Recent Reviews',
            filters: { dateRange: [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()] },
          },
        ]}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>All Reviews</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {isLoading ? (
              <LoadingSkeleton type="table" rows={5} />
            ) : (
              <DataTable
                columns={columns}
                data={filteredReviews}
                searchable
                sortable
                selectable
                selectedRows={selectedRows}
                onSelectionChange={setSelectedRows}
                emptyMessage="No reviews found"
                onRowClick={(row) => {
                  window.location.href = `/reviews/${row.id}`
                }}
              />
            )}
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Rating Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ReviewRatingDistributionChart
                data={getRatingDistribution().map(({ rating, count }) => ({
                  rating: rating.toString(),
                  count,
                }))}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Review Sentiment</CardTitle>
            </CardHeader>
            <CardContent>
              <ReviewSentimentChart
                data={[
                  {
                    sentiment: 'Positive',
                    count: filteredReviews.filter((r) => r.rating >= 4).length,
                  },
                  {
                    sentiment: 'Neutral',
                    count: filteredReviews.filter((r) => r.rating === 3).length,
                  },
                  {
                    sentiment: 'Negative',
                    count: filteredReviews.filter((r) => r.rating <= 2).length,
                  },
                ]}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Rated Vendors</CardTitle>
            </CardHeader>
            <CardContent>
              {reviewsArray.length > 0 ? (
                <div className="space-y-3">
                  {Array.from(
                    reviewsArray.reduce((acc, review) => {
                      if (!review.vendor) return acc

                      const vendorId = review.vendor.id
                      if (!acc.has(vendorId)) {
                        acc.set(vendorId, {
                          vendor: review.vendor,
                          totalRating: 0,
                          count: 0,
                        })
                      }

                      const data = acc.get(vendorId)!
                      data.totalRating += review.rating
                      data.count++

                      return acc
                    }, new Map())
                  )
                    .map(([vendorId, data]) => ({
                      vendorId,
                      vendorName: data.vendor.companyName,
                      averageRating: data.totalRating / data.count,
                      reviewCount: data.count,
                    }))
                    .sort((a, b) => b.averageRating - a.averageRating)
                    .slice(0, 5)
                    .map((vendor) => (
                      <div key={vendor.vendorId} className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{vendor.vendorName}</p>
                          <p className="text-xs text-muted-foreground">
                            {vendor.reviewCount} review{vendor.reviewCount !== 1 ? 's' : ''}
                          </p>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium">
                            {vendor.averageRating.toFixed(1)}
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No vendors rated yet</p>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <StatusTimeline
                events={filteredReviews
                  .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                  .slice(0, 5)
                  .map((review) => ({
                    date: new Date(review.createdAt),
                    title: `${review.reviewerName} reviewed ${review.vendor?.companyName || 'Unknown'}`,
                    description: `Rated ${review.rating}/5`,
                    type:
                      review.rating >= 4
                        ? 'success'
                        : review.rating <= 2
                          ? 'error'
                          : ('default' as const),
                  }))}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {showEditForm && editingReview && (
        <ReviewEditForm
          review={editingReview}
          open={showEditForm}
          onOpenChange={setShowEditForm}
          onSuccess={() => {
            setShowEditForm(false)
            setEditingReview(null)
            toast({
              title: 'Success',
              description: 'Review updated successfully',
            })
          }}
        />
      )}
    </div>
  )
}
