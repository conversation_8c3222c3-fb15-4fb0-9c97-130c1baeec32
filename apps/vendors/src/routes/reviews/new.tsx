import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, use<PERSON><PERSON><PERSON> } from '@tanstack/react-router'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useState } from 'react'
import { useVendors } from '~/hooks/useVendors'
import { useProposals } from '~/hooks/useProposals'
import { useCreateReview } from '~/hooks/useReviews'
import { Button } from '~/lib/ui-components'
import { Card } from '~/lib/ui-components'
import { CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Input } from '~/lib/ui-components'
import { Textarea } from '~/lib/ui-components'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '~/components/ui/form'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '~/components/ui/tabs'
import { Badge } from '~/lib/ui-components'
import { Breadcrumb } from '~/components/ui/Breadcrumb'
import { ArrowLeft, Plus, X, Star, Building2, FileText, User, MessageSquare } from 'lucide-react'

const reviewSchema = z.object({
  vendorId: z.string().min(1, 'Vendor is required'),
  proposalId: z.string().optional(),
  rating: z.number().min(1, 'Rating is required').max(5),
  reviewerName: z.string().min(2, 'Reviewer name is required'),
  reviewerRole: z.string().min(2, 'Reviewer role is required'),
  comment: z.string().min(10, 'Comment must be at least 10 characters'),
  strengths: z.array(z.string()).default([]),
  improvements: z.array(z.string()).default([]),
})

type ReviewFormData = z.infer<typeof reviewSchema>

export const Route = createFileRoute('/reviews/new')({
  component: NewReviewPage,
})

function NewReviewPage() {
  const navigate = useNavigate()
  const [hoveredRating, setHoveredRating] = useState(0)
  const [newStrength, setNewStrength] = useState('')
  const [newImprovement, setNewImprovement] = useState('')

  const { data: vendors, isLoading: vendorsLoading } = useVendors({ status: 'active' })
  const { data: proposals } = useProposals()
  const createReview = useCreateReview()

  const form = useForm<ReviewFormData>({
    resolver: zodResolver(reviewSchema),
    defaultValues: {
      vendorId: '',
      proposalId: '',
      rating: 0,
      reviewerName: '',
      reviewerRole: '',
      comment: '',
      strengths: [],
      improvements: [],
    },
  })

  const selectedVendorId = form.watch('vendorId')
  const vendorsArray = Array.isArray(vendors) ? vendors : []
  const proposalsArray = Array.isArray(proposals) ? proposals : []
  const selectedVendor = vendorsArray.find((v) => v.id === selectedVendorId)
  const vendorProposals = proposalsArray.filter((p) => p.vendorId === selectedVendorId)

  const onSubmit = async (data: ReviewFormData) => {
    try {
      await createReview.mutateAsync({
        ...data,
        rating: data.rating as 1 | 2 | 3 | 4 | 5,
        proposalId: data.proposalId || undefined,
        createdAt: new Date(),
      })

      navigate({ to: '/reviews' })
    } catch (error) {
      console.error('Failed to create review:', error)
    }
  }

  const addStrength = () => {
    if (newStrength.trim()) {
      const current = form.getValues('strengths')
      form.setValue('strengths', [...current, newStrength.trim()])
      setNewStrength('')
    }
  }

  const removeStrength = (index: number) => {
    const current = form.getValues('strengths')
    form.setValue(
      'strengths',
      current.filter((_, i) => i !== index)
    )
  }

  const addImprovement = () => {
    if (newImprovement.trim()) {
      const current = form.getValues('improvements')
      form.setValue('improvements', [...current, newImprovement.trim()])
      setNewImprovement('')
    }
  }

  const removeImprovement = (index: number) => {
    const current = form.getValues('improvements')
    form.setValue(
      'improvements',
      current.filter((_, i) => i !== index)
    )
  }

  const selectedRating = form.watch('rating')

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Breadcrumb
        items={[
          { label: 'Dashboard', href: '/' },
          { label: 'Reviews', href: '/reviews' },
          { label: 'New Review' },
        ]}
      />

      <div className="flex items-center gap-4 mb-6">
        <Link to="/reviews">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Reviews
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Create New Review</h1>
          <p className="text-muted-foreground">Share your experience working with a vendor</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Review Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs defaultValue="vendor" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="vendor">Vendor & Proposal</TabsTrigger>
                  <TabsTrigger value="rating">Rating & Comment</TabsTrigger>
                  <TabsTrigger value="feedback">Detailed Feedback</TabsTrigger>
                  <TabsTrigger value="reviewer">Reviewer Info</TabsTrigger>
                </TabsList>

                <TabsContent value="vendor" className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="vendorId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vendor *</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={(value) => {
                              field.onChange(value)
                              // Clear proposal selection when vendor changes
                              form.setValue('proposalId', '')
                            }}
                            disabled={vendorsLoading}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={
                                    vendorsLoading
                                      ? 'Loading vendors...'
                                      : 'Select a vendor to review'
                                  }
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {vendorsArray.map((vendor) => (
                                <SelectItem key={vendor.id} value={vendor.id}>
                                  <div className="flex items-center gap-2">
                                    <Building2 className="h-4 w-4" />
                                    <div>
                                      <span className="font-medium">{vendor.companyName}</span>
                                      <span className="text-sm text-muted-foreground ml-2">
                                        ({vendor.category})
                                      </span>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>Choose the vendor you want to review</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {selectedVendor && (
                      <div className="p-4 bg-muted rounded-lg">
                        <h4 className="font-medium mb-2">Selected Vendor</h4>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{selectedVendor.companyName}</p>
                            <p className="text-sm text-muted-foreground">
                              {selectedVendor.category}
                            </p>
                          </div>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm font-medium">
                              {selectedVendor.rating.toFixed(1)}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    <FormField
                      control={form.control}
                      name="proposalId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Related Proposal (Optional)</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            disabled={!selectedVendorId || vendorProposals.length === 0}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={
                                    !selectedVendorId
                                      ? 'Select a vendor first'
                                      : vendorProposals.length === 0
                                        ? 'No proposals available'
                                        : 'Select a related proposal (optional)'
                                  }
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {vendorProposals.map((proposal) => (
                                <SelectItem key={proposal.id} value={proposal.id}>
                                  <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4" />
                                    <div>
                                      <span className="font-medium">{proposal.title}</span>
                                      <span className="text-sm text-muted-foreground ml-2">
                                        (${proposal.totalCost.toLocaleString()})
                                      </span>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Optionally link this review to a specific proposal
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="rating" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="rating"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rating *</FormLabel>
                        <FormControl>
                          <div className="space-y-3">
                            <div className="flex gap-1">
                              {[1, 2, 3, 4, 5].map((rating) => (
                                <button
                                  key={rating}
                                  type="button"
                                  onClick={() => field.onChange(rating)}
                                  onMouseEnter={() => setHoveredRating(rating)}
                                  onMouseLeave={() => setHoveredRating(0)}
                                  className="p-1 transition-transform hover:scale-110"
                                >
                                  <Star
                                    className={`h-10 w-10 transition-colors ${
                                      rating <= (hoveredRating || selectedRating)
                                        ? 'fill-yellow-400 text-yellow-400'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                </button>
                              ))}
                            </div>
                            {selectedRating > 0 && (
                              <div className="text-sm text-muted-foreground">
                                {selectedRating === 5 &&
                                  '⭐ Outstanding performance - Exceeded expectations'}
                                {selectedRating === 4 &&
                                  '⭐ Good performance - Met most expectations'}
                                {selectedRating === 3 &&
                                  '⭐ Average performance - Met basic requirements'}
                                {selectedRating === 2 &&
                                  '⭐ Below average - Some significant issues'}
                                {selectedRating === 1 &&
                                  '⭐ Poor performance - Major problems encountered'}
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormDescription>
                          Rate your overall experience working with this vendor
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="comment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Review Comment *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Share your detailed feedback about working with this vendor. Include information about their service quality, communication, timeliness, and overall experience..."
                            className="min-h-[150px] resize-y"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Provide a comprehensive review of your experience (minimum 10 characters)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="feedback" className="space-y-6">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <FormLabel>Strengths</FormLabel>
                      <Badge variant="default">{form.watch('strengths').length} items</Badge>
                    </div>
                    <div className="flex gap-2 mb-3">
                      <Input
                        placeholder="Add a strength (e.g., 'Excellent communication')"
                        value={newStrength}
                        onChange={(e) => setNewStrength(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addStrength()
                          }
                        }}
                      />
                      <Button type="button" onClick={addStrength} disabled={!newStrength.trim()}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {form.watch('strengths').map((strength, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950/20 rounded-md"
                        >
                          <span className="flex-1 text-sm">{strength}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeStrength(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {form.watch('strengths').length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4 border-2 border-dashed rounded-lg">
                          No strengths added yet. Click "Add" to highlight what this vendor did
                          well.
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <FormLabel>Areas for Improvement</FormLabel>
                      <Badge variant="secondary">{form.watch('improvements').length} items</Badge>
                    </div>
                    <div className="flex gap-2 mb-3">
                      <Input
                        placeholder="Add an area for improvement (e.g., 'Faster response time')"
                        value={newImprovement}
                        onChange={(e) => setNewImprovement(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addImprovement()
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={addImprovement}
                        disabled={!newImprovement.trim()}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {form.watch('improvements').map((improvement, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-md"
                        >
                          <span className="flex-1 text-sm">{improvement}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeImprovement(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      {form.watch('improvements').length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4 border-2 border-dashed rounded-lg">
                          No improvements added yet. Add constructive feedback for areas the vendor
                          could enhance.
                        </p>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="reviewer" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="reviewerName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Your Name *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                              <Input placeholder="John Doe" className="pl-10" {...field} />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="reviewerRole"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Your Role *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="L&D Manager, Director of Training, etc."
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Your position or role in the organization
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex gap-2 pt-6 border-t">
                <Button type="submit" disabled={createReview.isPending}>
                  {createReview.isPending ? 'Creating Review...' : 'Create Review'}
                </Button>
                <Link to="/reviews">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
