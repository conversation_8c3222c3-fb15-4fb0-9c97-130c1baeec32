import {
  Alert<PERSON><PERSON>gle,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Clock,
  Flag,
  RotateCcw,
  Timer,
  XCircle,
} from 'lucide-react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { Luminar<PERSON>lert as <PERSON><PERSON>, LuminarAlertDescription as AlertDescription } from '@luminar/shared-ui'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { LuminarCard as Card, LuminarCardContent as CardContent, LuminarCardHeader as CardHeader, LuminarCardTitle as CardTitle } from '@luminar/shared-ui'
import { LuminarCheckbox as Checkbox } from '@luminar/shared-ui'
import { LuminarLabel as Label } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { LuminarRadioGroup as RadioGroup, LuminarRadioGroupItem as RadioGroupItem } from '@luminar/shared-ui'
import { LuminarTextarea as Textarea } from '@luminar/shared-ui'
import { useAuthStore } from '@/lib/stores/auth.store'
import { useTrainingStore } from '@/lib/stores/training.store'

interface Question {
  id: string
  text: string
  type: 'multiple_choice' | 'multiple_select' | 'text' | 'true_false'
  options?: string[]
  correctAnswer?: string | string[]
  points: number
  explanation?: string
}

interface Assessment {
  id: string
  courseId: string
  title: string
  description: string
  timeLimit: number // in minutes
  passingScore: number
  maxAttempts: number
  questions: Question[]
  randomizeQuestions: boolean
  showResultsImmediately: boolean
  allowReview: boolean
}

interface AssessmentAttempt {
  id: string
  assessmentId: string
  userId: string
  answers: Record<string, any>
  score: number
  startedAt: string
  completedAt?: string
  timeSpent: number
  status: 'in_progress' | 'completed' | 'abandoned'
}

interface AssessmentFormProps {
  assessmentId: string
  onComplete?: (score: number) => void
  onCancel?: () => void
}

export const AssessmentForm: React.FC<AssessmentFormProps> = ({
  assessmentId,
  onComplete,
  onCancel,
}) => {
  const { user } = useAuthStore()
  const { assessments, fetchAssessment, submitAssessment, saveAssessmentProgress } =
    useTrainingStore()

  const [assessment, setAssessment] = useState<Assessment | null>(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [timeRemaining, setTimeRemaining] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [score, setScore] = useState<number>(0)
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<string>>(new Set())
  const [startTime] = useState<Date>(new Date())
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    loadAssessment()
    return () => {
      if (autoSaveTimer) {
        clearInterval(autoSaveTimer)
      }
    }
  }, [assessmentId])

  useEffect(() => {
    if (assessment && timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            handleAutoSubmit()
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [assessment, timeRemaining])

  useEffect(() => {
    // Auto-save progress every 30 seconds
    if (assessment && !showResults) {
      const timer = setInterval(() => {
        saveProgress()
      }, 30000)
      setAutoSaveTimer(timer)

      return () => clearInterval(timer)
    }
  }, [assessment, answers, showResults])

  const loadAssessment = async () => {
    try {
      const assessmentData = await fetchAssessment(assessmentId)
      setAssessment(assessmentData)
      setTimeRemaining(assessmentData.timeLimit * 60) // Convert to seconds
    } catch (error) {
      console.error('Error loading assessment:', error)
      toast.error('Failed to load assessment')
    }
  }

  const saveProgress = async () => {
    if (!assessment || showResults) return

    try {
      await saveAssessmentProgress({
        assessmentId,
        answers,
        currentQuestionIndex,
        flaggedQuestions: Array.from(flaggedQuestions),
        timeSpent: Math.floor((new Date().getTime() - startTime.getTime()) / 1000),
      })
    } catch (error) {
      console.error('Error saving progress:', error)
    }
  }

  const handleAnswerChange = (questionId: string, value: any) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }))
  }

  const handleFlagQuestion = (questionId: string) => {
    setFlaggedQuestions((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(questionId)) {
        newSet.delete(questionId)
      } else {
        newSet.add(questionId)
      }
      return newSet
    })
  }

  const handleSubmit = async () => {
    if (!assessment) return

    try {
      setIsSubmitting(true)

      const result = await submitAssessment({
        assessmentId,
        answers,
        timeSpent: Math.floor((new Date().getTime() - startTime.getTime()) / 1000),
      })

      setScore(result.score)
      setShowResults(true)

      if (onComplete) {
        onComplete(result.score)
      }

      toast.success(`Assessment completed! Score: ${result.score}%`)
    } catch (error) {
      console.error('Error submitting assessment:', error)
      toast.error('Failed to submit assessment')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAutoSubmit = async () => {
    toast.warning('Time expired! Assessment submitted automatically.')
    await handleSubmit()
  }

  const navigateToQuestion = (index: number) => {
    if (index >= 0 && index < (assessment?.questions.length || 0)) {
      setCurrentQuestionIndex(index)
    }
  }

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const getAnsweredCount = () => {
    return Object.keys(answers).length
  }

  const getCompletionPercentage = () => {
    if (!assessment) return 0
    return Math.round((getAnsweredCount() / assessment.questions.length) * 100)
  }

  const renderQuestion = (question: Question) => {
    const currentAnswer = answers[question.id]

    switch (question.type) {
      case 'multiple_choice':
      case 'true_false':
        return (
          <RadioGroup
            value={currentAnswer || ''}
            onValueChange={(value) => handleAnswerChange(question.id, value)}
          >
            {question.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${question.id}-${index}`} />
                <Label htmlFor={`${question.id}-${index}`} className="flex-1 cursor-pointer">
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )

      case 'multiple_select':
        return (
          <div className="space-y-2">
            {question.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  id={`${question.id}-${index}`}
                  checked={currentAnswer?.includes?.(option) || false}
                  onCheckedChange={(checked) => {
                    const currentAnswers = currentAnswer || []
                    if (checked) {
                      handleAnswerChange(question.id, [...currentAnswers, option])
                    } else {
                      handleAnswerChange(
                        question.id,
                        currentAnswers.filter((a: string) => a !== option)
                      )
                    }
                  }}
                />
                <Label htmlFor={`${question.id}-${index}`} className="flex-1 cursor-pointer">
                  {option}
                </Label>
              </div>
            ))}
          </div>
        )

      case 'text':
        return (
          <Textarea
            placeholder="Enter your answer here..."
            value={currentAnswer || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            rows={4}
          />
        )

      default:
        return null
    }
  }

  const renderResults = () => {
    if (!assessment || !showResults) return null

    const passed = score >= assessment.passingScore
    const totalQuestions = assessment.questions.length
    const correctAnswers = Math.round((score / 100) * totalQuestions)

    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6 text-center">
            <div
              className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                passed ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
              }`}
            >
              {passed ? <CheckCircle className="w-8 h-8" /> : <XCircle className="w-8 h-8" />}
            </div>

            <h2 className="text-2xl font-bold mb-2">
              {passed ? 'Congratulations!' : 'Assessment Not Passed'}
            </h2>

            <p className="text-gray-600 mb-4">
              {passed
                ? 'You have successfully passed the assessment!'
                : `You need ${assessment.passingScore}% to pass. You can retake the assessment.`}
            </p>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{score}%</div>
                <div className="text-sm text-gray-600">Your Score</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {correctAnswers}/{totalQuestions}
                </div>
                <div className="text-sm text-gray-600">Correct Answers</div>
              </div>
            </div>

            <Progress value={score} className="mb-4" />

            <div className="flex gap-2 justify-center">
              {assessment.allowReview && (
                <Button variant="outline" onClick={() => setShowResults(false)}>
                  Review Answers
                </Button>
              )}
              <Button onClick={onCancel}>Continue Learning</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!assessment) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (showResults) {
    return renderResults()
  }

  const currentQuestion = assessment.questions[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === assessment.questions.length - 1
  const isFirstQuestion = currentQuestionIndex === 0

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-2xl font-bold">{assessment.title}</h1>
              <p className="text-gray-600">{assessment.description}</p>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 text-lg font-semibold">
                <Timer className="h-5 w-5" />
                <span className={timeRemaining < 300 ? 'text-red-600' : 'text-gray-700'}>
                  {formatTime(timeRemaining)}
                </span>
              </div>
              <p className="text-sm text-gray-600">Time Remaining</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>
                Progress: {getAnsweredCount()} of {assessment.questions.length} answered
              </span>
              <span>{getCompletionPercentage()}% complete</span>
            </div>
            <Progress value={getCompletionPercentage()} />
          </div>
        </CardContent>
      </Card>

      {/* Question Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            {assessment.questions.map((_, index) => (
              <Button
                key={index}
                variant={index === currentQuestionIndex ? 'default' : 'outline'}
                size="sm"
                className={`relative ${
                  answers[assessment.questions[index].id] ? 'bg-green-100 border-green-300' : ''
                }`}
                onClick={() => navigateToQuestion(index)}
              >
                {index + 1}
                {flaggedQuestions.has(assessment.questions[index].id) && (
                  <Flag className="absolute -top-1 -right-1 h-3 w-3 text-red-500" />
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Question */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <CardTitle className="flex items-center gap-2">
                Question {currentQuestionIndex + 1} of {assessment.questions.length}
                <Badge variant="outline">{currentQuestion.points} points</Badge>
              </CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleFlagQuestion(currentQuestion.id)}
              className={flaggedQuestions.has(currentQuestion.id) ? 'text-red-600' : ''}
            >
              <Flag className="h-4 w-4" />
              {flaggedQuestions.has(currentQuestion.id) ? 'Unflag' : 'Flag'}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="prose max-w-none">
            <p className="text-lg">{currentQuestion.text}</p>
          </div>

          <div className="space-y-4">{renderQuestion(currentQuestion)}</div>

          {timeRemaining < 300 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>Warning: Less than 5 minutes remaining!</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => navigateToQuestion(currentQuestionIndex - 1)}
              disabled={isFirstQuestion}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="flex gap-2">
              <Button variant="outline" onClick={saveProgress}>
                Save Progress
              </Button>
              {onCancel && (
                <Button variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
            </div>

            {isLastQuestion ? (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? (
                  <>
                    <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Assessment'
                )}
              </Button>
            ) : (
              <Button onClick={() => navigateToQuestion(currentQuestionIndex + 1)}>
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assessment Info */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Time Limit: {assessment.timeLimit} minutes</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              <span>Passing Score: {assessment.passingScore}%</span>
            </div>
            <div className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              <span>Max Attempts: {assessment.maxAttempts}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
