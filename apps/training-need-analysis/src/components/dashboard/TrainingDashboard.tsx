import { format } from 'date-fns'
import { Award, BarChart3, BookOpen, Calendar, Target, TrendingUp, User, Users } from 'lucide-react'
import type React from 'react'
import { useEffect, useState } from 'react'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { LuminarCard as Card, LuminarCardContent as CardContent, LuminarCardHeader as CardHeader, LuminarCardTitle as CardTitle } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { LuminarTabs as Tabs, <PERSON>minar<PERSON>absContent as TabsContent, <PERSON>minar<PERSON><PERSON>s<PERSON><PERSON> as TabsList, LuminarTabsTrigger as TabsTrigger } from '@luminar/shared-ui'
import { useAuthStore } from '@/lib/stores/auth.store'
import { useTrainingStore } from '@/lib/stores/training.store'

interface DashboardStats {
  totalCourses: number
  completedCourses: number
  inProgressCourses: number
  totalUsers: number
  averageScore: number
  completionRate: number
  upcomingDeadlines: number
  skillsGained: number
}

export const TrainingDashboard: React.FC = () => {
  const { user } = useAuthStore()
  const {
    courses,
    enrollments,
    assessments,
    analytics,
    fetchCourses,
    fetchEnrollments,
    fetchAssessments,
    fetchAnalytics,
  } = useTrainingStore()

  const [stats, setStats] = useState<DashboardStats>({
    totalCourses: 0,
    completedCourses: 0,
    inProgressCourses: 0,
    totalUsers: 0,
    averageScore: 0,
    completionRate: 0,
    upcomingDeadlines: 0,
    skillsGained: 0,
  })

  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      await Promise.all([fetchCourses(), fetchEnrollments(), fetchAssessments(), fetchAnalytics()])
      calculateStats()
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateStats = () => {
    const userEnrollments = enrollments.filter((e) => e.userId === user?.id)
    const completedEnrollments = userEnrollments.filter((e) => e.status === 'completed')
    const inProgressEnrollments = userEnrollments.filter((e) => e.status === 'in_progress')

    const userAssessments = assessments.filter((a) => a.userId === user?.id)
    const averageScore =
      userAssessments.length > 0
        ? userAssessments.reduce((sum, a) => sum + a.score, 0) / userAssessments.length
        : 0

    const upcomingDeadlines = userEnrollments.filter((e) => {
      const deadline = new Date(e.deadline)
      const now = new Date()
      const diffTime = deadline.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays <= 7 && diffDays > 0
    }).length

    const skillsGained = completedEnrollments.reduce((total, enrollment) => {
      const course = courses.find((c) => c.id === enrollment.courseId)
      return total + (course?.skills?.length || 0)
    }, 0)

    setStats({
      totalCourses: courses.length,
      completedCourses: completedEnrollments.length,
      inProgressCourses: inProgressEnrollments.length,
      totalUsers: new Set(enrollments.map((e) => e.userId)).size,
      averageScore: Math.round(averageScore),
      completionRate:
        userEnrollments.length > 0
          ? Math.round((completedEnrollments.length / userEnrollments.length) * 100)
          : 0,
      upcomingDeadlines,
      skillsGained,
    })
  }

  const getRecentActivity = () => {
    const userEnrollments = enrollments
      .filter((e) => e.userId === user?.id)
      .sort((a, b) => new Date(b.enrolledAt).getTime() - new Date(a.enrolledAt).getTime())
      .slice(0, 5)

    return userEnrollments.map((enrollment) => {
      const course = courses.find((c) => c.id === enrollment.courseId)
      return {
        id: enrollment.id,
        title: course?.title || 'Unknown Course',
        status: enrollment.status,
        progress: enrollment.progress,
        enrolledAt: enrollment.enrolledAt,
        deadline: enrollment.deadline,
      }
    })
  }

  const getUpcomingDeadlines = () => {
    const userEnrollments = enrollments
      .filter((e) => e.userId === user?.id && e.status === 'in_progress')
      .filter((e) => {
        const deadline = new Date(e.deadline)
        const now = new Date()
        return deadline > now
      })
      .sort((a, b) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime())
      .slice(0, 5)

    return userEnrollments.map((enrollment) => {
      const course = courses.find((c) => c.id === enrollment.courseId)
      const deadline = new Date(enrollment.deadline)
      const now = new Date()
      const diffTime = deadline.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      return {
        id: enrollment.id,
        title: course?.title || 'Unknown Course',
        deadline: enrollment.deadline,
        daysRemaining: diffDays,
        progress: enrollment.progress,
        isUrgent: diffDays <= 3,
      }
    })
  }

  const getSkillProgress = () => {
    const userEnrollments = enrollments.filter((e) => e.userId === user?.id)
    const skillMap = new Map<string, { total: number; completed: number }>()

    userEnrollments.forEach((enrollment) => {
      const course = courses.find((c) => c.id === enrollment.courseId)
      if (course?.skills) {
        course.skills.forEach((skill) => {
          if (!skillMap.has(skill)) {
            skillMap.set(skill, { total: 0, completed: 0 })
          }
          const skillData = skillMap.get(skill)!
          skillData.total++
          if (enrollment.status === 'completed') {
            skillData.completed++
          }
        })
      }
    })

    return Array.from(skillMap.entries())
      .map(([skill, data]) => ({
        skill,
        progress: Math.round((data.completed / data.total) * 100),
        completed: data.completed,
        total: data.total,
      }))
      .sort((a, b) => b.progress - a.progress)
      .slice(0, 8)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Training Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.firstName}!</p>
        </div>
        <Button onClick={loadDashboardData} disabled={isLoading}>
          Refresh Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Courses Enrolled</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCourses}</div>
            <p className="text-xs text-muted-foreground">{stats.completedCourses} completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completionRate}%</div>
            <p className="text-xs text-muted-foreground">{stats.inProgressCourses} in progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageScore}%</div>
            <p className="text-xs text-muted-foreground">Across all assessments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Skills Gained</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.skillsGained}</div>
            <p className="text-xs text-muted-foreground">
              {stats.upcomingDeadlines} deadlines upcoming
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getRecentActivity().map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex-1">
                        <p className="font-medium">{activity.title}</p>
                        <p className="text-sm text-gray-600">
                          Enrolled {format(new Date(activity.enrolledAt), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={activity.status === 'completed' ? 'default' : 'secondary'}>
                          {activity.status}
                        </Badge>
                        <div className="text-sm text-gray-600">{activity.progress}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Deadlines */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Upcoming Deadlines
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getUpcomingDeadlines().map((deadline) => (
                    <div
                      key={deadline.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex-1">
                        <p className="font-medium">{deadline.title}</p>
                        <p className="text-sm text-gray-600">
                          Due {format(new Date(deadline.deadline), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={deadline.isUrgent ? 'destructive' : 'outline'}>
                          {deadline.daysRemaining} days
                        </Badge>
                        <Progress value={deadline.progress} className="w-16" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Course Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {enrollments
                  .filter((e) => e.userId === user?.id && e.status === 'in_progress')
                  .map((enrollment) => {
                    const course = courses.find((c) => c.id === enrollment.courseId)
                    return (
                      <div key={enrollment.id} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{course?.title}</p>
                            <p className="text-sm text-gray-600">{course?.category}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{enrollment.progress}%</p>
                            <p className="text-xs text-gray-600">{course?.duration} hours</p>
                          </div>
                        </div>
                        <Progress value={enrollment.progress} className="h-2" />
                        <div className="flex justify-between text-xs text-gray-600">
                          <span>Started {format(new Date(enrollment.enrolledAt), 'MMM d')}</span>
                          <span>Due {format(new Date(enrollment.deadline), 'MMM d')}</span>
                        </div>
                      </div>
                    )
                  })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Skills Development</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getSkillProgress().map((skill) => (
                  <div key={skill.skill} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <p className="font-medium">{skill.skill}</p>
                      <p className="text-sm text-gray-600">
                        {skill.completed}/{skill.total} courses
                      </p>
                    </div>
                    <Progress value={skill.progress} className="h-2" />
                    <p className="text-xs text-gray-600">{skill.progress}% complete</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Learning Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Learning Hours</span>
                    <span className="font-bold">
                      {enrollments
                        .filter((e) => e.userId === user?.id && e.status === 'completed')
                        .reduce((total, enrollment) => {
                          const course = courses.find((c) => c.id === enrollment.courseId)
                          return total + (course?.duration || 0)
                        }, 0)}{' '}
                      hours
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Certificates Earned</span>
                    <span className="font-bold">{stats.completedCourses}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Average Assessment Score</span>
                    <span className="font-bold">{stats.averageScore}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Learning Streak</span>
                    <span className="font-bold">7 days</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Course Completion Rate</span>
                      <span>{stats.completionRate}%</span>
                    </div>
                    <Progress value={stats.completionRate} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Average Score</span>
                      <span>{stats.averageScore}%</span>
                    </div>
                    <Progress value={stats.averageScore} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Skills Mastery</span>
                      <span>75%</span>
                    </div>
                    <Progress value={75} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
