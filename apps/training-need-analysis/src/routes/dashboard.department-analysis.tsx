import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { LuminarBarChart as Chart } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { fetchDepartmentMetrics, fetchSkillGaps } from '../utils/analytics'

export const Route = createFileRoute('/dashboard/department-analysis')({
  loader: async () => {
    const [departmentMetrics, skillGaps] = await Promise.all([
      fetchDepartmentMetrics(),
      fetchSkillGaps(),
    ])
    return { departmentMetrics, skillGaps }
  },
  component: DepartmentAnalysis,
})

function DepartmentAnalysis() {
  const { departmentMetrics, skillGaps } = Route.useLoaderData()
  const [isExportingAnalysis, setIsExportingAnalysis] = useState(false)
  const [isSchedulingReview, setIsSchedulingReview] = useState(false)
  const [generatingReports, setGeneratingReports] = useState<Set<string>>(new Set())

  const gapsByDepartment = skillGaps.reduce(
    (acc, gap) => {
      acc[gap.departmentId] = (acc[gap.departmentId] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  const topDepartmentsByGaps = Object.entries(gapsByDepartment)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)

  const handleExportAnalysis = async () => {
    setIsExportingAnalysis(true)
    try {
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'department-analysis',
          format: 'pdf',
          data: {
            totalDepartments: departmentMetrics.length,
            averageSkillLevel:
              departmentMetrics.reduce((sum, dept) => sum + dept.averageSkillLevel, 0) /
              departmentMetrics.length,
            totalBudgetUtilization: departmentMetrics.reduce(
              (sum, dept) => sum + dept.budgetUsed,
              0
            ),
            departmentBreakdown: departmentMetrics.map((dept) => ({
              name: dept.departmentName,
              employees: dept.employeeCount,
              skillLevel: dept.averageSkillLevel,
              gaps: gapsByDepartment[dept.departmentId] || 0,
            })),
          },
        }),
      })

      const result = await response.json()
      if (result.success) {
        alert(`Department Analysis exported successfully! Report ID: ${result.reportId}`)
        console.log('Download URL:', result.downloadUrl)
      }
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    } finally {
      setIsExportingAnalysis(false)
    }
  }

  const handleScheduleReview = async () => {
    setIsSchedulingReview(true)
    try {
      // Simulate scheduling a review
      await new Promise((resolve) => setTimeout(resolve, 1000))
      alert(
        'Department review scheduled successfully!\n\nScheduled for: Next Monday 2:00 PM\nParticipants: Department heads and HR\nDuration: 2 hours'
      )
    } catch (error) {
      console.error('Scheduling failed:', error)
      alert('Failed to schedule review. Please try again.')
    } finally {
      setIsSchedulingReview(false)
    }
  }

  const handleGenerateReport = async (departmentId: string, departmentName: string) => {
    setGeneratingReports((prev) => new Set(prev).add(departmentId))
    try {
      const response = await fetch('/api/departments/generate-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          departmentId: departmentId,
          departmentName: departmentName,
          type: 'detailed-analysis',
        }),
      })

      const result = await response.json()
      if (result.success) {
        alert(
          `Report generation initiated for ${departmentName}!\n\nReport ID: ${result.reportId}\nEstimated completion: ${new Date(result.estimatedCompletion).toLocaleTimeString()}`
        )
        console.log('Report details:', result.report)
      }
    } catch (error) {
      console.error('Report generation failed:', error)
      alert('Failed to generate report. Please try again.')
    } finally {
      setGeneratingReports((prev) => {
        const newSet = new Set(prev)
        newSet.delete(departmentId)
        return newSet
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Department Analysis</h2>
          <p className="text-gray-600 mt-1">
            Compare performance and training needs across departments
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportAnalysis} disabled={isExportingAnalysis}>
            {isExportingAnalysis ? 'Exporting...' : 'Export Analysis'}
          </Button>
          <Button onClick={handleScheduleReview} disabled={isSchedulingReview}>
            {isSchedulingReview ? 'Scheduling...' : 'Schedule Review'}
          </Button>
        </div>
      </div>

      {/* Department Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Departments</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{departmentMetrics.length}</p>
          <p className="text-sm text-gray-600 mt-1">Active departments</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Avg Skill Level</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">
            {departmentMetrics.length > 0
              ? (
                  departmentMetrics.reduce((sum, dept) => sum + dept.averageSkillLevel, 0) /
                  departmentMetrics.length
                ).toFixed(1)
              : '0'}
          </p>
          <p className="text-sm text-gray-600 mt-1">Across all departments</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Training Budget Used</h3>
          <p className="text-3xl font-bold text-purple-600 mt-2">
            {departmentMetrics.length > 0
              ? Math.round(
                  (departmentMetrics.reduce((sum, dept) => sum + dept.budgetUtilized, 0) /
                    departmentMetrics.reduce((sum, dept) => sum + dept.budgetAllocated, 0)) *
                    100
                )
              : 0}
            %
          </p>
          <p className="text-sm text-gray-600 mt-1">Of allocated budget</p>
        </div>
      </div>

      {/* Department Comparison Chart */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Department Performance Comparison
        </h3>
        <Chart
          type="bar"
          data={{
            labels: departmentMetrics.map((dept) => `Dept ${dept.departmentId}`),
            values: departmentMetrics.map((dept) => dept.averageSkillLevel),
          }}
          height={300}
        />
      </div>

      {/* Top Departments by Skill Gaps */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Departments with Most Skill Gaps
        </h3>
        <div className="space-y-4">
          {topDepartmentsByGaps.map(([deptId, gapCount], index) => {
            const deptMetrics = departmentMetrics.find((d) => d.departmentId === deptId)
            return (
              <div
                key={deptId}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center text-sm font-medium text-red-800">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Department {deptId}</h4>
                    <p className="text-sm text-gray-600">
                      {gapCount} skill gaps - Avg level:{' '}
                      {deptMetrics?.averageSkillLevel.toFixed(1) || 'N/A'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="destructive">{gapCount} gaps</Badge>
                  <Button size="sm" variant="outline">
                    View Details
                  </Button>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Detailed Department Metrics */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Department Metrics</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employees
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Skill Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Skill Gaps
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Budget Utilization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Training Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {departmentMetrics.map((dept) => {
                const deptGaps = gapsByDepartment[dept.departmentId] || 0
                const budgetUtilization = (dept.budgetUtilized / dept.budgetAllocated) * 100

                return (
                  <tr key={dept.departmentId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Department {dept.departmentId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {dept.employeeCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center space-x-2">
                        <span>{dept.averageSkillLevel.toFixed(1)}</span>
                        <Progress value={(dept.averageSkillLevel / 3) * 100} className="h-2 w-16" />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <Badge
                        variant={
                          deptGaps > 5 ? 'destructive' : deptGaps > 2 ? 'default' : 'secondary'
                        }
                      >
                        {deptGaps}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center space-x-2">
                        <span>{budgetUtilization.toFixed(0)}%</span>
                        <Progress value={budgetUtilization} className="h-2 w-16" />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center space-x-2">
                        <span>{(dept.trainingProgress || 0).toFixed(0)}%</span>
                        <Progress value={dept.trainingProgress || 0} className="h-2 w-16" />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-4">
                        View Details
                      </button>
                      <button
                        className="text-gray-600 hover:text-gray-900 disabled:opacity-50"
                        onClick={() => handleGenerateReport(dept.departmentId, dept.departmentName)}
                        disabled={generatingReports.has(dept.departmentId)}
                      >
                        {generatingReports.has(dept.departmentId)
                          ? 'Generating...'
                          : 'Generate Report'}
                      </button>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
