import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { LuminarBarChart as Chart } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { fetchLearningActivities, fetchTrainingMetrics } from '../utils/analytics'

export const Route = createFileRoute('/dashboard/training-metrics')({
  loader: async () => {
    const [metrics, activities] = await Promise.all([
      fetchTrainingMetrics(),
      fetchLearningActivities({ limit: 50 }),
    ])
    return { metrics, activities }
  },
  component: TrainingMetrics,
})

function TrainingMetrics() {
  const { metrics, activities } = Route.useLoaderData()
  const [isExportingMetrics, setIsExportingMetrics] = useState(false)
  const [isViewingReport, setIsViewingReport] = useState(false)

  const completedActivities = activities.filter((a) => a.status === 'completed')
  const inProgressActivities = activities.filter((a) => a.status === 'in-progress')
  const notStartedActivities = activities.filter((a) => a.status === 'not-started')

  const completionRate =
    activities.length > 0 ? Math.round((completedActivities.length / activities.length) * 100) : 0

  const averageScore =
    completedActivities.length > 0
      ? Math.round(
          completedActivities.reduce((sum, activity) => sum + (activity.score || 0), 0) /
            completedActivities.length
        )
      : 0

  const handleExportMetrics = async () => {
    setIsExportingMetrics(true)
    try {
      const response = await fetch('/api/analytics/export-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'training-metrics',
          format: 'xlsx',
          data: {
            roi: metrics.roi,
            totalInvestment: metrics.totalInvestment,
            completionRate: completionRate,
            averageScore: averageScore,
            activities: activities.length,
            completedActivities: completedActivities.length,
          },
        }),
      })

      const result = await response.json()
      if (result.success) {
        alert(
          `Training metrics exported successfully! Export ID: ${result.exportId}\nFormat: ${result.format.toUpperCase()}`
        )
        console.log('Download URL:', result.downloadUrl)
      }
    } catch (error) {
      console.error('Metrics export failed:', error)
      alert('Failed to export metrics. Please try again.')
    } finally {
      setIsExportingMetrics(false)
    }
  }

  const handleViewDetailedReport = async () => {
    setIsViewingReport(true)
    try {
      const response = await fetch('/api/reports/detailed/training-metrics')
      const result = await response.json()

      if (result) {
        // In a real app, you might navigate to a detailed report page
        // For now, we'll show an alert with report info
        alert(
          `Detailed Training Metrics Report Generated!\n\nReport ID: ${result.id}\nGenerated: ${new Date(result.generatedAt).toLocaleString()}\nSections: ${result.sections.length}`
        )
        console.log('Detailed Report:', result)

        // You could also open in a new window or navigate to a report page
        // window.open(`/reports/${result.id}`, '_blank')
      }
    } catch (error) {
      console.error('Failed to generate detailed report:', error)
      alert('Failed to generate detailed report. Please try again.')
    } finally {
      setIsViewingReport(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Training Metrics</h2>
          <p className="text-gray-600 mt-1">Monitor training effectiveness and learning progress</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportMetrics} disabled={isExportingMetrics}>
            {isExportingMetrics ? 'Exporting...' : 'Export Metrics'}
          </Button>
          <Button onClick={handleViewDetailedReport} disabled={isViewingReport}>
            {isViewingReport ? 'Generating...' : 'View Detailed Report'}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Training ROI</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{metrics.roi.toFixed(1)}x</p>
          <p className="text-sm text-gray-600 mt-1">Return on investment</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Investment</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">
            ${metrics.totalInvestment.toLocaleString()}
          </p>
          <p className="text-sm text-gray-600 mt-1">This fiscal year</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Completion Rate</h3>
          <p className="text-3xl font-bold text-purple-600 mt-2">{completionRate}%</p>
          <p className="text-sm text-gray-600 mt-1">Overall completion</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Average Score</h3>
          <p className="text-3xl font-bold text-orange-600 mt-2">{averageScore}%</p>
          <p className="text-sm text-gray-600 mt-1">Assessment average</p>
        </div>
      </div>

      {/* Training Progress Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Training Status Distribution</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Completed</span>
              <span className="text-sm text-gray-600">{completedActivities.length}</span>
            </div>
            <Progress
              value={(completedActivities.length / activities.length) * 100}
              className="h-2"
            />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">In Progress</span>
              <span className="text-sm text-gray-600">{inProgressActivities.length}</span>
            </div>
            <Progress
              value={(inProgressActivities.length / activities.length) * 100}
              className="h-2"
            />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Not Started</span>
              <span className="text-sm text-gray-600">{notStartedActivities.length}</span>
            </div>
            <Progress
              value={(notStartedActivities.length / activities.length) * 100}
              className="h-2"
            />
          </div>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Training Trend</h3>
          <Chart
            type="line"
            data={{
              labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
              values: [45, 52, 48, 61, 55, 67],
            }}
            height={200}
          />
        </div>
      </div>

      {/* Training Effectiveness */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Training Effectiveness by Category
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">85%</div>
            <div className="text-sm text-gray-600 mb-2">Technical Skills</div>
            <Progress value={85} className="h-2" />
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">78%</div>
            <div className="text-sm text-gray-600 mb-2">Soft Skills</div>
            <Progress value={78} className="h-2" />
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">92%</div>
            <div className="text-sm text-gray-600 mb-2">Compliance</div>
            <Progress value={92} className="h-2" />
          </div>
        </div>
      </div>

      {/* Recent Training Activities */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Recent Training Activities</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Course
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Activity
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {activities.slice(0, 15).map((activity) => (
                <tr key={activity.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Employee {activity.employeeId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Course {activity.courseId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center space-x-2">
                      <Progress value={activity.progress} className="h-2 w-20" />
                      <span>{activity.progress}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {activity.score ? `${activity.score}%` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={
                        activity.status === 'completed'
                          ? 'default'
                          : activity.status === 'in-progress'
                            ? 'secondary'
                            : 'outline'
                      }
                    >
                      {activity.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(activity.lastActivity).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
