import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { fetchAssessments } from '../utils/assessments'

export const Route = createFileRoute('/assessments/')({
  loader: async () => fetchAssessments(),
  component: AssessmentsOverview,
})

function AssessmentsOverview() {
  const assessments = Route.useLoaderData()
  const [isCreatingAssessment, setIsCreatingAssessment] = useState(false)

  const activeAssessments = assessments.filter((a) => a.status === 'active')
  const completedAssessments = assessments.filter((a) => a.status === 'completed')
  const draftAssessments = assessments.filter((a) => a.status === 'draft')

  const totalResponses = assessments.reduce(
    (sum, assessment) => sum + assessment.responses.length,
    0
  )
  const avgResponseRate =
    assessments.length > 0
      ? Math.round((totalResponses / (assessments.length * 10)) * 100) // Assuming 10 avg employees per assessment
      : 0

  const handleCreateNewAssessment = async () => {
    setIsCreatingAssessment(true)
    try {
      const response = await fetch('/api/assessments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'New Skills Assessment',
          description: 'Assessment created from dashboard',
          status: 'draft',
          skillsAssessed: [],
          createdBy: 'current-user',
        }),
      })

      const result = await response.json()
      if (result.success) {
        alert(
          `New assessment created successfully!\n\nAssessment ID: ${result.assessment.id}\nTitle: ${result.assessment.title}\nStatus: ${result.assessment.status}`
        )
        // In a real app, you would navigate to the new assessment or refresh the data
        window.location.reload()
      }
    } catch (error) {
      console.error('Failed to create assessment:', error)
      alert('Failed to create new assessment. Please try again.')
    } finally {
      setIsCreatingAssessment(false)
    }
  }

  const handleViewTemplates = () => {
    // Navigate to templates page or show templates modal
    alert(
      'Assessment Templates:\n\n1. Technical Skills Assessment\n2. Leadership Competency Assessment\n3. Soft Skills Evaluation\n4. Department-Specific Assessment\n5. Role-Based Skills Assessment\n\nSelect a template to create a new assessment quickly!'
    )
  }

  const handleViewDetails = (assessmentId: string) => {
    // Navigate to assessment details page
    window.open(`/assessments/${assessmentId}`, '_blank')
  }

  return (
    <div className="space-y-6">
      {/* Welcome Message */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Skills Assessment Center</h2>
        <p className="text-gray-600 mb-4">
          Systematically evaluate employee skills to identify training needs and track development
          progress. Create targeted assessments, analyze results, and build data-driven learning
          strategies.
        </p>
        <div className="flex gap-3">
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={handleCreateNewAssessment}
            disabled={isCreatingAssessment}
          >
            {isCreatingAssessment ? 'Creating...' : 'Create New Assessment'}
          </Button>
          <Button variant="outline" onClick={handleViewTemplates}>
            View Assessment Templates
          </Button>
        </div>
      </div>

      {/* Assessment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Assessments"
          value={assessments.length}
          icon="📋"
          description="All time"
        />
        <StatCard
          title="Active Assessments"
          value={activeAssessments.length}
          icon="🔄"
          description="Currently running"
        />
        <StatCard
          title="Total Responses"
          value={totalResponses}
          icon="✍️"
          description="Completed assessments"
        />
        <StatCard
          title="Response Rate"
          value={`${avgResponseRate}%`}
          icon="📊"
          description="Average completion"
        />
      </div>

      {/* Assessment Status Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Active Assessments */}
        <AssessmentStatusCard
          title="Active Assessments"
          assessments={activeAssessments}
          statusColor="green"
          icon="🟢"
        />

        {/* Draft Assessments */}
        <AssessmentStatusCard
          title="Draft Assessments"
          assessments={draftAssessments}
          statusColor="yellow"
          icon="📝"
        />

        {/* Completed Assessments */}
        <AssessmentStatusCard
          title="Recently Completed"
          assessments={completedAssessments.slice(0, 5)}
          statusColor="blue"
          icon="✅"
        />
      </div>

      {/* Recent Assessment Activity */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Assessment Activity</h3>
        <div className="space-y-4">
          {assessments.slice(0, 5).map((assessment) => (
            <div
              key={assessment.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <AssessmentIcon status={assessment.status} />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{assessment.title}</h4>
                  <p className="text-sm text-gray-600">
                    {assessment.skillsAssessed.length} skills • {assessment.responses.length}{' '}
                    responses
                  </p>
                  <p className="text-xs text-gray-500">
                    Created {new Date(assessment.createdDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <StatusBadge status={assessment.status} />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewDetails(assessment.id)}
                >
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function StatCard({
  title,
  value,
  icon,
  description,
}: {
  title: string
  value: string | number
  icon: string
  description: string
}) {
  return (
    <div className="bg-white p-6 rounded-lg border">
      <div className="flex items-center">
        <span className="text-2xl mr-3">{icon}</span>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className="text-xs text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  )
}

function AssessmentStatusCard({
  title,
  assessments,
  statusColor,
  icon,
}: {
  title: string
  assessments: any[]
  statusColor: string
  icon: string
}) {
  return (
    <div className="bg-white rounded-lg border p-6">
      <div className="flex items-center mb-4">
        <span className="text-lg mr-2">{icon}</span>
        <h3 className="font-semibold text-gray-900">{title}</h3>
        <span className="ml-auto text-sm font-medium text-gray-600">{assessments.length}</span>
      </div>

      <div className="space-y-3">
        {assessments.length === 0 ? (
          <p className="text-sm text-gray-500 italic">No assessments found</p>
        ) : (
          assessments.slice(0, 3).map((assessment) => (
            <div key={assessment.id} className="border-l-2 border-gray-200 pl-3">
              <h4 className="text-sm font-medium text-gray-900 truncate">{assessment.title}</h4>
              <p className="text-xs text-gray-600">{assessment.responses.length} responses</p>
              {assessment.dueDate && (
                <p className="text-xs text-gray-500">
                  Due: {new Date(assessment.dueDate).toLocaleDateString()}
                </p>
              )}
            </div>
          ))
        )}

        {assessments.length > 3 && (
          <p className="text-xs text-gray-500">+{assessments.length - 3} more assessments</p>
        )}
      </div>
    </div>
  )
}

function AssessmentIcon({ status }: { status: string }) {
  const icons = {
    draft: '📝',
    active: '🔄',
    completed: '✅',
    archived: '📁',
  }

  return <span className="text-2xl">{icons[status as keyof typeof icons] || '📋'}</span>
}

function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
    active: { color: 'bg-green-100 text-green-800', label: 'Active' },
    completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' },
    archived: { color: 'bg-gray-100 text-gray-600', label: 'Archived' },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${config.color}`}
    >
      {config.label}
    </span>
  )
}
