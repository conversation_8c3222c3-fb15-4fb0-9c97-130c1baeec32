import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { fetchLearningPaths } from '../utils/training'

export const Route = createFileRoute('/training/learning-paths')({
  loader: async () => {
    const learningPaths = await fetchLearningPaths()
    return { learningPaths }
  },
  component: LearningPaths,
})

function LearningPaths() {
  const { learningPaths } = Route.useLoaderData()

  const activePaths = learningPaths.filter((path) => path.status === 'active')
  const totalCourses = learningPaths.reduce((sum, path) => sum + (path.courses?.length || 0), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Learning Paths</h2>
          <p className="text-gray-600 mt-1">
            Structured learning journeys to develop specific skills and competencies
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Create Custom Path</Button>
          <Button>Browse All Paths</Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Paths</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{learningPaths.length}</p>
          <p className="text-sm text-gray-600 mt-1">Available learning paths</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Active Paths</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{activePaths.length}</p>
          <p className="text-sm text-gray-600 mt-1">Currently available</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Courses</h3>
          <p className="text-3xl font-bold text-purple-600 mt-2">{totalCourses}</p>
          <p className="text-sm text-gray-600 mt-1">Across all paths</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Avg Duration</h3>
          <p className="text-3xl font-bold text-orange-600 mt-2">
            {learningPaths.length > 0
              ? Math.round(
                  learningPaths.reduce((sum, path) => sum + path.estimatedDuration, 0) /
                    learningPaths.length
                )
              : 0}
            h
          </p>
          <p className="text-sm text-gray-600 mt-1">Average completion time</p>
        </div>
      </div>

      {/* Featured Learning Paths */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Featured Learning Paths</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {learningPaths.slice(0, 6).map((path) => (
            <div key={path.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">{path.title}</h4>
                  <p className="text-sm text-gray-600 line-clamp-2">{path.description}</p>
                </div>
                <Badge
                  variant={
                    path.difficulty === 'beginner'
                      ? 'secondary'
                      : path.difficulty === 'intermediate'
                        ? 'default'
                        : 'destructive'
                  }
                >
                  {path.difficulty}
                </Badge>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Courses</span>
                  <span className="font-medium">{(path.courses || []).length}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Duration</span>
                  <span className="font-medium">{path.estimatedDuration}h</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Enrollments</span>
                  <span className="font-medium">{path.enrollments}</span>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center justify-between text-sm mb-1">
                  <span className="text-gray-600">Skills Covered</span>
                  <span className="text-gray-500">{(path.skillsAcquired || []).length}</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {(path.skillsAcquired || []).slice(0, 3).map((skill, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                  {(path.skillsAcquired || []).length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{(path.skillsAcquired || []).length - 3} more
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" className="flex-1">
                  Start Path
                </Button>
                <Button size="sm" variant="outline">
                  Preview
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* All Learning Paths Table */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">All Learning Paths</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Learning Path
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Difficulty
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Courses
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Enrollments
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {learningPaths.map((path) => (
                <tr key={path.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{path.title}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {path.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={
                        path.difficulty === 'beginner'
                          ? 'secondary'
                          : path.difficulty === 'intermediate'
                            ? 'default'
                            : 'destructive'
                      }
                    >
                      {path.difficulty}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(path.courses || []).length}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {path.estimatedDuration}h
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {path.enrollments}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant={path.status === 'active' ? 'default' : 'secondary'}>
                      {path.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Link
                      to="/training/learning-paths/$pathId"
                      params={{ pathId: path.id }}
                      className="text-blue-600 hover:text-blue-900 mr-4"
                    >
                      View
                    </Link>
                    <button className="text-gray-600 hover:text-gray-900">Enroll</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
