import { createFileRoute } from '@tanstack/react-router'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { fetchUserTrainingEnrollments } from '../utils/training'

export const Route = createFileRoute('/training/my-enrollments')({
  loader: async () => {
    // In a real app, you'd get the current user ID from auth context
    const currentUserId = 'emp-001'
    try {
      const enrollments = await fetchUserTrainingEnrollments(currentUserId)
      return { enrollments, currentUserId }
    } catch (error) {
      console.error('Failed to fetch enrollments:', error)
      return { enrollments: [], currentUserId }
    }
  },
  component: MyEnrollments,
})

function MyEnrollments() {
  const { enrollments } = Route.useLoaderData()

  const activeEnrollments = enrollments.filter((e) => e.status === 'active')
  const completedEnrollments = enrollments.filter((e) => e.status === 'completed')
  const upcomingEnrollments = enrollments.filter((e) => e.status === 'upcoming')

  const totalProgress =
    enrollments.length > 0
      ? Math.round(
          enrollments.reduce((sum, enrollment) => sum + enrollment.progress, 0) / enrollments.length
        )
      : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Training Enrollments</h2>
          <p className="text-gray-600 mt-1">
            Track your learning progress and manage your training schedule
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">View Certificates</Button>
          <Button>Browse Catalog</Button>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Enrollments</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{enrollments.length}</p>
          <p className="text-sm text-gray-600 mt-1">All time</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Active Courses</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{activeEnrollments.length}</p>
          <p className="text-sm text-gray-600 mt-1">In progress</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Completed</h3>
          <p className="text-3xl font-bold text-purple-600 mt-2">{completedEnrollments.length}</p>
          <p className="text-sm text-gray-600 mt-1">Finished courses</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Avg Progress</h3>
          <p className="text-3xl font-bold text-orange-600 mt-2">{totalProgress}%</p>
          <p className="text-sm text-gray-600 mt-1">Overall completion</p>
        </div>
      </div>

      {/* Active Enrollments */}
      {activeEnrollments.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Continue Learning</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {activeEnrollments.map((enrollment) => (
              <div
                key={enrollment.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">Course {enrollment.courseId}</h4>
                    <p className="text-sm text-gray-600">
                      Enrolled: {new Date(enrollment.enrollmentDate).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>

                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium">{enrollment.progress}%</span>
                  </div>
                  <Progress value={enrollment.progress} className="h-2" />
                </div>

                {enrollment.lastAccessed && (
                  <p className="text-xs text-gray-500 mb-3">
                    Last accessed: {new Date(enrollment.lastAccessed).toLocaleDateString()}
                  </p>
                )}

                <div className="flex gap-2">
                  <Button size="sm" className="flex-1">
                    Continue
                  </Button>
                  <Button size="sm" variant="outline">
                    Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upcoming Enrollments */}
      {upcomingEnrollments.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Courses</h3>
          <div className="space-y-4">
            {upcomingEnrollments.map((enrollment) => (
              <div
                key={enrollment.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <h4 className="font-medium text-gray-900">Course {enrollment.courseId}</h4>
                  <p className="text-sm text-gray-600">
                    Starts:{' '}
                    {enrollment.startDate
                      ? new Date(enrollment.startDate).toLocaleDateString()
                      : 'TBD'}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">Upcoming</Badge>
                  <Button size="sm" variant="outline">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* All Enrollments Table */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">All Enrollments</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Course
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Enrollment Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Completion Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {enrollments.map((enrollment) => (
                <tr key={enrollment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Course {enrollment.courseId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(enrollment.enrollmentDate).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center space-x-2">
                      <Progress value={enrollment.progress} className="h-2 w-20" />
                      <span>{enrollment.progress}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={
                        enrollment.status === 'completed'
                          ? 'default'
                          : enrollment.status === 'active'
                            ? 'secondary'
                            : 'outline'
                      }
                    >
                      {enrollment.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {enrollment.completionDate
                      ? new Date(enrollment.completionDate).toLocaleDateString()
                      : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {enrollment.finalScore ? `${enrollment.finalScore}%` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {enrollment.status === 'active' ? (
                      <button className="text-blue-600 hover:text-blue-900">Continue</button>
                    ) : enrollment.status === 'completed' ? (
                      <button className="text-green-600 hover:text-green-900">Certificate</button>
                    ) : (
                      <button className="text-gray-600 hover:text-gray-900">View</button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
