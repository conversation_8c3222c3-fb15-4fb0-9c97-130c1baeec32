import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { fetchReports } from '../utils/reports'

export const Route = createFileRoute('/reports/')({
  validateSearch: (search: Record<string, unknown>): { type?: string } => {
    return {
      type: search.type as string | undefined,
    }
  },
  loader: async () => {
    const reports = await fetchReports()
    return { reports }
  },
  component: ReportsIndex,
})

function ReportsIndex() {
  const { reports } = Route.useLoaderData()
  const { type } = Route.useSearch()

  const filteredReports = type ? reports.filter((report) => report.type === type) : reports

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">
          {type ? `${type.charAt(0).toUpperCase() + type.slice(1)} Reports` : 'All Reports'}
        </h1>
        <p className="text-gray-600 mt-2">View and analyze generated reports</p>
      </div>

      <div className="grid gap-4">
        {filteredReports.map((report) => (
          <Link
            key={report.id}
            to="/reports/$reportId"
            params={{ reportId: report.id }}
            className="block bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">{report.title}</h3>
                <p className="text-gray-600 mt-1">
                  {report.type.charAt(0).toUpperCase() + report.type.slice(1).replace('-', ' ')}{' '}
                  report
                </p>
                <div className="flex gap-4 mt-3 text-sm text-gray-500">
                  <span>Created: {new Date(report.createdDate).toLocaleDateString()}</span>
                  <span>•</span>
                  <span>Type: {report.type}</span>
                  <span>•</span>
                  <span>Format: {report.format}</span>
                  <span>•</span>
                  <span>By: {report.createdBy}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="default">Completed</Badge>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {filteredReports.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No reports found</p>
          <Button className="mt-4">Generate New Report</Button>
        </div>
      )}
    </div>
  )
}
