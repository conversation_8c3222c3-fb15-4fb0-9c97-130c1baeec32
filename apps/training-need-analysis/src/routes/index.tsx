import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { Button } from '@luminar/shared-ui'
import { fetchDashboardAnalytics } from '../utils/analytics'

export const Route = createFileRoute('/')({
  loader: async () => fetchDashboardAnalytics(),
  component: Home,
})

function Home() {
  const analytics = Route.useLoaderData()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">Training Need</span>
                  <span className="block text-blue-600 xl:inline"> Analysis</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  Empower your L&D strategy with comprehensive skills assessment, gap analysis, and
                  targeted training recommendations. Drive organizational growth through data-driven
                  learning initiatives.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link to="/dashboard">
                      <Button className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10">
                        Get Started
                      </Button>
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link to="/assessments">
                      <Button
                        variant="outline"
                        className="w-full flex items-center justify-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
                      >
                        View Assessments
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-br from-blue-400 to-purple-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
            <div className="text-6xl text-white">📊</div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-blue-600 font-semibold tracking-wide uppercase">
              Features
            </h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need for effective L&D
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              Comprehensive tools for assessing skills, identifying gaps, and managing training
              programs across your organization.
            </p>
          </div>

          <div className="mt-10">
            <div className="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
              <FeatureCard
                icon="📋"
                title="Skills Assessment"
                description="Create comprehensive skills assessments to evaluate employee capabilities and identify areas for improvement."
                link="/assessments"
              />
              <FeatureCard
                icon="📚"
                title="Training Catalog"
                description="Browse and manage a comprehensive library of training courses, workshops, and learning paths."
                link="/training"
              />
              <FeatureCard
                icon="📊"
                title="Analytics Dashboard"
                description="Monitor training progress, skill improvements, and ROI with detailed analytics and reporting."
                link="/dashboard"
              />
              <FeatureCard
                icon="📄"
                title="Custom Reports"
                description="Generate detailed reports on skills gaps, training effectiveness, and departmental performance."
                link="/reports"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-blue-600">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8 lg:py-20">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-extrabold text-white sm:text-4xl">Platform Overview</h2>
            <p className="mt-3 text-xl text-blue-200 sm:mt-4">
              Current system metrics and capabilities
            </p>
          </div>
          <dl className="mt-10 text-center sm:max-w-3xl sm:mx-auto sm:grid sm:grid-cols-3 sm:gap-8">
            <StatItem
              number={analytics.totalEmployees}
              label="Employees"
              description="Across all departments"
            />
            <StatItem
              number={analytics.totalSkillGaps}
              label="Skills Gaps"
              description="Identified and tracked"
            />
            <StatItem
              number={analytics.trainingCompleted}
              label="Training Completed"
              description="This quarter"
            />
          </dl>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-50">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
          <h2 className="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            <span className="block">Ready to optimize your L&D strategy?</span>
            <span className="block text-blue-600">Start your analysis today.</span>
          </h2>
          <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
            <div className="inline-flex rounded-md shadow">
              <Link to="/dashboard">
                <Button className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                  View Dashboard
                </Button>
              </Link>
            </div>
            <div className="ml-3 inline-flex rounded-md shadow">
              <Link to="/assessments">
                <Button
                  variant="outline"
                  className="inline-flex items-center justify-center px-5 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Create Assessment
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function FeatureCard({
  icon,
  title,
  description,
  link,
}: {
  icon: string
  title: string
  description: string
  link: string
}) {
  return (
    <div className="relative">
      <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
        <span className="text-2xl">{icon}</span>
      </div>
      <div className="ml-16">
        <h3 className="text-lg leading-6 font-medium text-gray-900">{title}</h3>
        <p className="mt-2 text-base text-gray-500">{description}</p>
        <Link to={link} className="mt-3 text-sm text-blue-600 hover:text-blue-800 font-medium">
          Learn more →
        </Link>
      </div>
    </div>
  )
}

function StatItem({
  number,
  label,
  description,
}: {
  number: number
  label: string
  description: string
}) {
  return (
    <div className="flex flex-col">
      <dt className="order-2 mt-2 text-lg leading-6 font-medium text-blue-200">{label}</dt>
      <dd className="order-1 text-5xl font-extrabold text-white">{number.toLocaleString()}</dd>
      <dd className="order-3 text-sm text-blue-200">{description}</dd>
    </div>
  )
}
