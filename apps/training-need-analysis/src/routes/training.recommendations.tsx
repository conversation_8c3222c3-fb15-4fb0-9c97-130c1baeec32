import { createFileRoute } from '@tanstack/react-router'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { fetchTrainingRecommendations } from '../utils/training'

export const Route = createFileRoute('/training/recommendations')({
  loader: async () => {
    // In a real app, you'd get the current user ID from auth context
    const currentUserId = 'emp-001'
    try {
      const recommendations = await fetchTrainingRecommendations(currentUserId)
      return { recommendations, currentUserId }
    } catch (error) {
      console.error('Failed to fetch recommendations:', error)
      return { recommendations: [], currentUserId }
    }
  },
  component: TrainingRecommendations,
})

function TrainingRecommendations() {
  const { recommendations } = Route.useLoaderData()

  const highPriorityRecs = recommendations.filter((rec) => rec.priority === 'high')
  const mediumPriorityRecs = recommendations.filter((rec) => rec.priority === 'medium')
  const lowPriorityRecs = recommendations.filter((rec) => rec.priority === 'low')

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Training Recommendations</h2>
          <p className="text-gray-600 mt-1">
            Personalized course suggestions based on your skills gaps and career goals
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Refresh Recommendations</Button>
          <Button>View All Courses</Button>
        </div>
      </div>

      {/* Recommendation Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">High Priority</h3>
          <p className="text-3xl font-bold text-red-600 mt-2">{highPriorityRecs.length}</p>
          <p className="text-sm text-gray-600 mt-1">Critical skill gaps</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Medium Priority</h3>
          <p className="text-3xl font-bold text-orange-600 mt-2">{mediumPriorityRecs.length}</p>
          <p className="text-sm text-gray-600 mt-1">Important improvements</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Low Priority</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{lowPriorityRecs.length}</p>
          <p className="text-sm text-gray-600 mt-1">Nice to have skills</p>
        </div>
      </div>

      {/* High Priority Recommendations */}
      {highPriorityRecs.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">High Priority Recommendations</h3>
            <Badge variant="destructive">Urgent</Badge>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {highPriorityRecs.map((rec) => (
              <div key={rec.id} className="border border-red-200 rounded-lg p-4 bg-red-50">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">Course {rec.courseId}</h4>
                    <p className="text-sm text-gray-600 mt-1">{rec.reason}</p>
                  </div>
                  <Badge variant="destructive">High</Badge>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-700 mb-2">
                    <strong>Skills addressed:</strong> {rec.skillsAddressed.join(', ')}
                  </p>
                  <p className="text-sm text-gray-700">
                    <strong>Expected improvement:</strong> {rec.expectedImprovement}
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="flex-1">
                    Enroll Now
                  </Button>
                  <Button size="sm" variant="outline">
                    Learn More
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* All Recommendations */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">All Recommendations</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Course
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Skills Addressed
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expected Improvement
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recommendations.map((rec) => (
                <tr key={rec.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Course {rec.courseId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={
                        rec.priority === 'high'
                          ? 'destructive'
                          : rec.priority === 'medium'
                            ? 'default'
                            : 'secondary'
                      }
                    >
                      {rec.priority}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex flex-wrap gap-1">
                      {rec.skillsAddressed.slice(0, 2).map((skill, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {rec.skillsAddressed.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{rec.skillsAddressed.length - 2}
                        </Badge>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                    {rec.reason}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {rec.expectedImprovement}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-4">Enroll</button>
                    <button className="text-gray-600 hover:text-gray-900">Details</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* No Recommendations Message */}
      {recommendations.length === 0 && (
        <div className="bg-white rounded-lg border p-12 text-center">
          <div className="text-gray-400 mb-4">
            <svg
              className="mx-auto h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a9 9 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.53-1.053l-.548-.547z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Recommendations Available</h3>
          <p className="text-gray-600 mb-4">
            Complete a skills assessment to get personalized training recommendations.
          </p>
          <Button>Take Skills Assessment</Button>
        </div>
      )}
    </div>
  )
}
