import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { LuminarBarChart as Chart } from '@luminar/shared-ui'
import { fetchDepartmentMetrics, fetchSkillGaps } from '../utils/analytics'

export const Route = createFileRoute('/dashboard/skills-gaps')({
  loader: async () => {
    const [skillGaps, departmentMetrics] = await Promise.all([
      fetchSkillGaps(),
      fetchDepartmentMetrics(),
    ])
    return { skillGaps, departmentMetrics }
  },
  component: SkillGapsAnalysis,
})

function SkillGapsAnalysis() {
  const { skillGaps, departmentMetrics } = Route.useLoaderData()
  const [isExporting, setIsExporting] = useState(false)
  const [isCreatingPlan, setIsCreatingPlan] = useState(false)

  const criticalGaps = skillGaps.filter((gap) => gap.severity === 'critical')
  const highGaps = skillGaps.filter((gap) => gap.severity === 'high')
  const mediumGaps = skillGaps.filter((gap) => gap.severity === 'medium')

  const gapsByDepartment = skillGaps.reduce(
    (acc, gap) => {
      acc[gap.departmentId] = (acc[gap.departmentId] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  const gapsBySkill = skillGaps.reduce(
    (acc, gap) => {
      acc[gap.skillId] = (acc[gap.skillId] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )

  const topSkillGaps = Object.entries(gapsBySkill)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)

  const handleExportReport = async () => {
    setIsExporting(true)
    try {
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'skills-gap',
          format: 'pdf',
          data: {
            totalGaps: skillGaps.length,
            criticalGaps: criticalGaps.length,
            highGaps: highGaps.length,
            mediumGaps: mediumGaps.length,
            topSkillGaps: topSkillGaps,
          },
        }),
      })

      const result = await response.json()
      if (result.success) {
        // Simulate download
        alert(`Skills Gap Report exported successfully! Report ID: ${result.reportId}`)
        // In a real app, you would trigger a download or redirect to the download URL
        console.log('Download URL:', result.downloadUrl)
      }
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    } finally {
      setIsExporting(false)
    }
  }

  const handleCreateTrainingPlan = async () => {
    setIsCreatingPlan(true)
    try {
      const response = await fetch('/api/training/create-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Skills Gap Training Plan',
          skills: topSkillGaps.slice(0, 5).map(([skillId]) => skillId),
          priority: 'high',
          targetEmployees: skillGaps.length,
          estimatedDuration: '8-12 weeks',
        }),
      })

      const result = await response.json()
      if (result.success) {
        alert(
          `Training plan created successfully! Plan ID: ${result.planId}\nEstimated Duration: ${result.plan.estimatedDuration}`
        )
        // In a real app, you might redirect to the training plan page
        console.log('Training Plan:', result.plan)
      }
    } catch (error) {
      console.error('Training plan creation failed:', error)
      alert('Failed to create training plan. Please try again.')
    } finally {
      setIsCreatingPlan(false)
    }
  }

  const handleViewSkillDetails = (skillId: string) => {
    const skillGapsForSkill = skillGaps.filter((gap) => gap.skillId === skillId)
    const affectedEmployees = skillGapsForSkill.length
    const avgGapSize =
      skillGapsForSkill.reduce((sum, gap) => sum + gap.gapSize, 0) / skillGapsForSkill.length

    alert(
      `Skill Details: ${skillId}\n\nAffected Employees: ${affectedEmployees}\nAverage Gap Size: ${avgGapSize.toFixed(1)}\nSeverity Distribution:\n- Critical: ${skillGapsForSkill.filter((g) => g.severity === 'critical').length}\n- High: ${skillGapsForSkill.filter((g) => g.severity === 'high').length}\n- Medium: ${skillGapsForSkill.filter((g) => g.severity === 'medium').length}`
    )
  }

  const handleRecommendTraining = async (employeeId: string, skillId: string) => {
    try {
      const response = await fetch('/api/training/recommend', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId,
          skillId,
          priority: 'high',
        }),
      })

      if (response.ok) {
        alert(
          `Training recommendation sent for Employee ${employeeId}!\n\nSkill: ${skillId}\nRecommended courses will be available in their training dashboard.`
        )
      }
    } catch (error) {
      console.error('Failed to recommend training:', error)
      alert('Failed to send training recommendation. Please try again.')
    }
  }

  const handleViewProfile = (employeeId: string) => {
    // In a real app, you would navigate to the employee profile
    window.open(`/users/${employeeId}`, '_blank')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Skills Gap Analysis</h2>
          <p className="text-gray-600 mt-1">
            Identify and prioritize skill gaps across your organization
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportReport} disabled={isExporting}>
            {isExporting ? 'Exporting...' : 'Export Report'}
          </Button>
          <Button onClick={handleCreateTrainingPlan} disabled={isCreatingPlan}>
            {isCreatingPlan ? 'Creating Plan...' : 'Create Training Plan'}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Gaps</h3>
          <p className="text-3xl font-bold text-gray-900 mt-2">{skillGaps.length}</p>
          <p className="text-sm text-gray-600 mt-1">Across all departments</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Critical Gaps</h3>
          <p className="text-3xl font-bold text-red-600 mt-2">{criticalGaps.length}</p>
          <p className="text-sm text-gray-600 mt-1">Require immediate attention</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">High Priority</h3>
          <p className="text-3xl font-bold text-orange-600 mt-2">{highGaps.length}</p>
          <p className="text-sm text-gray-600 mt-1">Should be addressed soon</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Medium Priority</h3>
          <p className="text-3xl font-bold text-yellow-600 mt-2">{mediumGaps.length}</p>
          <p className="text-sm text-gray-600 mt-1">Can be planned for later</p>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Gaps by Severity</h3>
          <Chart
            type="donut"
            data={{
              labels: ['Critical', 'High', 'Medium'],
              values: [criticalGaps.length, highGaps.length, mediumGaps.length],
            }}
            height={250}
          />
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Gaps by Department</h3>
          <Chart
            type="bar"
            data={{
              labels: Object.keys(gapsByDepartment),
              values: Object.values(gapsByDepartment),
            }}
            height={250}
          />
        </div>
      </div>

      {/* Top Skill Gaps */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Skill Gaps</h3>
        <div className="space-y-4">
          {topSkillGaps.map(([skillId, count], index) => (
            <div
              key={skillId}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-800">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Skill ID: {skillId}</h4>
                  <p className="text-sm text-gray-600">{count} employees affected</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">{count} gaps</Badge>
                <Button size="sm" variant="outline" onClick={() => handleViewSkillDetails(skillId)}>
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Gap List */}
      <div className="bg-white rounded-lg border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">All Skill Gaps</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Skill
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Required Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gap Size
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {skillGaps.slice(0, 20).map((gap) => (
                <tr key={`${gap.employeeId}-${gap.skillId}`} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Employee {gap.employeeId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Skill {gap.skillId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {gap.currentLevel}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {gap.requiredLevel}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {gap.gapSize}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={
                        gap.severity === 'critical'
                          ? 'destructive'
                          : gap.severity === 'high'
                            ? 'default'
                            : 'secondary'
                      }
                    >
                      {gap.severity}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      className="text-blue-600 hover:text-blue-900 mr-4"
                      onClick={() => handleRecommendTraining(gap.employeeId, gap.skillId)}
                    >
                      Recommend Training
                    </button>
                    <button
                      className="text-gray-600 hover:text-gray-900"
                      onClick={() => handleViewProfile(gap.employeeId)}
                    >
                      View Profile
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
