import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { fetchReportById } from '../utils/reports'

export const Route = createFileRoute('/reports/$reportId')({
  loader: async ({ params }) => {
    const report = await fetchReportById(params.reportId)
    return { report }
  },
  component: ReportDetail,
})

function ReportDetail() {
  const { report } = Route.useLoaderData()

  if (!report) {
    return <div>Report not found</div>
  }

  return (
    <div>
      <div className="mb-6">
        <Link to="/reports" className="text-blue-600 hover:text-blue-700 mb-4 inline-block">
          ← Back to Reports
        </Link>
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold">{report.title}</h1>
            <p className="text-gray-600 mt-2">{report.description}</p>
          </div>
          <div className="flex gap-2">
            <Button>Export PDF</Button>
            <Button variant="outline">Share</Button>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Generated</h3>
            <p className="mt-1 text-lg">{new Date(report.generatedAt).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Type</h3>
            <p className="mt-1 text-lg capitalize">{report.type}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Period</h3>
            <p className="mt-1 text-lg">{report.period}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Status</h3>
            <p className="mt-1">
              <Badge
                variant={
                  report.status === 'completed'
                    ? 'default'
                    : report.status === 'processing'
                      ? 'secondary'
                      : 'outline'
                }
              >
                {report.status}
              </Badge>
            </p>
          </div>
        </div>

        {report.content && (
          <div className="space-y-6">
            {report.content.sections?.map((section, index) => (
              <div key={index} className="border-t pt-6 first:border-0 first:pt-0">
                <h2 className="text-xl font-semibold mb-4">{section.title}</h2>
                {section.type === 'text' && <p className="text-gray-700">{section.data}</p>}
                {section.type === 'chart' && (
                  <div className="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
                    <span className="text-gray-500">Chart: {section.data.title}</span>
                  </div>
                )}
                {section.type === 'table' && (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {section.data.headers.map((header, i) => (
                            <th
                              key={i}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {section.data.rows.map((row, i) => (
                          <tr key={i}>
                            {row.map((cell, j) => (
                              <td
                                key={j}
                                className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                              >
                                {cell}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
