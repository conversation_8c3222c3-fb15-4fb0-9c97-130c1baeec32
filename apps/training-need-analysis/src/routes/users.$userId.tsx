import { createFileRoute } from '@tanstack/react-router'
import { NotFound } from '~/components/NotFound'
import { UserErrorComponent } from '~/components/UserError'
import { LuminarBadge as Badge } from '@luminar/shared-ui'
import { Button } from '@luminar/shared-ui'
import { LuminarBarChart as Chart } from '@luminar/shared-ui'
import { ProgressBar as Progress } from '@luminar/shared-ui'
import { fetchUsers } from '../utils/users'

export const Route = createFileRoute('/users/$userId')({
  loader: async ({ params: { userId } }) => {
    const users = await fetchUsers()
    const user = users.find((u) => u.id === userId)

    if (!user) {
      throw new Error('User not found')
    }

    return { user }
  },
  errorComponent: UserErrorComponent,
  component: UserComponent,
  notFoundComponent: () => {
    return <NotFound>User not found</NotFound>
  },
})

function UserComponent() {
  const { user } = Route.useLoaderData()

  // Mock additional user data for enhanced profile
  const skillLevels = user.skills || []
  const recentTraining = [
    { course: 'React Advanced Patterns', progress: 85, status: 'in-progress' },
    { course: 'TypeScript Fundamentals', progress: 100, status: 'completed' },
    { course: 'Team Leadership', progress: 45, status: 'in-progress' },
  ]

  const skillGaps = user.skillGaps || []
  const averageSkillLevel =
    skillLevels.length > 0
      ? skillLevels.reduce((sum, skill) => sum + (skill.level || 0), 0) / skillLevels.length
      : 0

  return (
    <div className="space-y-6">
      {/* User Header */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center text-xl font-medium text-gray-700">
              {user.name.charAt(0)}
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{user.name}</h1>
              <p className="text-gray-600">{user.role}</p>
              <p className="text-sm text-gray-500">{user.department} Department</p>
              <p className="text-sm text-gray-500">{user.email}</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">Edit Profile</Button>
            <Button>Assign Training</Button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Skills</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{skillLevels.length}</p>
          <p className="text-sm text-gray-600 mt-1">Total skills</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Avg Skill Level</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{averageSkillLevel.toFixed(1)}</p>
          <p className="text-sm text-gray-600 mt-1">Out of 3.0</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Skill Gaps</h3>
          <p className="text-3xl font-bold text-red-600 mt-2">{skillGaps.length}</p>
          <p className="text-sm text-gray-600 mt-1">Areas to improve</p>
        </div>

        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-sm font-medium text-gray-500">Training Progress</h3>
          <p className="text-3xl font-bold text-purple-600 mt-2">
            {Math.round(
              recentTraining.reduce((sum, t) => sum + t.progress, 0) / recentTraining.length
            )}
            %
          </p>
          <p className="text-sm text-gray-600 mt-1">Average completion</p>
        </div>
      </div>

      {/* Skills and Training Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Skills Overview */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Skills Overview</h3>
          {skillLevels.length > 0 ? (
            <div className="space-y-4">
              {skillLevels.slice(0, 8).map((skill, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    {skill.name || `Skill ${index + 1}`}
                  </span>
                  <div className="flex items-center space-x-2">
                    <Progress value={(skill.level / 3) * 100} className="h-2 w-20" />
                    <span className="text-sm text-gray-600">{skill.level}/3</span>
                  </div>
                </div>
              ))}
              {skillLevels.length > 8 && (
                <Button variant="outline" size="sm" className="w-full">
                  View All {skillLevels.length} Skills
                </Button>
              )}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">No skills data available</p>
          )}
        </div>

        {/* Recent Training */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Training</h3>
          <div className="space-y-4">
            {recentTraining.map((training, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900">{training.course}</h4>
                  <div className="flex items-center space-x-2 mt-1">
                    <Progress value={training.progress} className="h-2 flex-1" />
                    <span className="text-xs text-gray-600">{training.progress}%</span>
                  </div>
                </div>
                <Badge
                  variant={training.status === 'completed' ? 'default' : 'secondary'}
                  className="ml-3"
                >
                  {training.status}
                </Badge>
              </div>
            ))}
            <Button variant="outline" size="sm" className="w-full">
              View All Training
            </Button>
          </div>
        </div>
      </div>

      {/* Skill Gaps */}
      {skillGaps.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Skill Gaps</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {skillGaps.map((gap, index) => (
              <div key={index} className="p-4 border border-red-200 rounded-lg bg-red-50">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">Skill {gap.skillId}</h4>
                  <Badge variant="destructive">{gap.severity}</Badge>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>Current Level: {gap.currentLevel}</p>
                  <p>Required Level: {gap.requiredLevel}</p>
                  <p>Gap Size: {gap.gapSize}</p>
                </div>
                <Button size="sm" className="mt-3 w-full">
                  Recommend Training
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Chart */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Skill Development Trend</h3>
        <Chart
          data={[
            { label: 'Jan', value: 2.1 },
            { label: 'Feb', value: 2.3 },
            { label: 'Mar', value: 2.2 },
            { label: 'Apr', value: 2.5 },
            { label: 'May', value: 2.7 },
            { label: 'Jun', value: 2.8 },
          ]}
          height={250}
        />
      </div>
    </div>
  )
}
