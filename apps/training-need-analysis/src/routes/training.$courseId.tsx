import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@luminar/shared-ui'
import { fetchTrainingCourse } from '../utils/training'

export const Route = createFileRoute('/training/$courseId')({
  loader: async ({ params }) => {
    const course = await fetchTrainingCourse(params.courseId)
    return { course }
  },
  component: CourseDetail,
})

function CourseDetail() {
  const { course } = Route.useLoaderData()

  const availableSpots = course.capacity ? course.capacity - course.enrollments : null
  const isFullyBooked = availableSpots !== null && availableSpots <= 0

  return (
    <div className="space-y-6">
      {/* Course Header */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <TypeBadge type={course.type} />
              <DifficultyBadge difficulty={course.difficulty} />
              <StatusBadge status={course.status} />
            </div>

            <h1 className="text-3xl font-bold text-gray-900 mb-2">{course.title}</h1>
            <p className="text-lg text-gray-600 mb-4">{course.description}</p>

            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="mr-1">🏢</span>
                <span>{course.provider}</span>
              </div>
              <div className="flex items-center">
                <span className="mr-1">⏱️</span>
                <span>{course.duration} hours</span>
              </div>
              <div className="flex items-center">
                <span className="mr-1">⭐</span>
                <span>
                  {course.rating} ({course.enrollments} enrollments)
                </span>
              </div>
              <div className="flex items-center">
                <span className="mr-1">💰</span>
                <span>${course.cost}</span>
              </div>
            </div>
          </div>

          <div className="lg:w-80 bg-gray-50 rounded-lg p-6">
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-gray-900">${course.cost}</div>
              <div className="text-sm text-gray-600">Per person</div>
            </div>

            {course.capacity && (
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Enrollment</span>
                  <span>
                    {course.enrollments} / {course.capacity}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${(course.enrollments / course.capacity) * 100}%` }}
                  />
                </div>
                {availableSpots !== null && (
                  <div className="text-xs text-gray-500 mt-1">
                    {availableSpots > 0 ? `${availableSpots} spots available` : 'Fully booked'}
                  </div>
                )}
              </div>
            )}

            <div className="space-y-3">
              <Button className="w-full" disabled={isFullyBooked}>
                {isFullyBooked ? 'Join Waitlist' : 'Enroll Now'}
              </Button>
              <Button variant="outline" className="w-full">
                Add to Wishlist
              </Button>
              <Button variant="outline" className="w-full">
                Share Course
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Skills Addressed */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Skills You'll Learn</h2>
            <div className="flex flex-wrap gap-2">
              {course.skillsAddressed.map((skill) => (
                <SkillBadge key={skill} skill={skill} />
              ))}
            </div>
          </div>

          {/* Prerequisites */}
          {course.prerequisites.length > 0 && (
            <div className="bg-white rounded-lg border p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Prerequisites</h2>
              <ul className="space-y-2">
                {course.prerequisites.map((prerequisite, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span className="text-gray-700">{prerequisite}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Course Curriculum */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Course Curriculum</h2>
            <CourseCurriculum course={course} />
          </div>

          {/* Reviews and Ratings */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Student Reviews</h2>
            <ReviewsSection course={course} />
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Course Details */}
          <div className="bg-white rounded-lg border p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Course Details</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Format:</span>
                <span className="font-medium">{course.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium">{course.duration} hours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Difficulty:</span>
                <span className="font-medium capitalize">{course.difficulty}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Language:</span>
                <span className="font-medium">English</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Certificate:</span>
                <span className="font-medium">Yes</span>
              </div>
            </div>
          </div>

          {/* Related Courses */}
          <div className="bg-white rounded-lg border p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Related Courses</h3>
            <RelatedCourses currentCourse={course} />
          </div>

          {/* Learning Path */}
          <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
            <h3 className="font-semibold text-gray-900 mb-2">Part of Learning Path</h3>
            <p className="text-sm text-gray-600 mb-3">
              This course is part of the "Frontend Developer Career Path"
            </p>
            <Button variant="outline" size="sm">
              View Learning Path
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

function TypeBadge({ type }: { type: string }) {
  const config = {
    online: { color: 'bg-blue-100 text-blue-800', label: 'Online' },
    workshop: { color: 'bg-purple-100 text-purple-800', label: 'Workshop' },
    certification: { color: 'bg-indigo-100 text-indigo-800', label: 'Certification' },
    internal: { color: 'bg-gray-100 text-gray-800', label: 'Internal' },
    external: { color: 'bg-orange-100 text-orange-800', label: 'External' },
  }

  const badgeConfig = config[type as keyof typeof config] || config.online

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${badgeConfig.color}`}
    >
      {badgeConfig.label}
    </span>
  )
}

function DifficultyBadge({ difficulty }: { difficulty: string }) {
  const config = {
    beginner: { color: 'bg-green-100 text-green-800', label: 'Beginner' },
    intermediate: { color: 'bg-yellow-100 text-yellow-800', label: 'Intermediate' },
    advanced: { color: 'bg-red-100 text-red-800', label: 'Advanced' },
  }

  const badgeConfig = config[difficulty as keyof typeof config] || config.beginner

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${badgeConfig.color}`}
    >
      {badgeConfig.label}
    </span>
  )
}

function StatusBadge({ status }: { status: string }) {
  const config = {
    active: { color: 'bg-green-100 text-green-800', label: 'Available' },
    inactive: { color: 'bg-gray-100 text-gray-800', label: 'Unavailable' },
    'coming-soon': { color: 'bg-blue-100 text-blue-800', label: 'Coming Soon' },
  }

  const badgeConfig = config[status as keyof typeof config] || config.active

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${badgeConfig.color}`}
    >
      {badgeConfig.label}
    </span>
  )
}

function SkillBadge({ skill }: { skill: string }) {
  return (
    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
      {skill.replace(/-/g, ' ').replace(/^\w/, (c) => c.toUpperCase())}
    </span>
  )
}

function CourseCurriculum({ course }: { course: any }) {
  // Mock curriculum data
  const curriculum = [
    {
      module: 1,
      title: 'Introduction and Fundamentals',
      duration: Math.floor(course.duration * 0.2),
      lessons: ['Course Overview', 'Setting up Development Environment', 'Basic Concepts'],
    },
    {
      module: 2,
      title: 'Core Concepts and Practices',
      duration: Math.floor(course.duration * 0.4),
      lessons: ['Advanced Techniques', 'Best Practices', 'Common Patterns', 'Hands-on Exercises'],
    },
    {
      module: 3,
      title: 'Advanced Topics',
      duration: Math.floor(course.duration * 0.3),
      lessons: ['Complex Scenarios', 'Performance Optimization', 'Testing Strategies'],
    },
    {
      module: 4,
      title: 'Project and Assessment',
      duration: Math.floor(course.duration * 0.1),
      lessons: ['Final Project', 'Peer Review', 'Assessment'],
    },
  ]

  return (
    <div className="space-y-4">
      {curriculum.map((module) => (
        <div key={module.module} className="border rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-medium text-gray-900">
              Module {module.module}: {module.title}
            </h4>
            <span className="text-sm text-gray-500">{module.duration}h</span>
          </div>
          <ul className="space-y-1">
            {module.lessons.map((lesson, index) => (
              <li key={index} className="text-sm text-gray-600 flex items-center">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-2" />
                {lesson}
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  )
}

function ReviewsSection({ course }: { course: any }) {
  // Mock reviews data
  const reviews = [
    {
      id: 1,
      author: 'Sarah M.',
      rating: 5,
      date: '2024-11-15',
      comment:
        'Excellent course! The instructor was knowledgeable and the content was well-structured.',
    },
    {
      id: 2,
      author: 'John D.',
      rating: 4,
      date: '2024-11-10',
      comment:
        'Great practical examples. Would recommend to anyone looking to improve their skills.',
    },
    {
      id: 3,
      author: 'Lisa K.',
      rating: 5,
      date: '2024-11-05',
      comment: 'This course exceeded my expectations. The hands-on projects were very valuable.',
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-4 pb-4 border-b">
        <div className="text-3xl font-bold text-gray-900">{course.rating}</div>
        <div>
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <span
                key={star}
                className={`text-lg ${
                  star <= Math.floor(course.rating) ? 'text-yellow-400' : 'text-gray-300'
                }`}
              >
                ⭐
              </span>
            ))}
          </div>
          <div className="text-sm text-gray-500">{course.enrollments} reviews</div>
        </div>
      </div>

      <div className="space-y-4">
        {reviews.map((review) => (
          <div key={review.id} className="border-b pb-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-900">{review.author}</span>
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <span
                      key={star}
                      className={`text-sm ${
                        star <= review.rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      ⭐
                    </span>
                  ))}
                </div>
              </div>
              <span className="text-sm text-gray-500">
                {new Date(review.date).toLocaleDateString()}
              </span>
            </div>
            <p className="text-gray-700">{review.comment}</p>
          </div>
        ))}
      </div>

      <Button variant="outline" className="w-full">
        View All Reviews
      </Button>
    </div>
  )
}

function RelatedCourses({ currentCourse }: { currentCourse: any }) {
  // Mock related courses - in a real app, this would be based on similar skills or categories
  const relatedCourses = [
    {
      id: 'related-1',
      title: 'Advanced JavaScript Patterns',
      duration: 24,
      rating: 4.6,
      price: 399,
    },
    {
      id: 'related-2',
      title: 'React Testing Strategies',
      duration: 16,
      rating: 4.7,
      price: 299,
    },
    {
      id: 'related-3',
      title: 'Modern CSS Techniques',
      duration: 20,
      rating: 4.5,
      price: 249,
    },
  ]

  return (
    <div className="space-y-3">
      {relatedCourses.map((course) => (
        <div key={course.id} className="border rounded-lg p-3">
          <h4 className="font-medium text-gray-900 text-sm mb-1">{course.title}</h4>
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{course.duration}h</span>
            <span>⭐ {course.rating}</span>
            <span>${course.price}</span>
          </div>
        </div>
      ))}
      <Button variant="outline" size="sm" className="w-full">
        View All Related Courses
      </Button>
    </div>
  )
}
