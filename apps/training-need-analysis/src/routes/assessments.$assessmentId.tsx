import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@luminar/shared-ui'
import { fetchAssessment, fetchAssessmentResponses } from '../utils/assessments'

export const Route = createFileRoute('/assessments/$assessmentId')({
  loader: async ({ params }) => {
    const [assessment, responses] = await Promise.all([
      fetchAssessment(params.assessmentId),
      fetchAssessmentResponses(params.assessmentId),
    ])
    return { assessment, responses }
  },
  component: AssessmentDetail,
})

function AssessmentDetail() {
  const { assessment, responses } = Route.useLoaderData()

  const completedResponses = responses.filter((r) => r.status === 'completed')
  const inProgressResponses = responses.filter((r) => r.status === 'in-progress')
  const notStartedResponses = responses.filter((r) => r.status === 'not-started')

  const completionRate =
    responses.length > 0 ? Math.round((completedResponses.length / responses.length) * 100) : 0

  return (
    <div className="space-y-6">
      {/* Assessment Header */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{assessment.title}</h1>
              <StatusBadge status={assessment.status} />
            </div>

            <p className="text-gray-600 mb-4">{assessment.description}</p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Created:</span>
                <div className="font-medium">
                  {new Date(assessment.createdDate).toLocaleDateString()}
                </div>
              </div>
              <div>
                <span className="text-gray-500">Created by:</span>
                <div className="font-medium">{assessment.createdBy}</div>
              </div>
              <div>
                <span className="text-gray-500">Skills assessed:</span>
                <div className="font-medium">{assessment.skillsAssessed.length}</div>
              </div>
              {assessment.dueDate && (
                <div>
                  <span className="text-gray-500">Due date:</span>
                  <div className="font-medium">
                    {new Date(assessment.dueDate).toLocaleDateString()}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              Edit Assessment
            </Button>
            <Button size="sm">Export Results</Button>
          </div>
        </div>
      </div>

      {/* Response Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <ResponseStatCard title="Total Responses" value={responses.length} icon="📋" color="blue" />
        <ResponseStatCard
          title="Completed"
          value={completedResponses.length}
          icon="✅"
          color="green"
          subtitle={`${completionRate}% completion rate`}
        />
        <ResponseStatCard
          title="In Progress"
          value={inProgressResponses.length}
          icon="🔄"
          color="yellow"
        />
        <ResponseStatCard
          title="Not Started"
          value={notStartedResponses.length}
          icon="⏳"
          color="gray"
        />
      </div>

      {/* Skills Assessment Results */}
      <div className="bg-white rounded-lg border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Skills Assessment Results</h2>

        {assessment.skillsAssessed.length === 0 ? (
          <p className="text-gray-500 italic">
            No skills have been configured for this assessment.
          </p>
        ) : (
          <div className="space-y-4">
            {assessment.skillsAssessed.map((skillId) => (
              <SkillResultCard key={skillId} skillId={skillId} responses={completedResponses} />
            ))}
          </div>
        )}
      </div>

      {/* Response Details */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Response Details</h2>
          <Button variant="outline" size="sm">
            Send Reminders
          </Button>
        </div>

        {responses.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No responses have been submitted yet.</p>
            <Button>Invite Participants</Button>
          </div>
        ) : (
          <div className="space-y-3">
            {responses.map((response) => (
              <ResponseDetailCard key={response.id} response={response} />
            ))}
          </div>
        )}
      </div>

      {/* Gap Analysis Summary */}
      {completedResponses.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Gap Analysis Summary</h2>
          <GapAnalysisSummary responses={completedResponses} />
        </div>
      )}
    </div>
  )
}

function StatusBadge({ status }: { status: string }) {
  const statusConfig = {
    draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
    active: { color: 'bg-green-100 text-green-800', label: 'Active' },
    completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' },
    archived: { color: 'bg-gray-100 text-gray-600', label: 'Archived' },
  }

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft

  return (
    <span
      className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${config.color}`}
    >
      {config.label}
    </span>
  )
}

function ResponseStatCard({
  title,
  value,
  icon,
  color,
  subtitle,
}: {
  title: string
  value: number
  icon: string
  color: string
  subtitle?: string
}) {
  const colorClasses = {
    blue: 'border-blue-200 bg-blue-50',
    green: 'border-green-200 bg-green-50',
    yellow: 'border-yellow-200 bg-yellow-50',
    gray: 'border-gray-200 bg-gray-50',
  }

  return (
    <div className={`p-4 rounded-lg border ${colorClasses[color as keyof typeof colorClasses]}`}>
      <div className="flex items-center">
        <span className="text-xl mr-3">{icon}</span>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
      </div>
    </div>
  )
}

function SkillResultCard({ skillId, responses }: { skillId: string; responses: any[] }) {
  // Calculate average current and required levels for this skill
  const skillRatings = responses.flatMap((r) =>
    r.skillRatings.filter((rating: any) => rating.skillId === skillId)
  )

  if (skillRatings.length === 0) {
    return (
      <div className="p-4 border rounded-lg">
        <h3 className="font-medium text-gray-900">{skillId}</h3>
        <p className="text-sm text-gray-500">No responses for this skill yet</p>
      </div>
    )
  }

  const avgCurrentLevel =
    skillRatings.reduce((sum: number, rating: any) => sum + rating.currentLevel, 0) /
    skillRatings.length
  const avgRequiredLevel =
    skillRatings.reduce((sum: number, rating: any) => sum + rating.requiredLevel, 0) /
    skillRatings.length
  const avgGap = avgRequiredLevel - avgCurrentLevel

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium text-gray-900">{skillId.replace(/-/g, ' ').toUpperCase()}</h3>
        <span className={`text-sm font-medium ${avgGap > 0.5 ? 'text-red-600' : 'text-green-600'}`}>
          Gap: {avgGap.toFixed(1)}
        </span>
      </div>

      <div className="grid grid-cols-3 gap-4 text-sm">
        <div>
          <span className="text-gray-500">Current Level:</span>
          <div className="font-medium">{avgCurrentLevel.toFixed(1)}/3</div>
        </div>
        <div>
          <span className="text-gray-500">Required Level:</span>
          <div className="font-medium">{avgRequiredLevel.toFixed(1)}/3</div>
        </div>
        <div>
          <span className="text-gray-500">Responses:</span>
          <div className="font-medium">{skillRatings.length}</div>
        </div>
      </div>

      {/* Progress bar visualization */}
      <div className="mt-3">
        <div className="flex justify-between text-xs text-gray-500 mb-1">
          <span>Current</span>
          <span>Required</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${(avgCurrentLevel / 3) * 100}%` }}
          />
        </div>
      </div>
    </div>
  )
}

function ResponseDetailCard({ response }: { response: any }) {
  const statusConfig = {
    completed: { color: 'text-green-600', icon: '✅' },
    'in-progress': { color: 'text-yellow-600', icon: '🔄' },
    'not-started': { color: 'text-gray-600', icon: '⏳' },
  }

  const config =
    statusConfig[response.status as keyof typeof statusConfig] || statusConfig['not-started']

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex items-center space-x-3">
        <span className="text-lg">{config.icon}</span>
        <div>
          <h4 className="font-medium text-gray-900">Employee {response.employeeId}</h4>
          <p className={`text-sm ${config.color}`}>
            {response.status.replace('-', ' ').toUpperCase()}
          </p>
          {response.completedDate && (
            <p className="text-xs text-gray-500">
              Completed: {new Date(response.completedDate).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>

      <div className="text-right">
        <div className="text-sm font-medium text-gray-900">
          {response.skillRatings?.length || 0} skills rated
        </div>
        {response.status === 'completed' && (
          <Button variant="outline" size="sm">
            View Details
          </Button>
        )}
      </div>
    </div>
  )
}

function GapAnalysisSummary({ responses }: { responses: any[] }) {
  const allSkillRatings = responses.flatMap((r) => r.skillRatings)
  const gapsFound = allSkillRatings.filter(
    (rating: any) => rating.requiredLevel > rating.currentLevel
  )
  const criticalGaps = gapsFound.filter(
    (rating: any) => rating.requiredLevel - rating.currentLevel >= 2
  )

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{gapsFound.length}</div>
          <div className="text-sm text-gray-600">Skills Gaps Identified</div>
        </div>
        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">{criticalGaps.length}</div>
          <div className="text-sm text-gray-600">Critical Gaps (2+ levels)</div>
        </div>
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {Math.round((gapsFound.length / allSkillRatings.length) * 100)}%
          </div>
          <div className="text-sm text-gray-600">Gap Rate</div>
        </div>
      </div>

      <div className="flex space-x-2">
        <Button>Generate Training Plan</Button>
        <Button variant="outline">Export Gap Analysis</Button>
      </div>
    </div>
  )
}
