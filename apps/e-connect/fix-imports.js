#!/usr/bin/env node

/**
 * Fix broken import statements after migration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BROKEN_FILES = [
  'src/components/assistant-chat/ChatMessage.tsx',
  'src/components/assistant-chat/RuleBuilder.tsx',
  'src/components/assistant-chat/SuggestedActions.tsx',
  'src/components/rules/RuleForm.tsx',
  'src/components/rules/RuleHistory.tsx',
  'src/components/rules/RulesList.tsx',
  'src/components/rules/RuleTest.tsx',
  'src/routes/stats/emails.tsx',
  'src/routes/stats/index.tsx',
  'src/routes/stats/rules.tsx',
  'src/routes/stats/senders.tsx'
];

function fixFile(filePath) {
  console.log(`Fixing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Pattern: import {\nimport { ... from shared-ui
  // Fix: move shared-ui import to the top
  const brokenPattern = /import\s*{\s*\nimport\s*{([^}]+)}\s*from\s*'@luminar\/shared-ui'\s*\n/;
  const match = content.match(brokenPattern);
  
  if (match) {
    const sharedUIImport = `import { ${match[1]} } from '@luminar/shared-ui'\n`;
    
    // Remove the broken import
    content = content.replace(brokenPattern, 'import {\n');
    
    // Add shared-ui import at the beginning of imports
    const lines = content.split('\n');
    let insertIndex = 0;
    
    // Find first import line
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().startsWith('import ')) {
        insertIndex = i;
        break;
      }
    }
    
    // Insert shared-ui import
    lines.splice(insertIndex, 0, sharedUIImport.trim());
    
    content = lines.join('\n');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔧 Fixing broken import statements...\n');
  
  let fixedCount = 0;
  
  for (const file of BROKEN_FILES) {
    const fullPath = path.join(__dirname, file);
    
    if (fs.existsSync(fullPath)) {
      try {
        if (fixFile(fullPath)) {
          fixedCount++;
        }
      } catch (error) {
        console.error(`❌ Error fixing ${file}:`, error.message);
      }
    } else {
      console.log(`⚠️  File not found: ${file}`);
    }
  }
  
  console.log(`\n✨ Fixed ${fixedCount} files`);
}

main();