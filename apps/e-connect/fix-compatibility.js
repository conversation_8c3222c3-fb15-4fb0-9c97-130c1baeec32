#!/usr/bin/env node

/**
 * Fix compatibility issues between local and shared-ui components
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.join(__dirname, 'src');

function getAllFiles(dir, pattern = /\.(tsx?|jsx?)$/) {
  const files = [];
  
  function traverse(currentDir) {
    const entries = fs.readdirSync(currentDir);
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !entry.startsWith('.')) {
        traverse(fullPath);
      } else if (pattern.test(entry)) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function fixCompatibilityIssues(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Fix Button size variants
  const sizeReplacements = [
    { from: 'size="iconSm"', to: 'size="xs"' },
    { from: "size='iconSm'", to: "size='xs'" },
    { from: 'size={\'iconSm\'}', to: 'size={\'xs\'}' },
    { from: 'size={"iconSm"}', to: 'size={"xs"}' }
  ];
  
  for (const replacement of sizeReplacements) {
    if (content.includes(replacement.from)) {
      content = content.replaceAll(replacement.from, replacement.to);
      modified = true;
    }
  }
  
  // Fix Button variants  
  const variantReplacements = [
    { from: 'variant="primaryBlue"', to: 'variant="primary"' },
    { from: "variant='primaryBlue'", to: "variant='primary'" },
    { from: 'variant={\'primaryBlue\'}', to: 'variant={\'primary\'}' },
    { from: 'variant={"primaryBlue"}', to: 'variant={"primary"}' }
  ];
  
  for (const replacement of variantReplacements) {
    if (content.includes(replacement.from)) {
      content = content.replaceAll(replacement.from, replacement.to);
      modified = true;
    }
  }
  
  // Fix missing Input and Select imports
  if (content.includes('from \'@luminar/shared-ui\'')) {
    // Check if Input or Select are used but not imported
    const hasInputUsage = /\<Input\s/.test(content);
    const hasSelectUsage = /\<Select\s/.test(content);
    
    if (hasInputUsage || hasSelectUsage) {
      // Check current shared-ui import
      const sharedUIImportMatch = content.match(/import\s*{\s*([^}]+)\s*}\s*from\s*'@luminar\/shared-ui'/);
      
      if (sharedUIImportMatch) {
        const currentImports = sharedUIImportMatch[1].split(',').map(imp => imp.trim());
        const newImports = [...currentImports];
        
        if (hasInputUsage && !currentImports.includes('Input')) {
          // Add local Input import
          content = content.replace(
            sharedUIImportMatch[0],
            sharedUIImportMatch[0] + '\nimport { Input } from \'@/components/ui/Input\''
          );
          modified = true;
        }
        
        if (hasSelectUsage && !currentImports.includes('Select')) {
          // Add local Select import
          content = content.replace(
            sharedUIImportMatch[0],
            sharedUIImportMatch[0] + '\nimport { Select } from \'@/components/ui/Select\''
          );
          modified = true;
        }
      }
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔧 Fixing compatibility issues...\n');
  
  const files = getAllFiles(SRC_DIR);
  let fixedCount = 0;
  
  for (const file of files) {
    try {
      if (fixCompatibilityIssues(file)) {
        console.log(`✅ Fixed: ${path.relative(process.cwd(), file)}`);
        fixedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n✨ Fixed compatibility issues in ${fixedCount} files`);
  console.log('\n📝 Next: Run `npm run typecheck` to verify fixes');
}

main();