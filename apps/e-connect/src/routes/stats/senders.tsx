import {  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Tabs  } from '@luminar/shared-ui'
import {
  AdjustmentsHorizontalIcon,
  ArrowArrowTrendingDownIcon,
  ArrowArrowTrendingUpIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ClockIcon,
  EnvelopeIcon,
  FireIcon,
  GlobeAltIcon,
  InformationCircleIcon,
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  SparklesIcon,
  StarIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'
import { useQuery } from '@tanstack/react-query'
import { createFileRoute } from '@tanstack/react-router'
import { endOfDay, format, startOfDay, subDays, subMonths } from 'date-fns'
import { useMemo, useState } from 'react'
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  <PERSON><PERSON>hart,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON>,
  Treemap,
  XAxis,
  YAxis,
} from 'recharts'
import { Card } from '@/components/ui/Card'
import { useTheme } from '../../hooks/useTheme'

export const Route = createFileRoute('/stats/senders')({
  component: SenderAnalytics,
})

interface DateRange {
  start: Date
  end: Date
  label: string
}

const predefinedRanges: DateRange[] = [
  { start: startOfDay(new Date()), end: endOfDay(new Date()), label: 'Today' },
  { start: startOfDay(subDays(new Date(), 7)), end: endOfDay(new Date()), label: 'Last 7 days' },
  { start: startOfDay(subDays(new Date(), 30)), end: endOfDay(new Date()), label: 'Last 30 days' },
  { start: startOfDay(subDays(new Date(), 90)), end: endOfDay(new Date()), label: 'Last 90 days' },
  {
    start: startOfDay(subMonths(new Date(), 6)),
    end: endOfDay(new Date()),
    label: 'Last 6 months',
  },
]

const sortOptions = [
  { value: 'volume', label: 'Email Volume' },
  { value: 'frequency', label: 'Communication Frequency' },
  { value: 'relationship', label: 'Relationship Strength' },
  { value: 'responsiveness', label: 'Responsiveness' },
  { value: 'recent', label: 'Most Recent' },
  { value: 'domain', label: 'Domain' },
]

const filterOptions = [
  { value: 'all', label: 'All Senders' },
  { value: 'vip', label: 'VIP Senders' },
  { value: 'frequent', label: 'Frequent Contacts' },
  { value: 'new', label: 'New Senders' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'work', label: 'Work Contacts' },
  { value: 'personal', label: 'Personal' },
]

function SenderAnalytics() {
  const [dateRange, setDateRange] = useState<DateRange>(predefinedRanges[1])
  const [sortBy, setSortBy] = useState('volume')
  const [filterBy, setFilterBy] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshKey, setRefreshKey] = useState(0)
  const { isDark } = useTheme()

  // Fetch sender analytics data
  const { data: senderData, isLoading: isLoadingSenders } = useQuery({
    queryKey: ['sender-analytics', dateRange, sortBy, filterBy, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/senders?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&sort=${sortBy}&filter=${filterBy}`
      )
      if (!response.ok) throw new Error('Failed to fetch sender data')
      return response.json()
    },
  })

  const { data: relationshipData, isLoading: isLoadingRelationships } = useQuery({
    queryKey: ['sender-relationships', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/sender-relationships?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch relationship data')
      return response.json()
    },
  })

  const { data: domainAnalytics, isLoading: isLoadingDomains } = useQuery({
    queryKey: ['domain-analytics', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/domains?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch domain analytics')
      return response.json()
    },
  })

  const { data: communicationPatterns, isLoading: isLoadingPatterns } = useQuery({
    queryKey: ['communication-patterns', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/communication-patterns?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch communication patterns')
      return response.json()
    },
  })

  const isLoading =
    isLoadingSenders || isLoadingRelationships || isLoadingDomains || isLoadingPatterns

  // Filter senders based on search term
  const filteredSenders = useMemo(() => {
    if (!senderData?.senders) return []

    let filtered = senderData.senders

    if (searchTerm) {
      filtered = filtered.filter(
        (sender: any) =>
          sender.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sender.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sender.domain?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered
  }, [senderData, searchTerm])

  // Calculate summary metrics
  const metrics = useMemo(() => {
    if (!senderData) return null

    const { summary, vipSenders, newSenders } = senderData
    return {
      totalSenders: summary.totalSenders || 0,
      activeSenders: summary.activeSenders || 0,
      vipCount: vipSenders?.length || 0,
      newSendersCount: newSenders?.length || 0,
      avgEmailsPerSender: summary.avgEmailsPerSender || 0,
      topSenderVolume: summary.topSenderVolume || 0,
      responseRate: summary.avgResponseRate || 0,
      relationshipScore: summary.avgRelationshipScore || 0,
    }
  }, [senderData])

  const handleExport = async (format: 'csv' | 'pdf') => {
    try {
      const response = await fetch(
        `/api/analytics/export?type=sender-analytics&format=${format}&start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&sort=${sortBy}&filter=${filterBy}`
      )
      if (!response.ok) throw new Error('Export failed')

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `sender-analytics-${dateRange.label.toLowerCase().replace(/\s+/g, '-')}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loading size="lg" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Sender Analytics</h1>
          <p className="text-muted-foreground mt-1">
            Comprehensive analysis of your email senders and communication patterns
          </p>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search senders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <Select value={filterBy} onValueChange={setFilterBy} options={filterOptions} />

          <Select value={sortBy} onValueChange={setSortBy} options={sortOptions} />

          <Select
            value={dateRange.label}
            onValueChange={(value) => {
              const range = predefinedRanges.find((r) => r.label === value)
              if (range) setDateRange(range)
            }}
            options={predefinedRanges.map((r) => ({ value: r.label, label: r.label }))}
          />

          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshKey((prev) => prev + 1)}
            className="gap-2"
          >
            <ArrowPathIcon className="h-4 w-4" />
            Refresh
          </Button>

          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Senders"
          value={metrics?.totalSenders.toLocaleString() || '0'}
          subtitle={`${metrics?.activeSenders || 0} active`}
          icon={<UserGroupIcon className="h-5 w-5" />}
          className="bg-blue-50 dark:bg-blue-900/20"
        />

        <MetricCard
          title="VIP Senders"
          value={metrics?.vipCount.toString() || '0'}
          subtitle="High-priority contacts"
          icon={<StarIcon className="h-5 w-5" />}
          className="bg-yellow-50 dark:bg-yellow-900/20"
        />

        <MetricCard
          title="New Senders"
          value={metrics?.newSendersCount.toString() || '0'}
          subtitle="In selected period"
          icon={<SparklesIcon className="h-5 w-5" />}
          trend={12.5}
          className="bg-green-50 dark:bg-green-900/20"
        />

        <MetricCard
          title="Avg Response Rate"
          value={`${metrics?.responseRate.toFixed(1) || 0}%`}
          subtitle="Overall communication"
          icon={<ArrowTrendingUpIcon className="h-5 w-5" />}
          trend={8.3}
          className="bg-purple-50 dark:bg-purple-900/20"
        />
      </div>

      {/* Quick Insights */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Communication Insights</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <FireIcon className="h-4 w-4 text-red-500" />
              Most Active Sender
            </h3>
            <div className="space-y-2">
              <p className="font-medium">{senderData?.topSender?.name || 'N/A'}</p>
              <p className="text-sm text-muted-foreground">{senderData?.topSender?.email}</p>
              <p className="text-sm">{senderData?.topSender?.messageCount || 0} emails</p>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <GlobeAltIcon className="h-4 w-4 text-blue-500" />
              Top Domain
            </h3>
            <div className="space-y-2">
              <p className="font-medium">{domainAnalytics?.topDomain?.domain || 'N/A'}</p>
              <p className="text-sm text-muted-foreground">
                {domainAnalytics?.topDomain?.senderCount || 0} senders
              </p>
              <p className="text-sm">{domainAnalytics?.topDomain?.messageCount || 0} emails</p>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <ShieldCheckIcon className="h-4 w-4 text-green-500" />
              Communication Health
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">{relationshipData?.healthScore || 85}</span>
                <span className="text-sm text-muted-foreground">/100</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${relationshipData?.healthScore || 85}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="senders" className="space-y-4">
        <Tabs.List className="grid w-full grid-cols-5">
          <Tabs.Trigger value="senders">Top Senders</Tabs.Trigger>
          <Tabs.Trigger value="relationships">Relationships</Tabs.Trigger>
          <Tabs.Trigger value="domains">Domains</Tabs.Trigger>
          <Tabs.Trigger value="patterns">Patterns</Tabs.Trigger>
          <Tabs.Trigger value="insights">Insights</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="senders" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Volume Distribution</h3>
              <SenderVolumeChart data={senderData?.volumeDistribution} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Communication Frequency</h3>
              <FrequencyPatternChart data={communicationPatterns?.frequency} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Sender Details</h3>
            <SenderTable
              senders={filteredSenders}
              searchTerm={searchTerm}
              onMarkVIP={(senderId) => {
                // Handle VIP marking
                console.log('Mark as VIP:', senderId)
              }}
            />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="relationships" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Relationship Strength</h3>
              <RelationshipStrengthChart
                data={relationshipData?.strengthDistribution}
                isDark={isDark}
              />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Interaction Timeline</h3>
              <InteractionTimelineChart data={relationshipData?.timeline} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">VIP Sender Analysis</h3>
            <VIPAnalysisTable vipSenders={senderData?.vipSenders} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="domains" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Domain Distribution</h3>
              <DomainDistributionChart data={domainAnalytics?.distribution} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Organization Types</h3>
              <OrganizationTypesChart data={domainAnalytics?.organizationTypes} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Domain Analysis</h3>
            <DomainAnalysisTable domains={domainAnalytics?.domains} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="patterns" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Response Time Patterns</h3>
              <ResponseTimeChart data={communicationPatterns?.responseTimes} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Communication Radar</h3>
              <CommunicationRadarChart data={communicationPatterns?.radarData} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Sender Lifecycle</h3>
            <SenderLifecycleChart data={communicationPatterns?.lifecycle} isDark={isDark} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">New vs Recurring</h3>
              <NewVsRecurringChart data={senderData?.newVsRecurring} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Engagement Correlation</h3>
              <EngagementCorrelationChart data={relationshipData?.correlations} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Actionable Insights</h3>
            <InsightsTable insights={senderData?.insights} />
          </Card>
        </Tabs.Content>
      </Tabs>
    </div>
  )
}

// Metric Card Component
interface MetricCardProps {
  title: string
  value: string
  subtitle: string
  icon: React.ReactNode
  trend?: number
  className?: string
}

function MetricCard({ title, value, subtitle, icon, trend, className }: MetricCardProps) {
  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        </div>
        <div className="p-2 rounded-lg bg-background/50">{icon}</div>
      </div>

      {trend !== undefined && (
        <div className="mt-4 flex items-center gap-1">
          {trend > 0 ? (
            <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />
          )}
          <span className={`text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {Math.abs(trend).toFixed(1)}%
          </span>
          <span className="text-sm text-muted-foreground">vs last period</span>
        </div>
      )}
    </Card>
  )
}

// Chart Components
function SenderVolumeChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No volume data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data.slice(0, 10)} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis
          dataKey="name"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="emailCount" fill="#3b82f6" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function FrequencyPatternChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No frequency data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="period" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Line type="monotone" dataKey="frequency" stroke="#10b981" strokeWidth={2} />
      </LineChart>
    </ResponsiveContainer>
  )
}

function RelationshipStrengthChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No relationship data available</p>
      </div>
    )
  }

  const COLORS = ['#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6']

  return (
    <ResponsiveContainer width="100%" height={250}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="count"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  )
}

function InteractionTimelineChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No timeline data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Area
          type="monotone"
          dataKey="interactions"
          stroke="#8b5cf6"
          fill="#8b5cf6"
          fillOpacity={0.6}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

function DomainDistributionChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No domain data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <Treemap data={data} dataKey="size" ratio={4 / 3} stroke="#fff" fill="#3b82f6">
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
      </Treemap>
    </ResponsiveContainer>
  )
}

function OrganizationTypesChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No organization type data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="type" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="count" fill="#f59e0b" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function ResponseTimeChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No response time data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ScatterChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis
          type="number"
          dataKey="volume"
          name="Volume"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
        />
        <YAxis
          type="number"
          dataKey="responseTime"
          name="Response Time"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any, name: string) => [
            name === 'volume' ? `${value} emails` : `${value}h`,
            name === 'volume' ? 'Volume' : 'Response Time',
          ]}
        />
        <Scatter dataKey="responseTime" fill="#ef4444" />
      </ScatterChart>
    </ResponsiveContainer>
  )
}

function CommunicationRadarChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No radar data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <RadarChart data={data}>
        <PolarGrid />
        <PolarAngleAxis dataKey="metric" />
        <PolarRadiusAxis domain={[0, 100]} />
        <Radar
          name="Communication Score"
          dataKey="score"
          stroke="#3b82f6"
          fill="#3b82f6"
          fillOpacity={0.3}
        />
        <Tooltip />
      </RadarChart>
    </ResponsiveContainer>
  )
}

function SenderLifecycleChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No lifecycle data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="stage" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="count" fill="#10b981" />
        <Line type="monotone" dataKey="retention" stroke="#ef4444" strokeWidth={2} />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

function NewVsRecurringChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No sender comparison data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="period" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Area
          type="monotone"
          dataKey="new"
          stackId="1"
          stroke="#10b981"
          fill="#10b981"
          name="New Senders"
        />
        <Area
          type="monotone"
          dataKey="recurring"
          stackId="1"
          stroke="#3b82f6"
          fill="#3b82f6"
          name="Recurring Senders"
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

function EngagementCorrelationChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No correlation data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ScatterChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis
          type="number"
          dataKey="engagement"
          name="Engagement"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
        />
        <YAxis
          type="number"
          dataKey="responseRate"
          name="Response Rate"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any, name: string) => [
            `${value}${name === 'responseRate' ? '%' : ''}`,
            name === 'engagement' ? 'Engagement Score' : 'Response Rate',
          ]}
        />
        <Scatter dataKey="responseRate" fill="#8b5cf6" />
      </ScatterChart>
    </ResponsiveContainer>
  )
}

// Table Components
function SenderTable({
  senders,
  searchTerm,
  onMarkVIP,
}: {
  senders: any[]
  searchTerm: string
  onMarkVIP: (id: string) => void
}) {
  if (!senders || senders.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No senders found {searchTerm ? `matching "${searchTerm}"` : ''}</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">Sender</th>
            <th className="text-right p-3">Messages</th>
            <th className="text-right p-3">Frequency</th>
            <th className="text-right p-3">Response Time</th>
            <th className="text-right p-3">Relationship</th>
            <th className="text-right p-3">Actions</th>
          </tr>
        </thead>
        <tbody>
          {senders.slice(0, 20).map((sender, index) => (
            <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="p-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-sm font-medium">
                    {sender.name?.charAt(0) || sender.email?.charAt(0) || '?'}
                  </div>
                  <div>
                    <p className="font-medium">{sender.name || 'Unknown'}</p>
                    <p className="text-sm text-muted-foreground">{sender.email}</p>
                    <p className="text-xs text-muted-foreground">{sender.domain}</p>
                  </div>
                </div>
              </td>
              <td className="text-right p-3">
                <span className="font-medium">{sender.messageCount}</span>
              </td>
              <td className="text-right p-3">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    sender.frequency === 'high'
                      ? 'bg-red-100 text-red-700'
                      : sender.frequency === 'medium'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-green-100 text-green-700'
                  }`}
                >
                  {sender.frequency}
                </span>
              </td>
              <td className="text-right p-3">
                <span className="text-sm">{sender.avgResponseTime?.toFixed(1) || '-'}h</span>
              </td>
              <td className="text-right p-3">
                <div className="flex items-center justify-end gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`h-3 w-3 ${
                        i < (sender.relationshipScore || 0) / 20
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </td>
              <td className="text-right p-3">
                <div className="flex items-center gap-2 justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onMarkVIP(sender.id)}
                    className="gap-1"
                  >
                    {sender.isVIP ? (
                      <StarSolidIcon className="h-4 w-4 text-yellow-400" />
                    ) : (
                      <StarIcon className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function VIPAnalysisTable({ vipSenders }: { vipSenders: any[] }) {
  if (!vipSenders || vipSenders.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No VIP senders identified</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">VIP Sender</th>
            <th className="text-right p-3">Priority Score</th>
            <th className="text-right p-3">Engagement</th>
            <th className="text-right p-3">Last Contact</th>
            <th className="text-right p-3">Status</th>
          </tr>
        </thead>
        <tbody>
          {vipSenders.map((vip, index) => (
            <tr key={index} className="border-b">
              <td className="p-3">
                <div className="flex items-center gap-3">
                  <StarSolidIcon className="h-5 w-5 text-yellow-400" />
                  <div>
                    <p className="font-medium">{vip.name}</p>
                    <p className="text-sm text-muted-foreground">{vip.email}</p>
                  </div>
                </div>
              </td>
              <td className="text-right p-3">
                <span className="font-medium">{vip.priorityScore}</span>
              </td>
              <td className="text-right p-3">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    vip.engagement > 80
                      ? 'bg-green-100 text-green-700'
                      : vip.engagement > 60
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-red-100 text-red-700'
                  }`}
                >
                  {vip.engagement}%
                </span>
              </td>
              <td className="text-right p-3">
                <span className="text-sm">{vip.lastContact}</span>
              </td>
              <td className="text-right p-3">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    vip.status === 'active'
                      ? 'bg-green-100 text-green-700'
                      : vip.status === 'inactive'
                        ? 'bg-red-100 text-red-700'
                        : 'bg-yellow-100 text-yellow-700'
                  }`}
                >
                  {vip.status}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function DomainAnalysisTable({ domains }: { domains: any[] }) {
  if (!domains || domains.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No domain analysis available</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">Domain</th>
            <th className="text-right p-3">Senders</th>
            <th className="text-right p-3">Messages</th>
            <th className="text-right p-3">Type</th>
            <th className="text-right p-3">Reputation</th>
          </tr>
        </thead>
        <tbody>
          {domains.slice(0, 15).map((domain, index) => (
            <tr key={index} className="border-b">
              <td className="p-3">
                <div className="flex items-center gap-2">
                  <GlobeAltIcon className="h-4 w-4 text-gray-400" />
                  <span className="font-medium">{domain.name}</span>
                </div>
              </td>
              <td className="text-right p-3">{domain.senderCount}</td>
              <td className="text-right p-3">{domain.messageCount}</td>
              <td className="text-right p-3">
                <span className="text-sm capitalize">{domain.type}</span>
              </td>
              <td className="text-right p-3">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    domain.reputation === 'high'
                      ? 'bg-green-100 text-green-700'
                      : domain.reputation === 'medium'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-red-100 text-red-700'
                  }`}
                >
                  {domain.reputation}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function InsightsTable({ insights }: { insights: any[] }) {
  if (!insights || insights.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No insights available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {insights.map((insight, index) => (
        <div key={index} className="border rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-1">
              <div
                className={`w-3 h-3 rounded-full ${
                  insight.priority === 'high'
                    ? 'bg-red-500'
                    : insight.priority === 'medium'
                      ? 'bg-yellow-500'
                      : 'bg-green-500'
                }`}
              />
            </div>
            <div className="flex-1">
              <h4 className="font-medium">{insight.title}</h4>
              <p className="text-sm text-muted-foreground mt-1">{insight.description}</p>
              {insight.recommendation && (
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <div className="flex items-start gap-2">
                    <InformationCircleIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      {insight.recommendation}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
