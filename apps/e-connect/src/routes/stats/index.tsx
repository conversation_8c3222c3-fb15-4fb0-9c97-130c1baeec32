import { <PERSON><PERSON>, LoadingSpinner } from '@luminar/shared-ui'
import { LuminarSelect as Select } from '@luminar/shared-ui/forms'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from '@/components/ui/Tabs'
import {
  ArchiveBoxIcon,
  ArrowDownIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  ArrowUpIcon,
  CalendarIcon,
  ChartBarIcon,
  ClockIcon,
  DocumentTextIcon,
  EnvelopeIcon,
  InboxIcon,
  SparklesIcon,
  TrashIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'
import { useQuery } from '@tanstack/react-query'
import { createFileRoute } from '@tanstack/react-router'
import { endOfDay, format, startOfDay, subDays } from 'date-fns'
import { useMemo, useState } from 'react'
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'
import { Category<PERSON>hart } from '@/components/charts/CategoryChart'
import { EmailVolumeChart } from '@/components/charts/EmailVolumeChart'
import { RuleMetrics } from '@/components/charts/RuleMetrics'
import { SenderChart } from '@/components/charts/SenderChart'
import { Card } from '@/components/ui/Card'
import { useTheme } from '../../hooks/useTheme'

export const Route = createFileRoute('/stats/')({
  component: AnalyticsDashboard,
})

interface DateRange {
  start: Date
  end: Date
  label: string
}

const predefinedRanges: DateRange[] = [
  { start: startOfDay(new Date()), end: endOfDay(new Date()), label: 'Today' },
  { start: startOfDay(subDays(new Date(), 7)), end: endOfDay(new Date()), label: 'Last 7 days' },
  { start: startOfDay(subDays(new Date(), 30)), end: endOfDay(new Date()), label: 'Last 30 days' },
  { start: startOfDay(subDays(new Date(), 90)), end: endOfDay(new Date()), label: 'Last 90 days' },
]

function AnalyticsDashboard() {
  const [dateRange, setDateRange] = useState<DateRange>(predefinedRanges[1])
  const [refreshKey, setRefreshKey] = useState(0)
  const { isDark } = useTheme()

  // Fetch analytics data
  const { data: emailStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['email-stats', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/email-stats?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch email stats')
      return response.json()
    },
    refetchInterval: 60000, // Refresh every minute
  })

  const { data: senderAnalytics, isLoading: isLoadingSenders } = useQuery({
    queryKey: ['sender-analytics', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/senders?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch sender analytics')
      return response.json()
    },
  })

  const { data: rulePerformance, isLoading: isLoadingRules } = useQuery({
    queryKey: ['rule-performance', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/rules?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch rule performance')
      return response.json()
    },
  })

  const { data: timeAnalysis, isLoading: isLoadingTime } = useQuery({
    queryKey: ['time-analysis', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/time-patterns?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
      )
      if (!response.ok) throw new Error('Failed to fetch time analysis')
      return response.json()
    },
  })

  const isLoading = isLoadingStats || isLoadingSenders || isLoadingRules || isLoadingTime

  // Calculate key metrics
  const keyMetrics = useMemo(() => {
    if (!emailStats) return null

    const { totals, metrics, categoryBreakdown } = emailStats
    const responseRate = totals.sent > 0 ? ((totals.sent / totals.received) * 100).toFixed(1) : '0'
    const inboxZeroRate =
      totals.threads > 0 ? ((1 - totals.unread / totals.threads) * 100).toFixed(1) : '100'

    return {
      totalEmails: totals.threads,
      unreadCount: totals.unread,
      responseRate: `${responseRate}%`,
      avgResponseTime: `${metrics.avgResponseTime.toFixed(1)}h`,
      inboxZeroRate: `${inboxZeroRate}%`,
      emailsPerDay: metrics.emailsPerDay.toFixed(1),
      storageUsed: '2.3 GB', // Mock for now
      storageQuota: '15 GB', // Mock for now
      storagePercentage: 15.3, // Mock for now
    }
  }, [emailStats])

  // Productivity metrics
  const productivityMetrics = useMemo(() => {
    if (!emailStats || !rulePerformance) return null

    const automationTime = rulePerformance.totalTimeSaved || 0
    const processedToday = emailStats.metrics.emailsPerDay || 0
    const unsubscribeRate = 87.5 // Mock for now
    const coldEmailBlocked = 234 // Mock for now

    return {
      processedToday: Math.round(processedToday),
      automationTime: `${(automationTime / 60).toFixed(1)}h`,
      unsubscribeRate: `${unsubscribeRate}%`,
      coldEmailBlocked,
    }
  }, [emailStats, rulePerformance])

  const handleExport = async (format: 'png' | 'csv') => {
    try {
      if (format === 'png') {
        // For PNG export, we would typically use html2canvas or similar
        // For now, we'll show a message
        console.log('PNG export would capture the current dashboard view')
        // You could integrate with libraries like html2canvas here
        alert('PNG export functionality would be implemented with html2canvas or similar library')
      } else {
        // CSV export through API
        const response = await fetch(
          `/api/analytics/export?format=csv&type=email-stats&start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
        )
        if (!response.ok) throw new Error('Export failed')

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `analytics-${dateRange.label.toLowerCase().replace(/\s+/g, '-')}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    }
  }

  const handleRefresh = () => {
    setRefreshKey((prev) => prev + 1)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Track your email patterns and productivity metrics
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Select
            value={dateRange.label}
            onValueChange={(value) => {
              const range = predefinedRanges.find((r) => r.label === value)
              if (range) setDateRange(range)
            }}
            options={predefinedRanges.map((r) => ({ value: r.label, label: r.label }))}
          />

          <Button variant="outline" size="sm" onClick={handleRefresh} className="gap-2">
            <ArrowPathIcon className="h-4 w-4" />
            Refresh
          </Button>

          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('png')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              PNG
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              CSV
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Emails"
          value={keyMetrics?.totalEmails.toLocaleString() || '0'}
          subtitle={`${keyMetrics?.unreadCount || 0} unread`}
          icon={<EnvelopeIcon className="h-5 w-5" />}
          trend={emailStats?.metrics.growthRate}
          className="bg-blue-50 dark:bg-blue-900/20"
        />

        <MetricCard
          title="Response Rate"
          value={keyMetrics?.responseRate || '0%'}
          subtitle={`Avg ${keyMetrics?.avgResponseTime || '0h'}`}
          icon={<ClockIcon className="h-5 w-5" />}
          trend={12.5}
          className="bg-green-50 dark:bg-green-900/20"
        />

        <MetricCard
          title="Inbox Zero"
          value={keyMetrics?.inboxZeroRate || '0%'}
          subtitle="Achievement rate"
          icon={<InboxIcon className="h-5 w-5" />}
          trend={8.3}
          className="bg-purple-50 dark:bg-purple-900/20"
        />

        <MetricCard
          title="Storage Used"
          value={keyMetrics?.storageUsed || '0 GB'}
          subtitle={`of ${keyMetrics?.storageQuota || '0 GB'}`}
          icon={<ArchiveBoxIcon className="h-5 w-5" />}
          progress={keyMetrics?.storagePercentage}
          className="bg-orange-50 dark:bg-orange-900/20"
        />
      </div>

      {/* Productivity Metrics */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Productivity Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Emails Processed Today</p>
            <p className="text-2xl font-bold">{productivityMetrics?.processedToday || 0}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Time Saved by Automation</p>
            <p className="text-2xl font-bold">{productivityMetrics?.automationTime || '0h'}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Unsubscribe Success Rate</p>
            <p className="text-2xl font-bold">{productivityMetrics?.unsubscribeRate || '0%'}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Cold Emails Blocked</p>
            <p className="text-2xl font-bold">{productivityMetrics?.coldEmailBlocked || 0}</p>
          </div>
        </div>
      </Card>

      {/* Main Charts Section */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="senders">Senders</TabsTrigger>
          <TabsTrigger value="rules">Rules</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Email Volume Over Time</h3>
              <EmailVolumeChart data={emailStats?.volumeData} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Category Distribution</h3>
              <CategoryChart data={emailStats?.categoryBreakdown} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Peak Activity Heatmap</h3>
            <ActivityHeatmap data={timeAnalysis?.hourlyDistribution} isDark={isDark} />
          </Card>
        </TabsContent>

        <TabsContent value="senders" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Top Senders Analysis</h3>
            <SenderChart data={senderAnalytics?.topSenders} isDark={isDark} />
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Sender Metrics</h3>
            <SenderMetricsTable senders={senderAnalytics?.topSenders || []} />
          </Card>
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Rule Performance</h3>
            <RuleMetrics data={rulePerformance} isDark={isDark} />
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Rule Effectiveness</h3>
            <RuleEffectivenessTable rules={rulePerformance?.rules || []} />
          </Card>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Response Time Trends</h3>
              <ResponseTimeChart data={timeAnalysis?.responsePatterns} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Weekly Pattern</h3>
              <WeeklyPatternChart data={timeAnalysis?.weeklyPattern} isDark={isDark} />
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Metric Card Component
interface MetricCardProps {
  title: string
  value: string
  subtitle: string
  icon: React.ReactNode
  trend?: number
  progress?: number
  className?: string
}

function MetricCard({ title, value, subtitle, icon, trend, progress, className }: MetricCardProps) {
  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        </div>
        <div className="p-2 rounded-lg bg-background/50">{icon}</div>
      </div>

      {trend !== undefined && (
        <div className="mt-4 flex items-center gap-1">
          {trend > 0 ? (
            <ArrowUpIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ArrowDownIcon className="h-4 w-4 text-red-600" />
          )}
          <span className={`text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {Math.abs(trend).toFixed(1)}%
          </span>
          <span className="text-sm text-muted-foreground">vs last period</span>
        </div>
      )}

      {progress !== undefined && (
        <div className="mt-4">
          <div className="h-2 bg-background rounded-full overflow-hidden">
            <div
              className="h-full bg-primary transition-all duration-300"
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
        </div>
      )}
    </Card>
  )
}

// Activity Heatmap Component
function ActivityHeatmap({ data, isDark }: { data: any; isDark: boolean }) {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  const hours = Array.from({ length: 24 }, (_, i) => i)

  if (!data || !Array.isArray(data)) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No activity data available</p>
      </div>
    )
  }

  // Get max value for color scaling
  const maxValue = Math.max(...data.flat())

  const getIntensity = (value: number) => {
    if (maxValue === 0) return 0
    return value / maxValue
  }

  const getColor = (intensity: number) => {
    if (intensity === 0) return isDark ? 'bg-gray-800' : 'bg-gray-100'
    if (intensity < 0.25) return isDark ? 'bg-blue-900/40' : 'bg-blue-200'
    if (intensity < 0.5) return isDark ? 'bg-blue-700/60' : 'bg-blue-400'
    if (intensity < 0.75) return isDark ? 'bg-blue-600/80' : 'bg-blue-600'
    return isDark ? 'bg-blue-500' : 'bg-blue-800'
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">Email activity by hour and day</p>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Less</span>
          <div className="flex gap-1">
            {[0, 0.25, 0.5, 0.75, 1].map((intensity) => (
              <div key={intensity} className={`w-3 h-3 rounded-sm ${getColor(intensity)}`} />
            ))}
          </div>
          <span>More</span>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Hour labels */}
          <div className="flex mb-2">
            <div className="w-12"></div>
            {hours
              .filter((_, i) => i % 2 === 0)
              .map((hour) => (
                <div key={hour} className="w-8 text-xs text-center text-muted-foreground">
                  {hour}
                </div>
              ))}
          </div>

          {/* Grid */}
          <div className="space-y-1">
            {days.map((day, dayIndex) => (
              <div key={day} className="flex items-center">
                <div className="w-12 text-xs text-muted-foreground text-right pr-2">{day}</div>
                <div className="flex gap-1">
                  {hours.map((hour) => {
                    const value = data[hour]?.[dayIndex] || 0
                    const intensity = getIntensity(value)
                    return (
                      <div
                        key={`${day}-${hour}`}
                        className={`w-4 h-4 rounded-sm ${getColor(intensity)} relative group cursor-pointer`}
                        title={`${day} ${hour}:00 - ${value} emails`}
                      >
                        {/* Tooltip on hover */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-10">
                          {day} {hour}:00
                          <br />
                          {value} emails
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Sender Metrics Table
function SenderMetricsTable({ senders }: { senders: any[] }) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-2">Sender</th>
            <th className="text-right p-2">Messages</th>
            <th className="text-right p-2">Response Time</th>
            <th className="text-right p-2">Category</th>
            <th className="text-right p-2">Importance</th>
          </tr>
        </thead>
        <tbody>
          {senders.slice(0, 10).map((sender, idx) => (
            <tr key={idx} className="border-b">
              <td className="p-2">
                <div>
                  <p className="font-medium">{sender.name || sender.email}</p>
                  <p className="text-sm text-muted-foreground">{sender.email}</p>
                </div>
              </td>
              <td className="text-right p-2">{sender.messageCount}</td>
              <td className="text-right p-2">{sender.avgResponseTime?.toFixed(1) || '-'}h</td>
              <td className="text-right p-2">
                <span className="text-sm">{sender.categories?.[0] || 'Uncategorized'}</span>
              </td>
              <td className="text-right p-2">
                <span
                  className={`text-sm px-2 py-1 rounded-full ${
                    sender.importance === 'high'
                      ? 'bg-red-100 text-red-700'
                      : sender.importance === 'medium'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-gray-100 text-gray-700'
                  }`}
                >
                  {sender.importance}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// Rule Effectiveness Table
function RuleEffectivenessTable({ rules }: { rules: any[] }) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-2">Rule Name</th>
            <th className="text-right p-2">Executions</th>
            <th className="text-right p-2">Success Rate</th>
            <th className="text-right p-2">Time Saved</th>
            <th className="text-right p-2">Status</th>
          </tr>
        </thead>
        <tbody>
          {rules.map((rule, idx) => (
            <tr key={idx} className="border-b">
              <td className="p-2 font-medium">{rule.name}</td>
              <td className="text-right p-2">{rule.executions}</td>
              <td className="text-right p-2">{rule.successRate.toFixed(1)}%</td>
              <td className="text-right p-2">{(rule.timeSaved / 60).toFixed(1)}h</td>
              <td className="text-right p-2">
                <span
                  className={`text-sm px-2 py-1 rounded-full ${
                    rule.isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                  }`}
                >
                  {rule.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// Response Time Chart Component
function ResponseTimeChart({ data, isDark }: { data: any; isDark: boolean }) {
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    return data.map((pattern) => ({
      type: pattern.type.replace('-', ' '),
      count: pattern.count,
      percentage: pattern.percentage,
      avgTime: pattern.avgTime || 0,
    }))
  }, [data])

  const colors = {
    immediate: isDark ? '#10b981' : '#059669',
    'same day': isDark ? '#3b82f6' : '#2563eb',
    'next day': isDark ? '#f59e0b' : '#d97706',
    'within week': isDark ? '#8b5cf6' : '#7c3aed',
    'over week': isDark ? '#ef4444' : '#dc2626',
    'no response': isDark ? '#6b7280' : '#4b5563',
  }

  const gridColor = isDark ? '#374151' : '#e5e7eb'
  const textColor = isDark ? '#9ca3af' : '#6b7280'

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
        <XAxis
          dataKey="type"
          stroke={textColor}
          tick={{ fill: textColor }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis stroke={textColor} tick={{ fill: textColor }} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any, name: string) => [
            name === 'count'
              ? `${value} emails`
              : name === 'percentage'
                ? `${value}%`
                : `${value}h avg`,
            name === 'count' ? 'Count' : name === 'percentage' ? 'Percentage' : 'Avg Time',
          ]}
        />
        <Bar dataKey="count" radius={[4, 4, 0, 0]}>
          {chartData.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={colors[entry.type as keyof typeof colors] || colors['no response']}
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// Weekly Pattern Chart Component
function WeeklyPatternChart({ data, isDark }: { data: any; isDark: boolean }) {
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    return data.map((day) => ({
      day: day.day,
      emails: day.emails,
      avgResponseTime: day.avgResponseTime,
    }))
  }, [data])

  const colors = {
    emails: isDark ? '#3b82f6' : '#2563eb',
    responseTime: isDark ? '#f59e0b' : '#d97706',
  }

  const gridColor = isDark ? '#374151' : '#e5e7eb'
  const textColor = isDark ? '#9ca3af' : '#6b7280'

  if (chartData.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No weekly pattern data available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-7 gap-2">
        {chartData.map((day, index) => (
          <div key={day.day} className="text-center">
            <div className="text-xs text-muted-foreground mb-2">{day.day.slice(0, 3)}</div>
            <div
              className={`h-16 rounded-lg flex items-end justify-center text-white text-sm font-medium relative ${
                index === 0 || index === 6 ? 'opacity-60' : ''
              }`}
              style={{
                backgroundColor: colors.emails,
                height: `${Math.max(20, (day.emails / Math.max(...chartData.map((d) => d.emails))) * 64)}px`,
              }}
            >
              {day.emails}
              <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                {day.avgResponseTime.toFixed(1)}h
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded" style={{ backgroundColor: colors.emails }}></div>
          <span>Email Count</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded" style={{ backgroundColor: colors.responseTime }}></div>
          <span>Avg Response Time (shown above bars)</span>
        </div>
      </div>
    </div>
  )
}
