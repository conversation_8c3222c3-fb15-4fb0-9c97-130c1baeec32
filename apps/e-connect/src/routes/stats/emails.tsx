import { <PERSON><PERSON>, LoadingSpinner } from '@luminar/shared-ui'
import { LuminarSelect as Select } from '@luminar/shared-ui/forms'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/Tabs'
import {
  AdjustmentsHorizontalIcon,
  ArrowArrowTrendingDownIcon,
  ArrowArrowTrendingUpIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  CalendarIcon,
  ChartBarIcon,
  ClockIcon,
  DocumentIcon,
  EnvelopeIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PaperClipIcon,
} from '@heroicons/react/24/outline'
import { useQuery } from '@tanstack/react-query'
import { createFileRoute } from '@tanstack/react-router'
import { endOfDay, endOfWeek, format, startOfDay, startOfWeek, subDays, subMonths } from 'date-fns'
import { useMemo, useState } from 'react'
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Composed<PERSON><PERSON>,
  Legend,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  <PERSON>Axis,
} from 'recharts'
import { Card } from '@/components/ui/Card'
import { useTheme } from '../../hooks/useTheme'

export const Route = createFileRoute('/stats/emails')({
  component: EmailVolumeAnalytics,
})

interface DateRange {
  start: Date
  end: Date
  label: string
  granularity: 'hour' | 'day' | 'week' | 'month'
}

const predefinedRanges: DateRange[] = [
  { start: startOfDay(new Date()), end: endOfDay(new Date()), label: 'Today', granularity: 'hour' },
  {
    start: startOfDay(subDays(new Date(), 7)),
    end: endOfDay(new Date()),
    label: 'Last 7 days',
    granularity: 'day',
  },
  {
    start: startOfDay(subDays(new Date(), 30)),
    end: endOfDay(new Date()),
    label: 'Last 30 days',
    granularity: 'day',
  },
  {
    start: startOfDay(subDays(new Date(), 90)),
    end: endOfDay(new Date()),
    label: 'Last 90 days',
    granularity: 'week',
  },
  {
    start: startOfDay(subMonths(new Date(), 6)),
    end: endOfDay(new Date()),
    label: 'Last 6 months',
    granularity: 'month',
  },
  {
    start: startOfDay(subMonths(new Date(), 12)),
    end: endOfDay(new Date()),
    label: 'Last year',
    granularity: 'month',
  },
]

const emailTypes = [
  { value: 'all', label: 'All Emails' },
  { value: 'personal', label: 'Personal' },
  { value: 'work', label: 'Work' },
  { value: 'newsletters', label: 'Newsletters' },
  { value: 'promotional', label: 'Promotional' },
  { value: 'notifications', label: 'Notifications' },
  { value: 'social', label: 'Social' },
]

function EmailVolumeAnalytics() {
  const [dateRange, setDateRange] = useState<DateRange>(predefinedRanges[1])
  const [emailType, setEmailType] = useState('all')
  const [refreshKey, setRefreshKey] = useState(0)
  const [comparison, setComparison] = useState(false)
  const { isDark } = useTheme()

  // Fetch comprehensive email analytics
  const { data: volumeData, isLoading: isLoadingVolume } = useQuery({
    queryKey: ['email-volume', dateRange, emailType, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/email-volume?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&type=${emailType}&granularity=${dateRange.granularity}`
      )
      if (!response.ok) throw new Error('Failed to fetch volume data')
      return response.json()
    },
  })

  const { data: sizeAnalytics, isLoading: isLoadingSize } = useQuery({
    queryKey: ['email-size', dateRange, emailType, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/email-size?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&type=${emailType}`
      )
      if (!response.ok) throw new Error('Failed to fetch size analytics')
      return response.json()
    },
  })

  const { data: attachmentData, isLoading: isLoadingAttachments } = useQuery({
    queryKey: ['attachments', dateRange, emailType, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/attachments?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&type=${emailType}`
      )
      if (!response.ok) throw new Error('Failed to fetch attachment data')
      return response.json()
    },
  })

  const { data: peakAnalysis, isLoading: isLoadingPeaks } = useQuery({
    queryKey: ['peak-analysis', dateRange, emailType, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/peak-activity?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&type=${emailType}`
      )
      if (!response.ok) throw new Error('Failed to fetch peak analysis')
      return response.json()
    },
  })

  const { data: predictiveData, isLoading: isLoadingPredictive } = useQuery({
    queryKey: ['predictive', dateRange, emailType, refreshKey],
    queryFn: async () => {
      const response = await fetch(
        `/api/analytics/predictive?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&type=${emailType}`
      )
      if (!response.ok) throw new Error('Failed to fetch predictive data')
      return response.json()
    },
  })

  const isLoading =
    isLoadingVolume ||
    isLoadingSize ||
    isLoadingAttachments ||
    isLoadingPeaks ||
    isLoadingPredictive

  // Calculate key metrics
  const metrics = useMemo(() => {
    if (!volumeData) return null

    const { totals, trends, anomalies } = volumeData
    const totalEmails = totals.received + totals.sent
    const responsiveRate = totals.received > 0 ? (totals.sent / totals.received) * 100 : 0
    const avgPerDay =
      totalEmails /
      Math.max(
        1,
        Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24))
      )

    return {
      totalEmails,
      received: totals.received,
      sent: totals.sent,
      responsiveRate: responsiveRate.toFixed(1),
      avgPerDay: avgPerDay.toFixed(1),
      growthRate: trends.growthRate || 0,
      anomalyCount: anomalies?.length || 0,
      peakDay: trends.peakDay || 'N/A',
      quietDay: trends.quietDay || 'N/A',
    }
  }, [volumeData, dateRange])

  const storageMetrics = useMemo(() => {
    if (!sizeAnalytics) return null

    const { distribution, trends, storage } = sizeAnalytics
    return {
      totalSize: storage.totalSize,
      avgEmailSize: storage.avgEmailSize,
      largestEmail: storage.largestEmail,
      compressionRatio: storage.compressionRatio || 1,
      storageGrowth: trends.growthRate || 0,
      distribution,
    }
  }, [sizeAnalytics])

  const handleExport = async (format: 'csv' | 'png' | 'pdf') => {
    try {
      const response = await fetch(
        `/api/analytics/export?type=email-volume&format=${format}&start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&emailType=${emailType}`
      )
      if (!response.ok) throw new Error('Export failed')

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `email-analytics-${dateRange.label.toLowerCase().replace(/\s+/g, '-')}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Email Volume Analytics</h1>
          <p className="text-muted-foreground mt-1">
            Comprehensive analysis of email patterns, volume trends, and storage metrics
          </p>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          <Select value={emailType} onValueChange={setEmailType} options={emailTypes} />

          <Select
            value={dateRange.label}
            onValueChange={(value) => {
              const range = predefinedRanges.find((r) => r.label === value)
              if (range) setDateRange(range)
            }}
            options={predefinedRanges.map((r) => ({ value: r.label, label: r.label }))}
          />

          <Button
            variant="outline"
            size="sm"
            onClick={() => setComparison(!comparison)}
            className={comparison ? 'bg-primary text-primary-foreground' : ''}
          >
            Compare
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshKey((prev) => prev + 1)}
            className="gap-2"
          >
            <ArrowPathIcon className="h-4 w-4" />
            Refresh
          </Button>

          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Volume"
          value={metrics?.totalEmails.toLocaleString() || '0'}
          subtitle={`${metrics?.avgPerDay || 0}/day avg`}
          icon={<EnvelopeIcon className="h-5 w-5" />}
          trend={metrics?.growthRate}
          className="bg-blue-50 dark:bg-blue-900/20"
        />

        <MetricCard
          title="Response Rate"
          value={`${metrics?.responsiveRate || 0}%`}
          subtitle={`${metrics?.sent || 0} sent / ${metrics?.received || 0} received`}
          icon={<ArrowTrendingUpIcon className="h-5 w-5" />}
          trend={15.3}
          className="bg-green-50 dark:bg-green-900/20"
        />

        <MetricCard
          title="Storage Used"
          value={formatBytes(storageMetrics?.totalSize || 0)}
          subtitle={`${formatBytes(storageMetrics?.avgEmailSize || 0)} avg`}
          icon={<DocumentIcon className="h-5 w-5" />}
          trend={storageMetrics?.storageGrowth}
          className="bg-purple-50 dark:bg-purple-900/20"
        />

        <MetricCard
          title="Anomalies"
          value={metrics?.anomalyCount.toString() || '0'}
          subtitle="Unusual patterns detected"
          icon={<ExclamationTriangleIcon className="h-5 w-5" />}
          className="bg-orange-50 dark:bg-orange-900/20"
        />
      </div>

      {/* Peak Activity Insights */}
      {peakAnalysis && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Peak Activity Analysis</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h3 className="font-medium">Peak Times</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Busiest Day:</span>
                  <span className="font-medium">{metrics?.peakDay}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Quietest Day:</span>
                  <span className="font-medium">{metrics?.quietDay}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Peak Hour:</span>
                  <span className="font-medium">{peakAnalysis.peakHour || 'N/A'}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium">Recommendations</h3>
              <div className="space-y-2 text-sm">
                {peakAnalysis.recommendations?.map((rec: string, idx: number) => (
                  <div key={idx} className="flex items-start gap-2">
                    <InformationCircleIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span>{rec}</span>
                  </div>
                )) || <p className="text-muted-foreground">No recommendations available</p>}
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium">Efficiency Score</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">{peakAnalysis.efficiencyScore || 85}</span>
                  <span className="text-sm text-muted-foreground">/100</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${peakAnalysis.efficiencyScore || 85}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Based on response times and email processing patterns
                </p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="volume" className="space-y-4">
        <Tabs.List className="grid w-full grid-cols-6">
          <Tabs.Trigger value="volume">Volume Trends</Tabs.Trigger>
          <Tabs.Trigger value="breakdown">Time Breakdown</Tabs.Trigger>
          <Tabs.Trigger value="size">Size Analysis</Tabs.Trigger>
          <Tabs.Trigger value="attachments">Attachments</Tabs.Trigger>
          <Tabs.Trigger value="patterns">Patterns</Tabs.Trigger>
          <Tabs.Trigger value="predictive">Predictive</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="volume" className="space-y-4">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Email Volume Over Time</h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Granularity:</span>
                <span className="text-sm font-medium capitalize">{dateRange.granularity}</span>
              </div>
            </div>
            <EmailVolumeChart data={volumeData?.timeSeries} isDark={isDark} />
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Sent vs Received</h3>
              <SentReceivedChart data={volumeData?.sentVsReceived} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Growth Trend</h3>
              <GrowthTrendChart data={volumeData?.growthData} isDark={isDark} />
            </Card>
          </div>
        </Tabs.Content>

        <Tabs.Content value="breakdown" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Hourly Distribution</h3>
              <HourlyDistributionChart data={volumeData?.hourlyBreakdown} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Daily Patterns</h3>
              <DailyPatternsChart data={volumeData?.dailyPatterns} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Weekly Heatmap</h3>
            <WeeklyHeatmap data={volumeData?.weeklyHeatmap} isDark={isDark} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="size" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Size Distribution</h3>
              <SizeDistributionChart data={storageMetrics?.distribution} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Storage Trends</h3>
              <StorageTrendsChart data={sizeAnalytics?.trends} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Size Statistics</h3>
            <SizeStatisticsTable data={sizeAnalytics?.statistics} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="attachments" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Attachment Types</h3>
              <AttachmentTypesChart data={attachmentData?.typeDistribution} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Size by Type</h3>
              <AttachmentSizeChart data={attachmentData?.sizeByType} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Attachment Trends</h3>
            <AttachmentTrendsChart data={attachmentData?.trends} isDark={isDark} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="patterns" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Volume vs Response Time</h3>
              <VolumeResponseScatter data={volumeData?.correlationData} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Seasonal Patterns</h3>
              <SeasonalPatternsChart data={volumeData?.seasonalData} isDark={isDark} />
            </Card>
          </div>

          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Pattern Analysis</h3>
            <PatternInsightsTable patterns={volumeData?.patterns} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="predictive" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Volume Forecast</h3>
            <PredictiveChart data={predictiveData?.forecast} isDark={isDark} />
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Anomaly Detection</h3>
              <AnomalyChart data={predictiveData?.anomalies} isDark={isDark} />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Insights & Predictions</h3>
              <PredictiveInsights insights={predictiveData?.insights} />
            </Card>
          </div>
        </Tabs.Content>
      </Tabs>
    </div>
  )
}

// Helper function to format bytes
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / k ** i).toFixed(2)) + ' ' + sizes[i]
}

// Metric Card Component
interface MetricCardProps {
  title: string
  value: string
  subtitle: string
  icon: React.ReactNode
  trend?: number
  className?: string
}

function MetricCard({ title, value, subtitle, icon, trend, className }: MetricCardProps) {
  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        </div>
        <div className="p-2 rounded-lg bg-background/50">{icon}</div>
      </div>

      {trend !== undefined && (
        <div className="mt-4 flex items-center gap-1">
          {trend > 0 ? (
            <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />
          )}
          <span className={`text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {Math.abs(trend).toFixed(1)}%
          </span>
          <span className="text-sm text-muted-foreground">vs last period</span>
        </div>
      )}
    </Card>
  )
}

// Chart Components
function EmailVolumeChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No volume data available</p>
      </div>
    )
  }

  const colors = {
    received: isDark ? '#3b82f6' : '#2563eb',
    sent: isDark ? '#10b981' : '#059669',
    total: isDark ? '#8b5cf6' : '#7c3aed',
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis
          dataKey="date"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
        />
        <YAxis
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Legend />
        <Area
          type="monotone"
          dataKey="total"
          stackId="1"
          stroke={colors.total}
          fill={colors.total}
          fillOpacity={0.3}
          name="Total"
        />
        <Bar dataKey="received" fill={colors.received} name="Received" />
        <Bar dataKey="sent" fill={colors.sent} name="Sent" />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

function SentReceivedChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Legend />
        <Line type="monotone" dataKey="sent" stroke="#10b981" strokeWidth={2} name="Sent" />
        <Line type="monotone" dataKey="received" stroke="#3b82f6" strokeWidth={2} name="Received" />
      </LineChart>
    </ResponsiveContainer>
  )
}

function GrowthTrendChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No growth data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="period" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any) => [`${value}%`, 'Growth Rate']}
        />
        <Area
          type="monotone"
          dataKey="growthRate"
          stroke="#8b5cf6"
          fill="#8b5cf6"
          fillOpacity={0.6}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

function HourlyDistributionChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No hourly data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="hour" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="emails" fill="#3b82f6" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function DailyPatternsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No daily pattern data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="day" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="emails" fill="#10b981" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function WeeklyHeatmap({ data, isDark }: { data: any; isDark: boolean }) {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  const hours = Array.from({ length: 24 }, (_, i) => i)

  if (!data || !Array.isArray(data)) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No heatmap data available</p>
      </div>
    )
  }

  const maxValue = Math.max(...data.flat())

  const getIntensity = (value: number) => {
    if (maxValue === 0) return 0
    return value / maxValue
  }

  const getColor = (intensity: number) => {
    if (intensity === 0) return isDark ? 'bg-gray-800' : 'bg-gray-100'
    if (intensity < 0.25) return isDark ? 'bg-blue-900/40' : 'bg-blue-200'
    if (intensity < 0.5) return isDark ? 'bg-blue-700/60' : 'bg-blue-400'
    if (intensity < 0.75) return isDark ? 'bg-blue-600/80' : 'bg-blue-600'
    return isDark ? 'bg-blue-500' : 'bg-blue-800'
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">Email activity by hour and day</p>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Less</span>
          <div className="flex gap-1">
            {[0, 0.25, 0.5, 0.75, 1].map((intensity) => (
              <div key={intensity} className={`w-3 h-3 rounded-sm ${getColor(intensity)}`} />
            ))}
          </div>
          <span>More</span>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-[800px]">
          {/* Hour labels */}
          <div className="flex mb-2">
            <div className="w-12"></div>
            {hours
              .filter((_, i) => i % 2 === 0)
              .map((hour) => (
                <div key={hour} className="w-8 text-xs text-center text-muted-foreground">
                  {hour}
                </div>
              ))}
          </div>

          {/* Grid */}
          <div className="space-y-1">
            {days.map((day, dayIndex) => (
              <div key={day} className="flex items-center">
                <div className="w-12 text-xs text-muted-foreground text-right pr-2">{day}</div>
                <div className="flex gap-1">
                  {hours.map((hour) => {
                    const value = data[hour]?.[dayIndex] || 0
                    const intensity = getIntensity(value)
                    return (
                      <div
                        key={`${day}-${hour}`}
                        className={`w-4 h-4 rounded-sm ${getColor(intensity)} relative group cursor-pointer`}
                        title={`${day} ${hour}:00 - ${value} emails`}
                      >
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-10">
                          {day} {hour}:00
                          <br />
                          {value} emails
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function SizeDistributionChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No size distribution data available</p>
      </div>
    )
  }

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

  return (
    <ResponsiveContainer width="100%" height={250}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="count"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  )
}

function StorageTrendsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No storage trend data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any) => [formatBytes(value), 'Storage Used']}
        />
        <Area type="monotone" dataKey="storage" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.6} />
      </AreaChart>
    </ResponsiveContainer>
  )
}

function AttachmentTypesChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No attachment data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="type" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="count" fill="#f59e0b" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function AttachmentSizeChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No attachment size data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="type" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any) => [formatBytes(value), 'Total Size']}
        />
        <Bar dataKey="totalSize" fill="#ef4444" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function AttachmentTrendsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No attachment trend data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Line
          type="monotone"
          dataKey="count"
          stroke="#f59e0b"
          strokeWidth={2}
          name="Attachment Count"
        />
        <Line type="monotone" dataKey="size" stroke="#ef4444" strokeWidth={2} name="Total Size" />
      </LineChart>
    </ResponsiveContainer>
  )
}

function VolumeResponseScatter({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No correlation data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ScatterChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis
          type="number"
          dataKey="volume"
          name="Volume"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
        />
        <YAxis
          type="number"
          dataKey="responseTime"
          name="Response Time"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any, name: string) => [
            name === 'volume' ? `${value} emails` : `${value}h`,
            name === 'volume' ? 'Volume' : 'Response Time',
          ]}
        />
        <Scatter dataKey="responseTime" fill="#8b5cf6" />
      </ScatterChart>
    </ResponsiveContainer>
  )
}

function SeasonalPatternsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No seasonal data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="month" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Line
          type="monotone"
          dataKey="emails"
          stroke="#06b6d4"
          strokeWidth={3}
          name="Email Volume"
        />
      </LineChart>
    </ResponsiveContainer>
  )
}

function PredictiveChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No forecast data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Legend />
        <Line type="monotone" dataKey="actual" stroke="#3b82f6" strokeWidth={2} name="Actual" />
        <Line
          type="monotone"
          dataKey="predicted"
          stroke="#f59e0b"
          strokeWidth={2}
          strokeDasharray="5 5"
          name="Predicted"
        />
        <Area
          type="monotone"
          dataKey="confidenceUpper"
          stackId="1"
          stroke="none"
          fill="#f59e0b"
          fillOpacity={0.2}
          name="Confidence Interval"
        />
        <Area
          type="monotone"
          dataKey="confidenceLower"
          stackId="1"
          stroke="none"
          fill="#ffffff"
          fillOpacity={1}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

function AnomalyChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No anomaly data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ScatterChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any, name: string) => [
            `${value}${name === 'severity' ? '/10' : ''}`,
            name === 'severity' ? 'Severity' : 'Value',
          ]}
        />
        <Scatter dataKey="severity" fill="#ef4444" />
      </ScatterChart>
    </ResponsiveContainer>
  )
}

// Table Components
function SizeStatisticsTable({ data }: { data: any }) {
  if (!data) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No size statistics available</p>
      </div>
    )
  }

  const stats = [
    { label: 'Average Email Size', value: formatBytes(data.avgSize || 0) },
    { label: 'Median Email Size', value: formatBytes(data.medianSize || 0) },
    { label: 'Largest Email', value: formatBytes(data.maxSize || 0) },
    { label: 'Smallest Email', value: formatBytes(data.minSize || 0) },
    { label: '95th Percentile', value: formatBytes(data.p95Size || 0) },
    { label: 'Storage Efficiency', value: `${(data.compressionRatio * 100 || 100).toFixed(1)}%` },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {stats.map((stat, index) => (
        <div key={index} className="p-4 border rounded-lg">
          <p className="text-sm text-muted-foreground">{stat.label}</p>
          <p className="text-lg font-semibold">{stat.value}</p>
        </div>
      ))}
    </div>
  )
}

function PatternInsightsTable({ patterns }: { patterns: any[] }) {
  if (!patterns || patterns.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No patterns detected</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">Pattern</th>
            <th className="text-left p-3">Description</th>
            <th className="text-right p-3">Confidence</th>
            <th className="text-right p-3">Impact</th>
          </tr>
        </thead>
        <tbody>
          {patterns.map((pattern, index) => (
            <tr key={index} className="border-b">
              <td className="p-3 font-medium">{pattern.name}</td>
              <td className="p-3 text-sm text-muted-foreground">{pattern.description}</td>
              <td className="p-3 text-right">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    pattern.confidence > 80
                      ? 'bg-green-100 text-green-700'
                      : pattern.confidence > 60
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-red-100 text-red-700'
                  }`}
                >
                  {pattern.confidence}%
                </span>
              </td>
              <td className="p-3 text-right">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    pattern.impact === 'high'
                      ? 'bg-red-100 text-red-700'
                      : pattern.impact === 'medium'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-green-100 text-green-700'
                  }`}
                >
                  {pattern.impact}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function PredictiveInsights({ insights }: { insights: any }) {
  if (!insights) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No predictive insights available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <h4 className="font-medium">Next Week Forecast</h4>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Expected Volume:</span>
            <span className="font-medium">{insights.nextWeek?.volume || 0} emails</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Peak Day:</span>
            <span className="font-medium">{insights.nextWeek?.peakDay || 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Confidence:</span>
            <span className="font-medium">{insights.nextWeek?.confidence || 0}%</span>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="font-medium">Recommendations</h4>
        <div className="space-y-2">
          {insights.recommendations?.map((rec: string, index: number) => (
            <div key={index} className="flex items-start gap-2">
              <InformationCircleIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <span className="text-sm">{rec}</span>
            </div>
          )) || <p className="text-sm text-muted-foreground">No recommendations available</p>}
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="font-medium">Trends</h4>
        <div className="space-y-2">
          {insights.trends?.map((trend: any, index: number) => (
            <div key={index} className="flex justify-between items-center">
              <span className="text-sm">{trend.name}</span>
              <span
                className={`text-sm font-medium ${
                  trend.direction === 'up'
                    ? 'text-green-600'
                    : trend.direction === 'down'
                      ? 'text-red-600'
                      : 'text-gray-600'
                }`}
              >
                {trend.direction === 'up' ? '↗' : trend.direction === 'down' ? '↘' : '→'}{' '}
                {trend.change}%
              </span>
            </div>
          )) || <p className="text-sm text-muted-foreground">No trends available</p>}
        </div>
      </div>
    </div>
  )
}
