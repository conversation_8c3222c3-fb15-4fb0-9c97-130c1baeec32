import { <PERSON><PERSON>, LuminarBadge } from '@luminar/shared-ui'
import { LuminarCheckbox, LuminarInput, LuminarSelect } from '@luminar/shared-ui/forms'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/Tabs'
import { Toast } from '@/components/ui/Toast'
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  Copy,
  Eye,
  Info,
  Minus,
  Plus,
  Save,
  Settings,
  Shield,
  Target,
  Trash2,
  Zap,
} from 'lucide-react'
import type React from 'react'
import { useCallback, useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useRulesStore } from '@/stores/rulesStore'
import {
  type Rule,
  type RuleAction,
  type RuleActionType,
  type RuleCondition,
  type RuleConditionType,
  type RuleOperator,
  RuleSchedule,
} from '@/types/rules'
import { RuleValidator } from '@/utils/rules'

interface RuleFormProps {
  rule?: Rule
  onSave: (rule: Rule) => void
  onCancel: () => void
  onPreview?: (rule: Partial<Rule>) => void
}

const CONDITION_TYPES: Array<{ value: RuleConditionType; label: string; description: string }> = [
  { value: 'from', label: 'From Address', description: 'Sender email address' },
  { value: 'to', label: 'To Address', description: 'Recipient email address' },
  { value: 'subject', label: 'Subject', description: 'Email subject line' },
  { value: 'body', label: 'Body Content', description: 'Email body text' },
  { value: 'domain', label: 'Domain', description: 'Sender domain' },
  { value: 'hasAttachment', label: 'Has Attachment', description: 'Email contains attachments' },
  {
    value: 'attachmentType',
    label: 'Attachment Type',
    description: 'Type of attachment (pdf, jpg, etc.)',
  },
  {
    value: 'attachmentSize',
    label: 'Attachment Size',
    description: 'Size of attachments in bytes',
  },
  { value: 'isUnread', label: 'Is Unread', description: 'Email is unread' },
  { value: 'isImportant', label: 'Is Important', description: 'Email is marked important' },
  { value: 'isStarred', label: 'Is Starred', description: 'Email is starred' },
  { value: 'category', label: 'Category', description: 'Email category' },
  { value: 'label', label: 'Label', description: 'Email labels' },
  { value: 'age', label: 'Email Age', description: 'Age of email in days' },
  { value: 'size', label: 'Email Size', description: 'Size of email in bytes' },
  { value: 'keyword', label: 'Keyword', description: 'Specific keywords' },
  { value: 'regex', label: 'Regular Expression', description: 'Pattern matching' },
  { value: 'custom', label: 'Custom Field', description: 'Custom email field' },
]

const OPERATORS: Array<{ value: RuleOperator; label: string; description: string }> = [
  { value: 'equals', label: 'Equals', description: 'Exact match' },
  { value: 'notEquals', label: 'Not Equals', description: 'Not equal to' },
  { value: 'contains', label: 'Contains', description: 'Contains text' },
  { value: 'notContains', label: 'Not Contains', description: 'Does not contain text' },
  { value: 'startsWith', label: 'Starts With', description: 'Begins with text' },
  { value: 'endsWith', label: 'Ends With', description: 'Ends with text' },
  { value: 'matches', label: 'Matches Regex', description: 'Matches regular expression' },
  { value: 'in', label: 'In List', description: 'One of multiple values' },
  { value: 'notIn', label: 'Not In List', description: 'Not in list of values' },
  { value: 'greaterThan', label: 'Greater Than', description: 'Numeric comparison' },
  { value: 'lessThan', label: 'Less Than', description: 'Numeric comparison' },
  { value: 'greaterThanOrEqual', label: 'Greater or Equal', description: 'Numeric comparison' },
  { value: 'lessThanOrEqual', label: 'Less or Equal', description: 'Numeric comparison' },
  { value: 'between', label: 'Between', description: 'Within range' },
  { value: 'exists', label: 'Exists', description: 'Field has a value' },
  { value: 'notExists', label: 'Not Exists', description: 'Field is empty' },
]

const ACTION_TYPES: Array<{
  value: RuleActionType
  label: string
  description: string
  category: string
}> = [
  { value: 'archive', label: 'Archive', description: 'Move to archive', category: 'Organization' },
  {
    value: 'delete',
    label: 'Delete',
    description: 'Delete email permanently',
    category: 'Organization',
  },
  {
    value: 'markRead',
    label: 'Mark as Read',
    description: 'Mark email as read',
    category: 'Status',
  },
  {
    value: 'markUnread',
    label: 'Mark as Unread',
    description: 'Mark email as unread',
    category: 'Status',
  },
  { value: 'star', label: 'Star', description: 'Add star to email', category: 'Status' },
  {
    value: 'unstar',
    label: 'Remove Star',
    description: 'Remove star from email',
    category: 'Status',
  },
  {
    value: 'markImportant',
    label: 'Mark Important',
    description: 'Mark as important',
    category: 'Status',
  },
  {
    value: 'markNotImportant',
    label: 'Mark Not Important',
    description: 'Remove importance',
    category: 'Status',
  },
  {
    value: 'label',
    label: 'Add Label',
    description: 'Add label to email',
    category: 'Organization',
  },
  {
    value: 'removeLabel',
    label: 'Remove Label',
    description: 'Remove label from email',
    category: 'Organization',
  },
  {
    value: 'categorize',
    label: 'Categorize',
    description: 'Set email category',
    category: 'Organization',
  },
  {
    value: 'moveToFolder',
    label: 'Move to Folder',
    description: 'Move to specific folder',
    category: 'Organization',
  },
  {
    value: 'forward',
    label: 'Forward',
    description: 'Forward email to address',
    category: 'Communication',
  },
  {
    value: 'reply',
    label: 'Auto Reply',
    description: 'Send automatic reply',
    category: 'Communication',
  },
  { value: 'snooze', label: 'Snooze', description: 'Snooze for later', category: 'Schedule' },
  {
    value: 'createTask',
    label: 'Create Task',
    description: 'Create task from email',
    category: 'Productivity',
  },
  {
    value: 'webhook',
    label: 'Webhook',
    description: 'Call external webhook',
    category: 'Integration',
  },
  {
    value: 'notification',
    label: 'Send Notification',
    description: 'Send custom notification',
    category: 'Communication',
  },
  { value: 'aiProcess', label: 'AI Processing', description: 'Process with AI', category: 'AI' },
  {
    value: 'custom',
    label: 'Custom Action',
    description: 'Custom action script',
    category: 'Advanced',
  },
]

export const RuleForm: React.FC<RuleFormProps> = ({ rule, onSave, onCancel, onPreview }) => {
  const { createRule, updateRule, templates, fetchTemplates } = useRulesStore()

  // Form state
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<Partial<Rule>>({
    name: '',
    description: '',
    enabled: true,
    priority: 5,
    conditions: [],
    conditionLogic: 'all',
    actions: [],
    tags: [],
    schedule: { type: 'immediate' },
    exceptions: [],
  })

  const [validation, setValidation] = useState<{ valid: boolean; errors: string[] }>({
    valid: true,
    errors: [],
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const steps = [
    { id: 'basic', title: 'Basic Info', icon: Settings },
    { id: 'conditions', title: 'Conditions', icon: Target },
    { id: 'actions', title: 'Actions', icon: Zap },
    { id: 'schedule', title: 'Schedule', icon: Clock },
    { id: 'review', title: 'Review', icon: Eye },
  ]

  // Initialize form with existing rule data
  useEffect(() => {
    if (rule) {
      setFormData({
        ...rule,
        conditions: rule.conditions || [],
        actions: rule.actions || [],
        tags: rule.tags || [],
        schedule: rule.schedule || { type: 'immediate' },
        exceptions: rule.exceptions || [],
      })
    }
  }, [rule])

  // Load templates
  useEffect(() => {
    fetchTemplates()
  }, [fetchTemplates])

  // Validate form
  useEffect(() => {
    const result = RuleValidator.validateRule(formData)
    setValidation(result)
  }, [formData])

  // Update form data
  const updateFormData = useCallback((updates: Partial<Rule>) => {
    setFormData((prev) => ({ ...prev, ...updates }))
  }, [])

  // Add condition
  const addCondition = useCallback(() => {
    const newCondition: RuleCondition = {
      id: `condition-${Date.now()}`,
      type: 'from',
      operator: 'contains',
      value: '',
      caseSensitive: false,
    }

    updateFormData({
      conditions: [...(formData.conditions || []), newCondition],
    })
  }, [formData.conditions, updateFormData])

  // Update condition
  const updateCondition = useCallback(
    (index: number, updates: Partial<RuleCondition>) => {
      const conditions = [...(formData.conditions || [])]
      conditions[index] = { ...conditions[index], ...updates }
      updateFormData({ conditions })
    },
    [formData.conditions, updateFormData]
  )

  // Remove condition
  const removeCondition = useCallback(
    (index: number) => {
      const conditions = [...(formData.conditions || [])]
      conditions.splice(index, 1)
      updateFormData({ conditions })
    },
    [formData.conditions, updateFormData]
  )

  // Add action
  const addAction = useCallback(() => {
    const newAction: RuleAction = {
      id: `action-${Date.now()}`,
      type: 'archive',
      order: (formData.actions?.length || 0) + 1,
    }

    updateFormData({
      actions: [...(formData.actions || []), newAction],
    })
  }, [formData.actions, updateFormData])

  // Update action
  const updateAction = useCallback(
    (index: number, updates: Partial<RuleAction>) => {
      const actions = [...(formData.actions || [])]
      actions[index] = { ...actions[index], ...updates }
      updateFormData({ actions })
    },
    [formData.actions, updateFormData]
  )

  // Remove action
  const removeAction = useCallback(
    (index: number) => {
      const actions = [...(formData.actions || [])]
      actions.splice(index, 1)
      // Reorder remaining actions
      actions.forEach((action, i) => {
        action.order = i + 1
      })
      updateFormData({ actions })
    },
    [formData.actions, updateFormData]
  )

  // Handle save
  const handleSave = async () => {
    if (!validation.valid) {
      setError('Please fix validation errors before saving')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const ruleData = {
        ...formData,
        updatedAt: new Date(),
      } as Rule

      if (rule?.id) {
        await updateRule(rule.id, ruleData)
      } else {
        await createRule(ruleData)
      }

      onSave(ruleData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save rule')
    } finally {
      setIsLoading(false)
    }
  }

  // Navigate steps
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Load from template
  const loadTemplate = (templateId: string) => {
    const template = templates.find((t) => t.id === templateId)
    if (template) {
      setFormData({
        ...formData,
        ...template.rule,
        name: template.rule.name || template.name,
        description: template.rule.description || template.description,
      })
    }
  }

  // Render basic info step
  const renderBasicStep = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium mb-2">Rule Name *</label>
        <LuminarInput
          value={formData.name || ''}
          onChange={(e) => updateFormData({ name: e.target.value })}
          placeholder="Enter rule name"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Description</label>
        <textarea
          className="w-full p-3 border rounded-md resize-none"
          rows={3}
          value={formData.description || ''}
          onChange={(e) => updateFormData({ description: e.target.value })}
          placeholder="Describe what this rule does"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Priority *</label>
          <LuminarSelect
            value={formData.priority?.toString() || '5'}
            onValueChange={(value) => updateFormData({ priority: parseInt(value) })}
          >
            <option value="0">0 - Highest</option>
            <option value="1">1 - High</option>
            <option value="2">2 - Above Normal</option>
            <option value="3">3 - Normal</option>
            <option value="4">4 - Below Normal</option>
            <option value="5">5 - Low</option>
            <option value="6">6 - Lowest</option>
          </LuminarSelect>
          <p className="text-xs text-gray-500 mt-1">Lower numbers have higher priority</p>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Status</label>
          <div className="flex items-center gap-2">
            <LuminarCheckbox
              checked={formData.enabled || false}
              onCheckedChange={(checked) => updateFormData({ enabled: checked })}
            />
            <span>Enable rule</span>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Tags</label>
        <LuminarInput
          value={formData.tags?.join(', ') || ''}
          onChange={(e) =>
            updateFormData({
              tags: e.target.value
                .split(',')
                .map((tag) => tag.trim())
                .filter(Boolean),
            })
          }
          placeholder="Enter tags separated by commas"
        />
      </div>

      {templates.length > 0 && (
        <div>
          <label className="block text-sm font-medium mb-2">Start from Template</label>
          <LuminarSelect value="" onValueChange={loadTemplate}>
            <option value="">Choose a template...</option>
            {templates.map((template) => (
              <option key={template.id} value={template.id}>
                {template.name} - {template.description}
              </option>
            ))}
          </LuminarSelect>
        </div>
      )}
    </div>
  )

  // Render conditions step
  const renderConditionsStep = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Rule Conditions</h3>
          <p className="text-sm text-gray-600">Define when this rule should trigger</p>
        </div>
        <Button onClick={addCondition}>
          <Plus className="w-4 h-4 mr-2" />
          Add Condition
        </Button>
      </div>

      {formData.conditions && formData.conditions.length > 1 && (
        <div>
          <label className="block text-sm font-medium mb-2">Condition Logic</label>
          <LuminarSelect
            value={formData.conditionLogic || 'all'}
            onValueChange={(value) => updateFormData({ conditionLogic: value as 'all' | 'any' })}
          >
            <option value="all">ALL conditions must match (AND)</option>
            <option value="any">ANY condition can match (OR)</option>
          </LuminarSelect>
        </div>
      )}

      <div className="space-y-4">
        {formData.conditions?.map((condition, index) => (
          <Card key={condition.id} className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Field</label>
                <LuminarSelect
                  value={condition.type}
                  onValueChange={(value) =>
                    updateCondition(index, { type: value as RuleConditionType })
                  }
                >
                  {CONDITION_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </LuminarSelect>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Operator</label>
                <LuminarSelect
                  value={condition.operator}
                  onValueChange={(value) =>
                    updateCondition(index, { operator: value as RuleOperator })
                  }
                >
                  {OPERATORS.map((op) => (
                    <option key={op.value} value={op.value}>
                      {op.label}
                    </option>
                  ))}
                </LuminarSelect>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Value</label>
                {condition.type === 'hasAttachment' ||
                condition.type === 'isUnread' ||
                condition.type === 'isImportant' ||
                condition.type === 'isStarred' ? (
                  <LuminarSelect
                    value={condition.value?.toString() || 'true'}
                    onValueChange={(value) => updateCondition(index, { value: value === 'true' })}
                  >
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </LuminarSelect>
                ) : (
                  <LuminarInput
                    value={
                      Array.isArray(condition.value)
                        ? condition.value.join(', ')
                        : condition.value?.toString() || ''
                    }
                    onChange={(e) => {
                      const value = e.target.value
                      updateCondition(index, {
                        value:
                          condition.operator === 'in' || condition.operator === 'notIn'
                            ? value.split(',').map((v) => v.trim())
                            : value,
                      })
                    }}
                    placeholder="Enter value"
                  />
                )}
              </div>

              <div className="flex items-end gap-2">
                <div className="flex items-center gap-2">
                  <LuminarCheckbox
                    checked={condition.negate || false}
                    onCheckedChange={(checked) => updateCondition(index, { negate: checked })}
                  />
                  <span className="text-sm">NOT</span>
                </div>
                <Button variant="outline" size="sm" onClick={() => removeCondition(index)}>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {(condition.type === 'from' ||
              condition.type === 'to' ||
              condition.type === 'subject' ||
              condition.type === 'body') && (
              <div className="mt-3">
                <label className="flex items-center gap-2">
                  <LuminarCheckbox
                    checked={condition.caseSensitive || false}
                    onCheckedChange={(checked) =>
                      updateCondition(index, { caseSensitive: checked })
                    }
                  />
                  <span className="text-sm">Case sensitive</span>
                </label>
              </div>
            )}
          </Card>
        ))}
      </div>

      {(!formData.conditions || formData.conditions.length === 0) && (
        <Card className="p-8 text-center">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No conditions defined</h3>
          <p className="text-gray-600 mb-4">
            Add at least one condition to specify when this rule should trigger.
          </p>
          <Button onClick={addCondition}>
            <Plus className="w-4 h-4 mr-2" />
            Add First Condition
          </Button>
        </Card>
      )}
    </div>
  )

  // Render actions step
  const renderActionsStep = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Rule Actions</h3>
          <p className="text-sm text-gray-600">Define what happens when conditions match</p>
        </div>
        <Button onClick={addAction}>
          <Plus className="w-4 h-4 mr-2" />
          Add Action
        </Button>
      </div>

      <div className="space-y-4">
        {formData.actions?.map((action, index) => (
          <Card key={action.id} className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Action Type</label>
                <LuminarSelect
                  value={action.type}
                  onValueChange={(value) => updateAction(index, { type: value as RuleActionType })}
                >
                  {Object.entries(
                    ACTION_TYPES.reduce(
                      (acc, action) => {
                        if (!acc[action.category]) acc[action.category] = []
                        acc[action.category].push(action)
                        return acc
                      },
                      {} as Record<string, typeof ACTION_TYPES>
                    )
                  ).map(([category, actions]) => (
                    <optgroup key={category} label={category}>
                      {actions.map((actionType) => (
                        <option key={actionType.value} value={actionType.value}>
                          {actionType.label}
                        </option>
                      ))}
                    </optgroup>
                  ))}
                </LuminarSelect>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Order</label>
                <LuminarInput
                  type="number"
                  min="1"
                  value={action.order}
                  onChange={(e) => updateAction(index, { order: parseInt(e.target.value) || 1 })}
                />
              </div>

              <div className="flex items-end">
                <Button variant="outline" size="sm" onClick={() => removeAction(index)}>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Action-specific parameters */}
            {(action.type === 'label' || action.type === 'removeLabel') && (
              <div className="mt-4">
                <label className="block text-sm font-medium mb-1">Label Name</label>
                <LuminarInput
                  value={action.value?.toString() || ''}
                  onChange={(e) => updateAction(index, { value: e.target.value })}
                  placeholder="Enter label name"
                />
              </div>
            )}

            {action.type === 'categorize' && (
              <div className="mt-4">
                <label className="block text-sm font-medium mb-1">Category</label>
                <LuminarSelect
                  value={action.value?.toString() || ''}
                  onValueChange={(value) => updateAction(index, { value })}
                >
                  <option value="primary">Primary</option>
                  <option value="social">Social</option>
                  <option value="promotions">Promotions</option>
                  <option value="updates">Updates</option>
                  <option value="forums">Forums</option>
                </LuminarSelect>
              </div>
            )}

            {action.type === 'moveToFolder' && (
              <div className="mt-4">
                <label className="block text-sm font-medium mb-1">Folder Path</label>
                <LuminarInput
                  value={action.params?.folder || ''}
                  onChange={(e) =>
                    updateAction(index, {
                      params: { ...action.params, folder: e.target.value },
                    })
                  }
                  placeholder="e.g., Archive/2024"
                />
              </div>
            )}

            {action.type === 'forward' && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Forward To</label>
                  <LuminarInput
                    type="email"
                    value={action.params?.address || ''}
                    onChange={(e) =>
                      updateAction(index, {
                        params: { ...action.params, address: e.target.value },
                      })
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Subject Prefix</label>
                  <LuminarInput
                    value={action.params?.subject || ''}
                    onChange={(e) =>
                      updateAction(index, {
                        params: { ...action.params, subject: e.target.value },
                      })
                    }
                    placeholder="Fwd:"
                  />
                </div>
              </div>
            )}

            {action.type === 'reply' && (
              <div className="mt-4">
                <label className="block text-sm font-medium mb-1">Reply Template</label>
                <textarea
                  className="w-full p-3 border rounded-md resize-none"
                  rows={3}
                  value={action.template || ''}
                  onChange={(e) => updateAction(index, { template: e.target.value })}
                  placeholder="Enter your auto-reply message"
                />
              </div>
            )}

            {action.type === 'snooze' && (
              <div className="mt-4">
                <label className="block text-sm font-medium mb-1">Snooze Duration (minutes)</label>
                <LuminarSelect
                  value={action.params?.duration?.toString() || '60'}
                  onValueChange={(value) =>
                    updateAction(index, {
                      params: { ...action.params, duration: parseInt(value) },
                    })
                  }
                >
                  <option value="30">30 minutes</option>
                  <option value="60">1 hour</option>
                  <option value="240">4 hours</option>
                  <option value="480">8 hours</option>
                  <option value="1440">1 day</option>
                  <option value="10080">1 week</option>
                </LuminarSelect>
              </div>
            )}

            {action.type === 'webhook' && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Webhook URL</label>
                  <LuminarInput
                    type="url"
                    value={action.params?.url || ''}
                    onChange={(e) =>
                      updateAction(index, {
                        params: { ...action.params, url: e.target.value },
                      })
                    }
                    placeholder="https://api.example.com/webhook"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Method</label>
                  <LuminarSelect
                    value={action.params?.method || 'POST'}
                    onValueChange={(value) =>
                      updateAction(index, {
                        params: { ...action.params, method: value },
                      })
                    }
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="PATCH">PATCH</option>
                  </LuminarSelect>
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      {(!formData.actions || formData.actions.length === 0) && (
        <Card className="p-8 text-center">
          <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No actions defined</h3>
          <p className="text-gray-600 mb-4">
            Add at least one action to specify what should happen.
          </p>
          <Button onClick={addAction}>
            <Plus className="w-4 h-4 mr-2" />
            Add First Action
          </Button>
        </Card>
      )}
    </div>
  )

  // Render schedule step
  const renderScheduleStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Rule Scheduling</h3>
        <p className="text-sm text-gray-600">Configure when and how often this rule should run</p>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Schedule Type</label>
        <LuminarSelect
          value={formData.schedule?.type || 'immediate'}
          onValueChange={(value) =>
            updateFormData({
              schedule: { ...formData.schedule, type: value as any },
            })
          }
        >
          <option value="immediate">Immediate (on email arrival)</option>
          <option value="delayed">Delayed execution</option>
          <option value="scheduled">Scheduled time</option>
          <option value="recurring">Recurring schedule</option>
        </LuminarSelect>
      </div>

      {formData.schedule?.type === 'delayed' && (
        <div>
          <label className="block text-sm font-medium mb-2">Delay (minutes)</label>
          <LuminarInput
            type="number"
            min="1"
            value={formData.schedule.delay || 5}
            onChange={(e) =>
              updateFormData({
                schedule: { ...formData.schedule, delay: parseInt(e.target.value) || 5 },
              })
            }
          />
        </div>
      )}

      {(formData.schedule?.type === 'scheduled' || formData.schedule?.type === 'recurring') && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Time</label>
            <LuminarInput
              type="time"
              value={formData.schedule.time || '09:00'}
              onChange={(e) =>
                updateFormData({
                  schedule: { ...formData.schedule, time: e.target.value },
                })
              }
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Timezone</label>
            <LuminarSelect
              value={formData.schedule.timezone || 'UTC'}
              onValueChange={(value) =>
                updateFormData({
                  schedule: { ...formData.schedule, timezone: value },
                })
              }
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
            </LuminarSelect>
          </div>
        </div>
      )}

      {(formData.schedule?.type === 'scheduled' || formData.schedule?.type === 'recurring') && (
        <div>
          <label className="block text-sm font-medium mb-2">Days of Week</label>
          <div className="grid grid-cols-4 md:grid-cols-7 gap-2">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
              <label key={day} className="flex items-center gap-2">
                <LuminarCheckbox
                  checked={formData.schedule?.days?.includes(index) || false}
                  onCheckedChange={(checked) => {
                    const days = formData.schedule?.days || []
                    const newDays = checked ? [...days, index] : days.filter((d) => d !== index)
                    updateFormData({
                      schedule: { ...formData.schedule, days: newDays },
                    })
                  }}
                />
                <span className="text-sm">{day}</span>
              </label>
            ))}
          </div>
        </div>
      )}

      <div className="border-t pt-6">
        <Button variant="outline" onClick={() => setShowAdvanced(!showAdvanced)} className="mb-4">
          {showAdvanced ? (
            <ChevronUp className="w-4 h-4 mr-2" />
          ) : (
            <ChevronDown className="w-4 h-4 mr-2" />
          )}
          Advanced Options
        </Button>

        {showAdvanced && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Exceptions</label>
              <p className="text-sm text-gray-600 mb-2">
                Define exceptions where this rule should NOT apply
              </p>
              {/* Exception configuration would go here */}
              <div className="p-4 border rounded-md bg-gray-50">
                <p className="text-sm text-gray-600">Exception configuration coming soon...</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // Render review step
  const renderReviewStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Review Rule Configuration</h3>
        <p className="text-sm text-gray-600">Review all settings before saving</p>
      </div>

      {/* Validation errors */}
      {!validation.valid && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-900">Validation Errors</h4>
              <ul className="text-sm text-red-700 mt-1 list-disc list-inside">
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}

      {/* Basic info summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Basic Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Name:</span> {formData.name}
            </div>
            <div>
              <span className="font-medium">Priority:</span> {formData.priority}
            </div>
            <div>
              <span className="font-medium">Status:</span>{' '}
              <Badge variant={formData.enabled ? 'default' : 'secondary'}>
                {formData.enabled ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Tags:</span>{' '}
              {formData.tags?.length ? formData.tags.join(', ') : 'None'}
            </div>
          </div>
          {formData.description && (
            <div className="mt-4">
              <span className="font-medium">Description:</span>
              <p className="text-gray-600 mt-1">{formData.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Conditions summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Conditions ({formData.conditionLogic?.toUpperCase()})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {formData.conditions?.length ? (
            <div className="space-y-2">
              {formData.conditions.map((condition, index) => (
                <div key={condition.id} className="p-3 bg-gray-50 rounded-md">
                  <span className="font-mono text-sm">
                    {condition.negate && 'NOT '}
                    {CONDITION_TYPES.find((t) => t.value === condition.type)?.label}{' '}
                    {OPERATORS.find((o) => o.value === condition.operator)?.label} "
                    {Array.isArray(condition.value) ? condition.value.join(', ') : condition.value}"
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-600">No conditions defined</p>
          )}
        </CardContent>
      </Card>

      {/* Actions summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Actions</CardTitle>
        </CardHeader>
        <CardContent>
          {formData.actions?.length ? (
            <div className="space-y-2">
              {formData.actions
                .sort((a, b) => a.order - b.order)
                .map((action, index) => (
                  <div key={action.id} className="p-3 bg-gray-50 rounded-md">
                    <span className="font-mono text-sm">
                      {action.order}. {ACTION_TYPES.find((t) => t.value === action.type)?.label}
                      {action.value && ` (${action.value})`}
                    </span>
                  </div>
                ))}
            </div>
          ) : (
            <p className="text-gray-600">No actions defined</p>
          )}
        </CardContent>
      </Card>

      {/* Schedule summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="font-mono text-sm">
            Type: {formData.schedule?.type || 'immediate'}
            {formData.schedule?.delay && ` (${formData.schedule.delay} minutes delay)`}
            {formData.schedule?.time && ` at ${formData.schedule.time}`}
          </p>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">{rule ? 'Edit Rule' : 'Create New Rule'}</h1>
        <p className="text-gray-600">
          {rule ? 'Modify your existing rule configuration' : 'Set up a new email automation rule'}
        </p>
      </div>

      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isActive = index === currentStep
            const isCompleted = index < currentStep

            return (
              <div key={step.id} className="flex items-center">
                <div
                  className={`
                  w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium
                  ${
                    isActive
                      ? 'bg-blue-600 text-white'
                      : isCompleted
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                  }
                `}
                >
                  {isCompleted ? <CheckCircle className="w-5 h-5" /> : <Icon className="w-5 h-5" />}
                </div>
                <span
                  className={`ml-2 text-sm font-medium ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-600'
                  }`}
                >
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div
                    className={`w-16 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}`}
                  />
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Step content */}
      <Card className="mb-8">
        <CardContent className="p-6">
          {currentStep === 0 && renderBasicStep()}
          {currentStep === 1 && renderConditionsStep()}
          {currentStep === 2 && renderActionsStep()}
          {currentStep === 3 && renderScheduleStep()}
          {currentStep === 4 && renderReviewStep()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={currentStep === 0 ? onCancel : prevStep}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          {currentStep === 0 ? 'Cancel' : 'Previous'}
        </Button>

        <div className="flex gap-2">
          {onPreview && (
            <Button variant="outline" onClick={() => onPreview(formData)}>
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
          )}

          {currentStep === steps.length - 1 ? (
            <Button
              onClick={handleSave}
              disabled={!validation.valid || isLoading}
              loading={isLoading}
            >
              <Save className="w-4 h-4 mr-2" />
              {rule ? 'Update Rule' : 'Create Rule'}
            </Button>
          ) : (
            <Button onClick={nextStep}>
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>

      {/* Error Toast */}
      {error && <Toast type="error" message={error} onClose={() => setError(null)} />}
    </div>
  )
}

export default RuleForm
