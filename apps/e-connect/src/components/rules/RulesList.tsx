import {  Button, Checkbox, Input, LuminarBadge, Modal, Select, Toast  } from '@luminar/shared-ui'
import {
  AlertTriangle,
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  BarChart3,
  CheckCircle,
  Clock,
  Copy,
  Download,
  Edit,
  Eye,
  Filter,
  MoreHorizontal,
  Pause,
  Play,
  Plus,
  Search,
  Settings,
  Target,
  Trash2,
  TrendingUp,
  Upload,
} from 'lucide-react'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useRulesStore } from '@/stores/rulesStore'
import type { Rule } from '@/types/rules'

interface RulesListProps {
  onCreateRule?: () => void
  onEditRule?: (rule: Rule) => void
  onViewRule?: (rule: Rule) => void
  onTestRule?: (rule: Rule) => void
}

export const RulesList: React.FC<RulesListProps> = ({
  onCreateRule,
  onEditRule,
  onViewRule,
  onTestRule,
}) => {
  const {
    // State
    rules,
    isLoading,
    error,
    searchQuery,
    selectedTags,
    enabledFilter,
    sortBy,
    sortOrder,
    selectedRules,
    performanceData,

    // Actions
    fetchRules,
    setSearchQuery,
    setSelectedTags,
    setEnabledFilter,
    setSorting,
    selectRule,
    selectAllRules,
    clearSelection,
    toggleRule,
    deleteRule,
    duplicateRule,
    bulkToggleRules,
    bulkDeleteRules,
    bulkUpdatePriority,
    exportRules,
    importRules,
    updatePerformanceData,
    getFilteredRules,
    getRuleConflicts,
  } = useRulesStore()

  // Local state
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importData, setImportData] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [ruleToDelete, setRuleToDelete] = useState<string | null>(null)
  const [showConflicts, setShowConflicts] = useState(false)
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table')

  // Get filtered and sorted rules
  const filteredRules = useMemo(
    () => getFilteredRules(),
    [rules, searchQuery, selectedTags, enabledFilter, sortBy, sortOrder]
  )

  // Get rule conflicts
  const conflicts = useMemo(() => getRuleConflicts(), [rules])

  // Available tags for filtering
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>()
    rules.forEach((rule) => {
      rule.tags?.forEach((tag) => tagSet.add(tag))
    })
    return Array.from(tagSet)
  }, [rules])

  // Load rules on mount
  useEffect(() => {
    fetchRules()
    updatePerformanceData()
  }, [])

  // Handle sort change
  const handleSort = (newSortBy: string) => {
    const newOrder = sortBy === newSortBy && sortOrder === 'asc' ? 'desc' : 'asc'
    setSorting(newSortBy, newOrder)
  }

  // Handle rule toggle
  const handleToggleRule = async (ruleId: string) => {
    try {
      await toggleRule(ruleId)
    } catch (error) {
      console.error('Failed to toggle rule:', error)
    }
  }

  // Handle rule deletion
  const handleDeleteRule = async (ruleId: string) => {
    setRuleToDelete(ruleId)
    setShowDeleteConfirm(true)
  }

  const confirmDelete = async () => {
    if (!ruleToDelete) return

    try {
      await deleteRule(ruleToDelete)
      setShowDeleteConfirm(false)
      setRuleToDelete(null)
    } catch (error) {
      console.error('Failed to delete rule:', error)
    }
  }

  // Handle rule duplication
  const handleDuplicateRule = async (ruleId: string) => {
    try {
      await duplicateRule(ruleId)
    } catch (error) {
      console.error('Failed to duplicate rule:', error)
    }
  }

  // Handle bulk operations
  const handleBulkEnable = () => bulkToggleRules(true)
  const handleBulkDisable = () => bulkToggleRules(false)
  const handleBulkDelete = () => bulkDeleteRules()

  // Handle export
  const handleExport = async () => {
    try {
      const data = await exportRules(selectedRules.length > 0 ? selectedRules : undefined)
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `rules-export-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
      setShowExportDialog(false)
    } catch (error) {
      console.error('Failed to export rules:', error)
    }
  }

  // Handle import
  const handleImport = async () => {
    try {
      await importRules(importData)
      setShowImportDialog(false)
      setImportData('')
      await fetchRules()
    } catch (error) {
      console.error('Failed to import rules:', error)
    }
  }

  // Render sort icon
  const renderSortIcon = (column: string) => {
    if (sortBy !== column) return <ArrowUpDown className="w-4 h-4 text-gray-400" />
    return sortOrder === 'asc' ? (
      <ArrowUp className="w-4 h-4 text-blue-500" />
    ) : (
      <ArrowDown className="w-4 h-4 text-blue-500" />
    )
  }

  // Render performance metrics
  const renderPerformanceMetrics = () => (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Rules</p>
              <p className="text-2xl font-bold">{performanceData.totalRules}</p>
            </div>
            <Target className="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Rules</p>
              <p className="text-2xl font-bold text-green-600">{performanceData.activeRules}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-green-600">
                {(performanceData.successRate * 100).toFixed(1)}%
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg. Execution</p>
              <p className="text-2xl font-bold">{Math.round(performanceData.avgExecutionTime)}ms</p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </CardContent>
      </Card>
    </div>
  )

  // Render filters
  const renderFilters = () => (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-wrap gap-4 items-center">
          {/* Search */}
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search rules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Status filter */}
          <Select value={enabledFilter} onValueChange={(value) => setEnabledFilter(value as any)}>
            <option value="all">All Rules</option>
            <option value="enabled">Enabled Only</option>
            <option value="disabled">Disabled Only</option>
          </Select>

          {/* Tags filter */}
          <Select
            value={selectedTags.join(',')}
            onValueChange={(value) => setSelectedTags(value ? value.split(',') : [])}
          >
            <option value="">All Tags</option>
            {availableTags.map((tag) => (
              <option key={tag} value={tag}>
                {tag}
              </option>
            ))}
          </Select>

          {/* View mode toggle */}
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              Table
            </Button>
            <Button
              variant={viewMode === 'cards' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('cards')}
            >
              Cards
            </Button>
          </div>

          {/* Conflicts indicator */}
          {conflicts.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowConflicts(true)}
              className="text-red-600 border-red-200"
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              {conflicts.length} Conflicts
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )

  // Render bulk actions
  const renderBulkActions = () => {
    if (selectedRules.length === 0) return null

    return (
      <Card className="mb-4 border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              {selectedRules.length} rule{selectedRules.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex gap-2">
              <Button size="sm" onClick={handleBulkEnable}>
                Enable
              </Button>
              <Button size="sm" onClick={handleBulkDisable}>
                Disable
              </Button>
              <Button size="sm" variant="destructive" onClick={handleBulkDelete}>
                Delete
              </Button>
              <Button size="sm" variant="outline" onClick={clearSelection}>
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Render rule row (for table view)
  const renderRuleRow = (rule: Rule) => (
    <tr key={rule.id} className="border-b hover:bg-gray-50">
      <td className="px-4 py-3">
        <Checkbox
          checked={selectedRules.includes(rule.id)}
          onCheckedChange={() => selectRule(rule.id)}
        />
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center gap-2">
          <span className="font-medium">{rule.name}</span>
          {!rule.enabled && <Badge variant="secondary">Disabled</Badge>}
        </div>
        {rule.description && <p className="text-sm text-gray-600 mt-1">{rule.description}</p>}
        {rule.tags && (
          <div className="flex gap-1 mt-2">
            {rule.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {rule.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{rule.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </td>
      <td className="px-4 py-3 text-center">
        <Badge
          variant={
            rule.priority === 0 ? 'destructive' : rule.priority <= 2 ? 'default' : 'secondary'
          }
        >
          {rule.priority}
        </Badge>
      </td>
      <td className="px-4 py-3 text-center">
        <div className="text-sm">
          <div className="font-medium">{rule.stats?.totalMatches || 0}</div>
          <div className="text-gray-500">matches</div>
        </div>
      </td>
      <td className="px-4 py-3 text-center">
        <div className="text-sm">
          <div className="font-medium">
            {rule.stats?.totalActions > 0
              ? ((rule.stats.successfulActions / rule.stats.totalActions) * 100).toFixed(1)
              : '0'}
            %
          </div>
          <div className="text-gray-500">success</div>
        </div>
      </td>
      <td className="px-4 py-3 text-center">
        <div className="text-sm">
          <div className="font-medium">{rule.stats?.avgProcessingTime || 0}ms</div>
          <div className="text-gray-500">avg time</div>
        </div>
      </td>
      <td className="px-4 py-3 text-center">
        <div className="text-sm text-gray-500">
          {rule.lastTriggered ? new Date(rule.lastTriggered).toLocaleDateString() : 'Never'}
        </div>
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center gap-1">
          <Button size="sm" variant="ghost" onClick={() => handleToggleRule(rule.id)}>
            {rule.enabled ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </Button>
          <Button size="sm" variant="ghost" onClick={() => onViewRule?.(rule)}>
            <Eye className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="ghost" onClick={() => onEditRule?.(rule)}>
            <Edit className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="ghost" onClick={() => onTestRule?.(rule)}>
            <BarChart3 className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="ghost" onClick={() => handleDuplicateRule(rule.id)}>
            <Copy className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="ghost" onClick={() => handleDeleteRule(rule.id)}>
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </td>
    </tr>
  )

  // Render rule card (for card view)
  const renderRuleCard = (rule: Rule) => (
    <Card key={rule.id} className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <Checkbox
              checked={selectedRules.includes(rule.id)}
              onCheckedChange={() => selectRule(rule.id)}
            />
            <div>
              <CardTitle className="text-lg">{rule.name}</CardTitle>
              {rule.description && <p className="text-sm text-gray-600 mt-1">{rule.description}</p>}
            </div>
          </div>
          <Badge variant={rule.enabled ? 'default' : 'secondary'}>
            {rule.enabled ? 'Enabled' : 'Disabled'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm text-gray-600">Priority</p>
            <p className="font-medium">{rule.priority}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Matches</p>
            <p className="font-medium">{rule.stats?.totalMatches || 0}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Success Rate</p>
            <p className="font-medium">
              {rule.stats?.totalActions > 0
                ? ((rule.stats.successfulActions / rule.stats.totalActions) * 100).toFixed(1)
                : '0'}
              %
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Avg Time</p>
            <p className="font-medium">{rule.stats?.avgProcessingTime || 0}ms</p>
          </div>
        </div>

        {rule.tags && (
          <div className="flex flex-wrap gap-1 mb-4">
            {rule.tags.map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Last triggered:{' '}
            {rule.lastTriggered ? new Date(rule.lastTriggered).toLocaleDateString() : 'Never'}
          </div>
          <div className="flex gap-1">
            <Button size="sm" variant="ghost" onClick={() => handleToggleRule(rule.id)}>
              {rule.enabled ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button size="sm" variant="ghost" onClick={() => onEditRule?.(rule)}>
              <Edit className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="ghost" onClick={() => onTestRule?.(rule)}>
              <BarChart3 className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="ghost" onClick={() => handleDuplicateRule(rule.id)}>
              <Copy className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // Loading state
  if (isLoading && rules.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading rules...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Email Rules</h1>
          <p className="text-gray-600">Manage your email automation rules</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowImportDialog(true)}>
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" onClick={() => setShowExportDialog(true)}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={onCreateRule}>
            <Plus className="w-4 h-4 mr-2" />
            Create Rule
          </Button>
        </div>
      </div>

      {/* Performance Metrics */}
      {renderPerformanceMetrics()}

      {/* Filters */}
      {renderFilters()}

      {/* Bulk Actions */}
      {renderBulkActions()}

      {/* Rules List */}
      {viewMode === 'table' ? (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left">
                      <Checkbox
                        checked={
                          selectedRules.length === filteredRules.length && filteredRules.length > 0
                        }
                        onCheckedChange={selectAllRules}
                      />
                    </th>
                    <th
                      className="px-4 py-3 text-left cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        Rule Name
                        {renderSortIcon('name')}
                      </div>
                    </th>
                    <th
                      className="px-4 py-3 text-center cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('priority')}
                    >
                      <div className="flex items-center justify-center gap-2">
                        Priority
                        {renderSortIcon('priority')}
                      </div>
                    </th>
                    <th
                      className="px-4 py-3 text-center cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('matches')}
                    >
                      <div className="flex items-center justify-center gap-2">
                        Matches
                        {renderSortIcon('matches')}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center">Success Rate</th>
                    <th className="px-4 py-3 text-center">Performance</th>
                    <th
                      className="px-4 py-3 text-center cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('lastTriggered')}
                    >
                      <div className="flex items-center justify-center gap-2">
                        Last Triggered
                        {renderSortIcon('lastTriggered')}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center">Actions</th>
                  </tr>
                </thead>
                <tbody>{filteredRules.map(renderRuleRow)}</tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredRules.map(renderRuleCard)}
        </div>
      )}

      {filteredRules.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No rules found</h3>
            <p className="text-gray-600 mb-4">
              {rules.length === 0
                ? "You haven't created any rules yet. Create your first rule to get started."
                : 'No rules match your current filters. Try adjusting your search criteria.'}
            </p>
            {rules.length === 0 && (
              <Button onClick={onCreateRule}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Rule
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">Delete Rule</h3>
          <p className="text-gray-600 mb-6">
            Are you sure you want to delete this rule? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">Export Rules</h3>
          <p className="text-gray-600 mb-6">
            {selectedRules.length > 0
              ? `Export ${selectedRules.length} selected rule${selectedRules.length > 1 ? 's' : ''}`
              : 'Export all rules'}{' '}
            as JSON file.
          </p>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowExportDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleExport}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">Import Rules</h3>
          <p className="text-gray-600 mb-4">Paste your exported rules JSON data below:</p>
          <textarea
            className="w-full h-64 p-3 border rounded-md font-mono text-sm"
            placeholder="Paste JSON data here..."
            value={importData}
            onChange={(e) => setImportData(e.target.value)}
          />
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowImportDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleImport} disabled={!importData.trim()}>
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Conflicts Dialog */}
      <Dialog open={showConflicts} onOpenChange={setShowConflicts}>
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">Rule Conflicts</h3>
          <div className="space-y-4">
            {conflicts.map((conflict, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-500" />
                  <span className="font-medium">
                    {conflict.severity === 'error' ? 'Error' : 'Warning'}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{conflict.reason}</p>
              </div>
            ))}
          </div>
          <div className="flex justify-end mt-6">
            <Button onClick={() => setShowConflicts(false)}>Close</Button>
          </div>
        </div>
      </Dialog>

      {/* Error Toast */}
      {error && (
        <Toast type="error" message={error} onClose={() => useRulesStore.getState().clearError()} />
      )}
    </div>
  )
}

export default RulesList
