import {  Button, Input, LuminarBadge, Select  } from '@luminar/shared-ui'
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Calendar,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Download,
  Eye,
  Filter,
  MoreHorizontal,
  PieChart,
  RefreshCw,
  RotateCcw,
  Search,
  Target,
  TrendingDown,
  TrendingUp,
  XCircle,
  Zap,
} from 'lucide-react'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useRulesStore } from '@/stores/rulesStore'
import { Rule, type RuleExecution } from '@/types/rules'

interface RuleHistoryProps {
  ruleId?: string
  onClose?: () => void
}

interface ExecutionStats {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  avgExecutionTime: number
  successRate: number
  timeRange: {
    start: Date
    end: Date
  }
  trends: {
    executionCount: Array<{ date: string; count: number }>
    successRate: Array<{ date: string; rate: number }>
    avgTime: Array<{ date: string; time: number }>
  }
  topActions: Array<{ action: string; count: number }>
  errorDistribution: Array<{ error: string; count: number }>
}

export const RuleHistory: React.FC<RuleHistoryProps> = ({ ruleId, onClose }) => {
  const { rules, executions, fetchExecutions, getExecutionsByRule, rollbackExecution } =
    useRulesStore()

  // State
  const [selectedRule, setSelectedRule] = useState<string>(ruleId || 'all')
  const [timeRange, setTimeRange] = useState<string>('7d')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(50)
  const [sortBy, setSortBy] = useState<'timestamp' | 'duration' | 'rule'>('timestamp')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [selectedExecution, setSelectedExecution] = useState<RuleExecution | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Load executions on mount and when filters change
  useEffect(() => {
    loadExecutions()
  }, [selectedRule, timeRange])

  const loadExecutions = async () => {
    setIsLoading(true)
    try {
      if (selectedRule === 'all') {
        await fetchExecutions()
      } else {
        await fetchExecutions(selectedRule)
      }
    } catch (error) {
      console.error('Failed to load executions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Filter and sort executions
  const filteredExecutions = useMemo(() => {
    let filtered = executions

    // Filter by rule
    if (selectedRule !== 'all') {
      filtered = filtered.filter((exec) => exec.ruleId === selectedRule)
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter((exec) => {
        switch (statusFilter) {
          case 'success':
            return exec.success
          case 'failed':
            return !exec.success
          case 'errors':
            return exec.error
          default:
            return true
        }
      })
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (exec) =>
          exec.messageId.toLowerCase().includes(query) ||
          exec.threadId.toLowerCase().includes(query) ||
          exec.error?.toLowerCase().includes(query) ||
          rules
            .find((r) => r.id === exec.ruleId)
            ?.name.toLowerCase()
            .includes(query)
      )
    }

    // Filter by time range
    const now = new Date()
    const startDate = new Date()
    switch (timeRange) {
      case '1d':
        startDate.setDate(now.getDate() - 1)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
    }
    filtered = filtered.filter((exec) => exec.timestamp >= startDate)

    // Sort
    filtered.sort((a, b) => {
      let aVal: any, bVal: any

      switch (sortBy) {
        case 'timestamp':
          aVal = a.timestamp.getTime()
          bVal = b.timestamp.getTime()
          break
        case 'duration':
          aVal = a.totalTime
          bVal = b.totalTime
          break
        case 'rule': {
          const ruleA = rules.find((r) => r.id === a.ruleId)
          const ruleB = rules.find((r) => r.id === b.ruleId)
          aVal = ruleA?.name || ''
          bVal = ruleB?.name || ''
          break
        }
        default:
          return 0
      }

      if (sortOrder === 'asc') {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0
      }
    })

    return filtered
  }, [executions, selectedRule, statusFilter, searchQuery, timeRange, sortBy, sortOrder, rules])

  // Calculate statistics
  const stats = useMemo((): ExecutionStats => {
    const filtered = filteredExecutions
    const successful = filtered.filter((exec) => exec.success)
    const failed = filtered.filter((exec) => !exec.success)

    const totalTime = filtered.reduce((sum, exec) => sum + exec.totalTime, 0)
    const avgTime = filtered.length > 0 ? totalTime / filtered.length : 0

    // Calculate trends (group by day)
    const groupedByDay = filtered.reduce(
      (acc, exec) => {
        const date = exec.timestamp.toISOString().split('T')[0]
        if (!acc[date]) {
          acc[date] = { executions: [], successCount: 0, totalTime: 0 }
        }
        acc[date].executions.push(exec)
        if (exec.success) acc[date].successCount++
        acc[date].totalTime += exec.totalTime
        return acc
      },
      {} as Record<string, { executions: RuleExecution[]; successCount: number; totalTime: number }>
    )

    const executionTrend = Object.entries(groupedByDay)
      .map(([date, data]) => ({
        date,
        count: data.executions.length,
      }))
      .sort((a, b) => a.date.localeCompare(b.date))

    const successRateTrend = Object.entries(groupedByDay)
      .map(([date, data]) => ({
        date,
        rate: data.executions.length > 0 ? (data.successCount / data.executions.length) * 100 : 0,
      }))
      .sort((a, b) => a.date.localeCompare(b.date))

    const avgTimeTrend = Object.entries(groupedByDay)
      .map(([date, data]) => ({
        date,
        time: data.executions.length > 0 ? data.totalTime / data.executions.length : 0,
      }))
      .sort((a, b) => a.date.localeCompare(b.date))

    // Top actions
    const actionCounts = filtered.reduce(
      (acc, exec) => {
        exec.actions.forEach((action) => {
          if (action.executed) {
            acc[action.actionId] = (acc[action.actionId] || 0) + 1
          }
        })
        return acc
      },
      {} as Record<string, number>
    )

    const topActions = Object.entries(actionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([action, count]) => ({ action, count }))

    // Error distribution
    const errorCounts = failed.reduce(
      (acc, exec) => {
        const error = exec.error || 'Unknown error'
        acc[error] = (acc[error] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const errorDistribution = Object.entries(errorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([error, count]) => ({ error, count }))

    return {
      totalExecutions: filtered.length,
      successfulExecutions: successful.length,
      failedExecutions: failed.length,
      avgExecutionTime: avgTime,
      successRate: filtered.length > 0 ? (successful.length / filtered.length) * 100 : 0,
      timeRange: {
        start: new Date(Math.min(...filtered.map((e) => e.timestamp.getTime()))),
        end: new Date(Math.max(...filtered.map((e) => e.timestamp.getTime()))),
      },
      trends: {
        executionCount: executionTrend,
        successRate: successRateTrend,
        avgTime: avgTimeTrend,
      },
      topActions,
      errorDistribution,
    }
  }, [filteredExecutions])

  // Paginated executions
  const paginatedExecutions = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredExecutions.slice(startIndex, startIndex + pageSize)
  }, [filteredExecutions, currentPage, pageSize])

  const totalPages = Math.ceil(filteredExecutions.length / pageSize)

  // Handle rollback
  const handleRollback = async (executionId: string) => {
    try {
      await rollbackExecution(executionId)
      await loadExecutions()
    } catch (error) {
      console.error('Rollback failed:', error)
    }
  }

  // Render trend chart (simplified)
  const renderTrendChart = (
    data: Array<{ date: string; count?: number; rate?: number; time?: number }>,
    title: string,
    valueKey: string
  ) => (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-32 flex items-end justify-between gap-1">
          {data.slice(-7).map((point, index) => {
            const value = point[valueKey as keyof typeof point] as number
            const maxValue = Math.max(...data.map((p) => p[valueKey as keyof typeof p] as number))
            const height = maxValue > 0 ? (value / maxValue) * 100 : 0

            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="bg-blue-500 w-full rounded-t" style={{ height: `${height}%` }} />
                <div className="text-xs text-gray-500 mt-1">{new Date(point.date).getDate()}</div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )

  // Render execution details modal
  const renderExecutionDetails = () => {
    if (!selectedExecution) return null

    const rule = rules.find((r) => r.id === selectedExecution.ruleId)

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold">Execution Details</h2>
              <Button variant="outline" onClick={() => setSelectedExecution(null)}>
                Close
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Basic Information</h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Rule:</span> {rule?.name || 'Unknown'}
                    </div>
                    <div>
                      <span className="font-medium">Message ID:</span> {selectedExecution.messageId}
                    </div>
                    <div>
                      <span className="font-medium">Thread ID:</span> {selectedExecution.threadId}
                    </div>
                    <div>
                      <span className="font-medium">Timestamp:</span>{' '}
                      {selectedExecution.timestamp.toLocaleString()}
                    </div>
                    <div>
                      <span className="font-medium">Total Time:</span> {selectedExecution.totalTime}
                      ms
                    </div>
                    <div>
                      <span className="font-medium">Status:</span>{' '}
                      <Badge variant={selectedExecution.success ? 'default' : 'destructive'}>
                        {selectedExecution.success ? 'Success' : 'Failed'}
                      </Badge>
                    </div>
                    {selectedExecution.error && (
                      <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700">
                        <span className="font-medium">Error:</span> {selectedExecution.error}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Conditions */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Condition Results</h3>
                  <div className="space-y-2">
                    {selectedExecution.conditions.map((condition, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        {condition.matched ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                        <span>Condition {index + 1}</span>
                        <span className="text-gray-500">({condition.evaluationTime}ms)</span>
                        {condition.details && (
                          <span className="text-xs text-gray-400">
                            {JSON.stringify(condition.details)}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="md:col-span-2">
                <h3 className="font-medium mb-2">Action Results</h3>
                <div className="space-y-2">
                  {selectedExecution.actions.map((action, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {action.success ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                          <span className="font-medium">Action {index + 1}</span>
                          <span className="text-sm text-gray-500">
                            {action.executed ? 'Executed' : 'Skipped'}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">{action.executionTime}ms</span>
                      </div>

                      {action.error && (
                        <div className="text-sm text-red-600 mb-2">Error: {action.error}</div>
                      )}

                      {action.result && (
                        <div className="text-sm text-gray-600">
                          Result: {JSON.stringify(action.result)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Rollback button for reversible actions */}
            {selectedExecution.success && (
              <div className="mt-6 pt-6 border-t">
                <Button variant="outline" onClick={() => handleRollback(selectedExecution.id)}>
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Rollback Actions
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  Attempt to reverse the actions performed by this execution
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Rule Execution History</h1>
          <p className="text-gray-600">Monitor and analyze rule execution performance</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadExecutions} loading={isLoading}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Rule</label>
              <Select value={selectedRule} onValueChange={setSelectedRule}>
                <option value="all">All Rules</option>
                {rules.map((rule) => (
                  <option key={rule.id} value={rule.id}>
                    {rule.name}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Time Range</label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <option value="1d">Last 24 hours</option>
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <option value="all">All</option>
                <option value="success">Success</option>
                <option value="failed">Failed</option>
                <option value="errors">With Errors</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Sort By</label>
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
                <option value="timestamp">Timestamp</option>
                <option value="duration">Duration</option>
                <option value="rule">Rule</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Order</label>
              <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as any)}>
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Search</label>
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Executions</p>
                <p className="text-2xl font-bold">{stats.totalExecutions}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-green-600">{stats.successRate.toFixed(1)}%</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Execution Time</p>
                <p className="text-2xl font-bold">{Math.round(stats.avgExecutionTime)}ms</p>
              </div>
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Failed Executions</p>
                <p className="text-2xl font-bold text-red-600">{stats.failedExecutions}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Trend Charts */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {renderTrendChart(stats.trends.executionCount, 'Execution Count', 'count')}
        {renderTrendChart(stats.trends.successRate, 'Success Rate (%)', 'rate')}
        {renderTrendChart(stats.trends.avgTime, 'Avg Time (ms)', 'time')}
      </div>

      {/* Execution List */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Execution History</CardTitle>
            <div className="flex items-center gap-2">
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => setPageSize(parseInt(value))}
              >
                <option value="25">25 per page</option>
                <option value="50">50 per page</option>
                <option value="100">100 per page</option>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  <th className="text-left py-2">Timestamp</th>
                  <th className="text-left py-2">Rule</th>
                  <th className="text-left py-2">Message ID</th>
                  <th className="text-center py-2">Status</th>
                  <th className="text-center py-2">Conditions</th>
                  <th className="text-center py-2">Actions</th>
                  <th className="text-center py-2">Duration</th>
                  <th className="text-center py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedExecutions.map((execution) => {
                  const rule = rules.find((r) => r.id === execution.ruleId)
                  const conditionsMatched = execution.conditions.filter((c) => c.matched).length
                  const actionsExecuted = execution.actions.filter((a) => a.executed).length

                  return (
                    <tr key={execution.id} className="border-b hover:bg-gray-50">
                      <td className="py-3">
                        <div className="text-sm">{execution.timestamp.toLocaleDateString()}</div>
                        <div className="text-xs text-gray-500">
                          {execution.timestamp.toLocaleTimeString()}
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="font-medium">{rule?.name || 'Unknown'}</div>
                        {rule?.priority !== undefined && (
                          <div className="text-xs text-gray-500">Priority: {rule.priority}</div>
                        )}
                      </td>
                      <td className="py-3">
                        <div className="font-mono text-sm">{execution.messageId}</div>
                        <div className="font-mono text-xs text-gray-500">{execution.threadId}</div>
                      </td>
                      <td className="py-3 text-center">
                        <Badge variant={execution.success ? 'default' : 'destructive'}>
                          {execution.success ? 'Success' : 'Failed'}
                        </Badge>
                        {execution.error && <div className="text-xs text-red-600 mt-1">Error</div>}
                      </td>
                      <td className="py-3 text-center">
                        <div className="text-sm">
                          {conditionsMatched}/{execution.conditions.length}
                        </div>
                        <div className="text-xs text-gray-500">matched</div>
                      </td>
                      <td className="py-3 text-center">
                        <div className="text-sm">
                          {actionsExecuted}/{execution.actions.length}
                        </div>
                        <div className="text-xs text-gray-500">executed</div>
                      </td>
                      <td className="py-3 text-center">
                        <div className="text-sm">{execution.totalTime}ms</div>
                      </td>
                      <td className="py-3 text-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedExecution(execution)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {filteredExecutions.length === 0 && (
            <div className="text-center py-8">
              <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No executions found</h3>
              <p className="text-gray-600">No rule executions match your current filters.</p>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Showing {(currentPage - 1) * pageSize + 1} to{' '}
                {Math.min(currentPage * pageSize, filteredExecutions.length)} of{' '}
                {filteredExecutions.length} executions
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Analysis */}
      {stats.errorDistribution.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Error Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.errorDistribution.map((error, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-red-50 rounded-lg"
                >
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                    <span className="text-sm">{error.error}</span>
                  </div>
                  <Badge variant="destructive">{error.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Execution Details Modal */}
      {renderExecutionDetails()}
    </div>
  )
}

export default RuleHistory
