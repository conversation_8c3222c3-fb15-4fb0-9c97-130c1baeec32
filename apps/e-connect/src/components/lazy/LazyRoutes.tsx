import { LoadingSpinner } from '@luminar/shared-ui'
/**
 * Lazy loaded route components for better performance
 * NOTE: This file is currently not in use and needs proper implementation
 * for TanStack Router lazy loading patterns
 */

import { lazy, Suspense } from 'react'

// TODO: Implement proper lazy loading for TanStack Router
// These are currently commented out due to TypeScript errors
// as TanStack Router uses a different lazy loading pattern

/* 
export const LazyAnalyticsDashboard = lazy(() => import('@/routes/stats/index'))

export const LazyAutomationRules = lazy(() => import('@/routes/automation/rules'))

export const LazyAssistantChat = lazy(() => import('@/routes/assistant/index'))

export const LazyBulkUnsubscribe = lazy(() => import('@/routes/bulk-unsubscribe/index'))

export const LazyCleanInbox = lazy(() => import('@/routes/clean/index'))

export const LazySettings = lazy(() => import('@/routes/settings/index'))
*/

// Lazy load heavy component groups
export const LazyChartComponents = lazy(() =>
  import('@/components/charts/ChartWrapper').then((module) => ({ default: module.ChartWrapper }))
)

export const LazyEmailList = lazy(() =>
  import('@/components/EmailList').then((module) => ({ default: module.EmailList }))
)

export const LazyRulesForm = lazy(() =>
  import('@/components/rules/RuleForm').then((module) => ({ default: module.RuleForm }))
)

export const LazyBulkActions = lazy(() =>
  import('@/components/bulk-actions/BulkUnsubscribe').then((module) => ({
    default: module.BulkUnsubscribe,
  }))
)

// Suspense wrapper component
interface LazySuspenseProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function LazySuspense({ children, fallback }: LazySuspenseProps) {
  return (
    <Suspense
      fallback={
        fallback || (
          <div className="flex items-center justify-center h-64">
            <Loading size="lg" />
          </div>
        )
      }
    >
      {children}
    </Suspense>
  )
}

// Higher-order component for lazy loading
export function withLazyLoading<T extends object>(
  Component: React.ComponentType<T>,
  fallback?: React.ReactNode
) {
  return function LazyComponent(props: T) {
    return (
      <LazySuspense fallback={fallback}>
        <Component {...props} />
      </LazySuspense>
    )
  }
}

// Preload utilities for better UX
export const preloadRoutes = {
  analytics: () => import('@/routes/stats/index'),
  automation: () => import('@/routes/automation/rules'),
  assistant: () => import('@/routes/assistant/index'),
  bulkUnsubscribe: () => import('@/routes/bulk-unsubscribe/index'),
  cleanInbox: () => import('@/routes/clean/index'),
  settings: () => import('@/routes/settings/index'),
}

export const preloadComponents = {
  charts: () => import('@/components/charts/ChartWrapper'),
  emailList: () => import('@/components/EmailList'),
  rulesForm: () => import('@/components/rules/RuleForm'),
  bulkActions: () => import('@/components/bulk-actions/BulkUnsubscribe'),
}

// Hook for preloading on hover or focus
export function usePreload() {
  const preloadRoute = (routeName: keyof typeof preloadRoutes) => {
    preloadRoutes[routeName]().catch(console.error)
  }

  const preloadComponent = (componentName: keyof typeof preloadComponents) => {
    preloadComponents[componentName]().catch(console.error)
  }

  return { preloadRoute, preloadComponent }
}
