import {
  AlertCircle,
  Archive,
  BarChart,
  Brain,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Clock,
  Database,
  Download,
  Eye,
  EyeOff,
  FileText,
  Filter,
  FolderOpen,
  History,
  Inbox,
  Info,
  Layers,
  Mail,
  Package,
  Paperclip,
  PauseCircle,
  PlayCircle,
  RefreshCw,
  Save,
  Search,
  Settings,
  Shield,
  Sparkles,
  Tag,
  Target,
  Trash2,
  TrendingDown,
  TrendingUp,
  Undo2,
  User,
  Wand2,
  XCircle,
  Zap,
} from 'lucide-react'
import type React from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import type { BulkOperation } from '../../types/bulk'
import type { ParsedMessage, Thread } from '../../types/email'
import { Badge } from '../ui/Badge'
import { Button } from '../ui/Button'
import { Card } from '../ui/Card'
import { Checkbox } from '../ui/Checkbox'
import { Dialog } from '../ui/Dialog'
import { Input } from '../ui/Input'
import { Loading } from '../ui/Loading'
import { Progress } from '../ui/Progress'
import { RadioGroup } from '../ui/RadioGroup'
import { Select } from '../ui/Select'
import { Slider } from '../ui/Slider'
import { Tabs } from '../ui/Tabs'
import { Toast } from '../ui/Toast'
import { Toggle } from '../ui/Toggle'
import { Tooltip } from '../ui/Tooltip'

// Types for Clean Inbox feature
interface CleaningCategory {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  count: number
  sizeInBytes: number
  suggestedAction: 'archive' | 'delete' | 'keep'
  confidence: number // AI confidence score
  examples: string[]
  criteria: CleaningCriteria
}

interface CleaningCriteria {
  ageInDays?: number
  senders?: string[]
  senderPatterns?: string[]
  subjects?: string[]
  subjectPatterns?: string[]
  hasAttachments?: boolean
  isUnread?: boolean
  isStarred?: boolean
  labels?: string[]
  sizeThreshold?: number
  customRules?: CustomRule[]
}

interface CustomRule {
  id: string
  name: string
  condition: 'and' | 'or'
  filters: Array<{
    field: 'sender' | 'subject' | 'body' | 'date' | 'size' | 'attachments' | 'labels'
    operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'greater' | 'less' | 'regex'
    value: string | number | Date
  }>
  action: 'archive' | 'delete' | 'label' | 'keep'
}

interface CleaningWorkflow {
  id: string
  name: string
  description: string
  steps: WorkflowStep[]
  isDefault?: boolean
  lastUsed?: Date
  successRate?: number
}

interface WorkflowStep {
  id: string
  name: string
  description: string
  criteria: CleaningCriteria
  action: 'archive' | 'delete' | 'label' | 'review'
  requiresConfirmation?: boolean
  estimatedImpact: {
    emails: number
    sizeInBytes: number
  }
}

interface CleaningSession {
  id: string
  startedAt: Date
  completedAt?: Date
  workflow: CleaningWorkflow
  status: 'analyzing' | 'preview' | 'cleaning' | 'completed' | 'failed'
  categories: CleaningCategory[]
  selectedCategories: Set<string>
  selectedParsedMessages: Set<string>
  progress: {
    current: number
    total: number
    currentStep?: string
    percentage: number
  }
  results?: CleaningResults
  backup?: BackupInfo
}

interface CleaningResults {
  archived: number
  deleted: number
  kept: number
  errors: number
  storageFreed: number
  timeElapsed: number
  recommendations: string[]
}

interface BackupInfo {
  id: string
  createdAt: Date
  expiresAt: Date
  size: number
  canRestore: boolean
}

interface CleaningAnalytics {
  totalParsedMessages: number
  totalSize: number
  categoriesFound: number
  potentialSavings: {
    emails: number
    storage: number
    percentage: number
  }
  topSenders: Array<{
    email: string
    name: string
    count: number
    size: number
  }>
  emailsByAge: Array<{
    label: string
    count: number
    size: number
  }>
  growthRate: {
    daily: number
    weekly: number
    monthly: number
  }
}

interface CleanInboxProps {
  className?: string
  onComplete?: (session: CleaningSession) => void
}

type ViewMode = 'setup' | 'analysis' | 'preview' | 'cleaning' | 'results'
type WorkflowType = 'quick' | 'deep' | 'custom' | 'smart'

// Pre-defined workflows
const DEFAULT_WORKFLOWS: CleaningWorkflow[] = [
  {
    id: 'quick-clean',
    name: 'Quick Clean',
    description: 'Fast cleanup of obvious junk and old emails',
    isDefault: true,
    steps: [
      {
        id: 'step-1',
        name: 'Remove old marketing emails',
        description: 'Delete marketing emails older than 30 days',
        criteria: {
          ageInDays: 30,
          labels: ['marketing', 'promotions'],
        },
        action: 'delete',
        estimatedImpact: { emails: 0, sizeInBytes: 0 },
      },
      {
        id: 'step-2',
        name: 'Archive old notifications',
        description: 'Archive notification emails older than 7 days',
        criteria: {
          ageInDays: 7,
          labels: ['notifications', 'social'],
        },
        action: 'archive',
        estimatedImpact: { emails: 0, sizeInBytes: 0 },
      },
    ],
  },
  {
    id: 'deep-clean',
    name: 'Deep Clean',
    description: 'Thorough analysis and cleanup of your entire inbox',
    steps: [
      {
        id: 'step-1',
        name: 'Analyze all emails',
        description: 'AI-powered categorization of all emails',
        criteria: {},
        action: 'review',
        requiresConfirmation: true,
        estimatedImpact: { emails: 0, sizeInBytes: 0 },
      },
      {
        id: 'step-2',
        name: 'Clean by category',
        description: 'Review and clean each category',
        criteria: {},
        action: 'review',
        requiresConfirmation: true,
        estimatedImpact: { emails: 0, sizeInBytes: 0 },
      },
    ],
  },
  {
    id: 'smart-clean',
    name: 'Smart Clean',
    description: 'AI-powered intelligent cleaning based on your patterns',
    steps: [
      {
        id: 'step-1',
        name: 'Learn patterns',
        description: 'Analyze your email habits and patterns',
        criteria: {},
        action: 'review',
        estimatedImpact: { emails: 0, sizeInBytes: 0 },
      },
      {
        id: 'step-2',
        name: 'Apply smart rules',
        description: 'Clean based on learned patterns',
        criteria: {},
        action: 'review',
        requiresConfirmation: true,
        estimatedImpact: { emails: 0, sizeInBytes: 0 },
      },
    ],
  },
]

export function CleanInbox({ className, onComplete }: CleanInboxProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('setup')
  const [selectedWorkflow, setSelectedWorkflow] = useState<CleaningWorkflow>(DEFAULT_WORKFLOWS[0])
  const [customWorkflow, setCustomWorkflow] = useState<CleaningWorkflow | null>(null)
  const [session, setSession] = useState<CleaningSession | null>(null)
  const [analytics, setAnalytics] = useState<CleaningAnalytics | null>(null)
  const [loading, setLoading] = useState(false)
  const [toast, setToast] = useState<{
    message: string
    type: 'success' | 'error' | 'info'
  } | null>(null)

  // Preview state
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set())
  const [selectedParsedMessages, setSelectedParsedMessages] = useState<Set<string>>(new Set())
  const [previewMode, setPreviewMode] = useState<'grid' | 'list'>('grid')
  const [showIndividualParsedMessages, setShowIndividualParsedMessages] = useState(false)

  // Settings state
  const [showSettings, setShowSettings] = useState(false)
  const [settings, setSettings] = useState({
    autoBackup: true,
    confirmDestructive: true,
    showPreview: true,
    batchSize: 100,
    delayBetweenBatches: 500,
    preserveStarred: true,
    preserveImportant: true,
    notifyOnComplete: true,
  })

  // Schedule state
  const [showSchedule, setShowSchedule] = useState(false)
  const [schedule, setSchedule] = useState({
    enabled: false,
    frequency: 'weekly',
    dayOfWeek: 0,
    timeOfDay: '03:00',
    workflow: 'quick-clean',
  })

  // Load initial analytics
  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/cleaning/analytics')
      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      setToast({ message: 'Failed to load inbox analytics', type: 'error' })
    } finally {
      setLoading(false)
    }
  }

  // Start cleaning session
  const startCleaning = async (workflow: CleaningWorkflow) => {
    setLoading(true)
    try {
      const response = await fetch('/api/cleaning/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          workflow,
          settings,
        }),
      })

      const session = await response.json()
      setSession(session)
      setViewMode('analysis')

      // Start analysis
      analyzeInbox(session.id)
    } catch (error) {
      setToast({ message: 'Failed to start cleaning session', type: 'error' })
    } finally {
      setLoading(false)
    }
  }

  // Analyze inbox
  const analyzeInbox = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/cleaning/${sessionId}/analyze`, {
        method: 'POST',
      })

      const updatedSession = await response.json()
      setSession(updatedSession)
      setViewMode('preview')

      // Auto-select high-confidence categories
      const autoSelected = new Set<string>(
        updatedSession.categories
          .filter((cat: CleaningCategory) => cat.confidence >= 90)
          .map((cat: CleaningCategory) => cat.id)
      )
      setSelectedCategories(autoSelected)
    } catch (error) {
      setToast({ message: 'Failed to analyze inbox', type: 'error' })
    }
  }

  // Execute cleaning
  const executeCleaning = async () => {
    if (!session || selectedCategories.size === 0) return

    setViewMode('cleaning')

    try {
      const response = await fetch(`/api/cleaning/${session.id}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          categories: Array.from(selectedCategories),
          emails: showIndividualParsedMessages ? Array.from(selectedParsedMessages) : undefined,
          createBackup: settings.autoBackup,
        }),
      })

      const updatedSession = await response.json()

      // Poll for progress
      pollProgress(session.id)
    } catch (error) {
      setToast({ message: 'Failed to execute cleaning', type: 'error' })
    }
  }

  // Poll cleaning progress
  const pollProgress = async (sessionId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/cleaning/${sessionId}/status`)
        const updatedSession = await response.json()
        setSession(updatedSession)

        if (updatedSession.status === 'completed' || updatedSession.status === 'failed') {
          clearInterval(interval)
          setViewMode('results')
          if (onComplete) onComplete(updatedSession)

          if (settings.notifyOnComplete) {
            setToast({
              message: 'Inbox cleaning completed!',
              type: 'success',
            })
          }
        }
      } catch (error) {
        console.error('Failed to poll progress:', error)
      }
    }, 1000)
  }

  // Restore from backup
  const restoreBackup = async () => {
    if (!session?.backup) return

    setLoading(true)
    try {
      await fetch(`/api/cleaning/backup/${session.backup.id}/restore`, {
        method: 'POST',
      })

      setToast({ message: 'Successfully restored from backup', type: 'success' })
      setViewMode('setup')
      setSession(null)
      loadAnalytics()
    } catch (error) {
      setToast({ message: 'Failed to restore backup', type: 'error' })
    } finally {
      setLoading(false)
    }
  }

  // Save custom workflow
  const saveCustomWorkflow = async () => {
    if (!customWorkflow) return

    try {
      const response = await fetch('/api/cleaning/workflows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customWorkflow),
      })

      const saved = await response.json()
      setToast({ message: 'Workflow saved successfully', type: 'success' })
    } catch (error) {
      setToast({ message: 'Failed to save workflow', type: 'error' })
    }
  }

  // Toggle category selection
  const toggleCategory = (categoryId: string) => {
    const newSelected = new Set(selectedCategories)
    if (newSelected.has(categoryId)) {
      newSelected.delete(categoryId)
    } else {
      newSelected.add(categoryId)
    }
    setSelectedCategories(newSelected)
  }

  // Calculate selected impact
  const selectedImpact = useMemo(() => {
    if (!session) return { emails: 0, size: 0 }

    return session.categories
      .filter((cat) => selectedCategories.has(cat.id))
      .reduce(
        (acc, cat) => ({
          emails: acc.emails + cat.count,
          size: acc.size + cat.sizeInBytes,
        }),
        { emails: 0, size: 0 }
      )
  }, [session, selectedCategories])

  // Render setup view
  const renderSetupView = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
          <Sparkles className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Clean Your Inbox</h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Let AI analyze your inbox and suggest what to clean up. Choose a workflow to get started.
        </p>
      </div>

      {/* Analytics Overview */}
      {analytics && (
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-3xl font-bold text-gray-900">
                {analytics.totalParsedMessages.toLocaleString()}
              </p>
              <p className="text-sm text-gray-600">Total ParsedMessages</p>
            </div>
            <div>
              <p className="text-3xl font-bold text-gray-900">
                {(analytics.totalSize / 1024 / 1024 / 1024).toFixed(1)} GB
              </p>
              <p className="text-sm text-gray-600">Storage Used</p>
            </div>
            <div>
              <p className="text-3xl font-bold text-orange-600">
                {analytics.potentialSavings.percentage}%
              </p>
              <p className="text-sm text-gray-600">Can Be Cleaned</p>
            </div>
            <div>
              <p className="text-3xl font-bold text-green-600">
                {(analytics.potentialSavings.storage / 1024 / 1024 / 1024).toFixed(1)} GB
              </p>
              <p className="text-sm text-gray-600">Potential Savings</p>
            </div>
          </div>
        </Card>
      )}

      {/* Workflow Selection */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {DEFAULT_WORKFLOWS.map((workflow) => (
          <Card
            key={workflow.id}
            className={`p-6 cursor-pointer transition-all ${
              selectedWorkflow.id === workflow.id
                ? 'border-blue-500 bg-blue-50'
                : 'hover:border-gray-300'
            }`}
            onClick={() => setSelectedWorkflow(workflow)}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="p-2 bg-white rounded-lg">
                {workflow.id === 'quick-clean' && <Zap className="w-6 h-6 text-yellow-500" />}
                {workflow.id === 'deep-clean' && <Layers className="w-6 h-6 text-purple-500" />}
                {workflow.id === 'smart-clean' && <Brain className="w-6 h-6 text-blue-500" />}
              </div>
              {selectedWorkflow.id === workflow.id && (
                <CheckCircle className="w-5 h-5 text-blue-500" />
              )}
            </div>

            <h4 className="font-semibold text-gray-900 mb-2">{workflow.name}</h4>
            <p className="text-sm text-gray-600 mb-4">{workflow.description}</p>

            <div className="space-y-2">
              {workflow.steps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-2 text-xs text-gray-500">
                  <div className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                    {index + 1}
                  </div>
                  <span>{step.name}</span>
                </div>
              ))}
            </div>

            {workflow.successRate && (
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Success Rate</span>
                  <Badge variant="success">{workflow.successRate}%</Badge>
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* Custom Workflow */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold text-gray-900">Custom Workflow</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              /* Open custom workflow builder */
            }}
          >
            <Settings className="w-4 h-4 mr-2" />
            Create Custom
          </Button>
        </div>
        <p className="text-sm text-gray-600">
          Build your own cleaning workflow with custom rules and criteria
        </p>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => setShowSettings(true)}>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button variant="ghost" size="sm" onClick={() => setShowSchedule(true)}>
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
        </div>

        <Button
          variant="primary"
          size="lg"
          onClick={() => startCleaning(selectedWorkflow)}
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
              Starting...
            </>
          ) : (
            <>
              <PlayCircle className="w-5 h-5 mr-2" />
              Start Cleaning
            </>
          )}
        </Button>
      </div>
    </div>
  )

  // Render analysis view
  const renderAnalysisView = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
          <Brain className="w-8 h-8 text-blue-600 animate-pulse" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Analyzing Your Inbox</h3>
        <p className="text-gray-600">
          AI is categorizing your emails and identifying cleaning opportunities...
        </p>
      </div>

      <Card className="p-6">
        <Progress value={session?.progress.percentage || 0} className="mb-4" />
        <div className="text-center">
          <p className="text-sm text-gray-600">
            {session?.progress.currentStep || 'Initializing analysis...'}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {session?.progress.current || 0} of {session?.progress.total || 0} emails analyzed
          </p>
        </div>
      </Card>

      <div className="grid grid-cols-3 gap-4">
        <Card className="p-4 text-center">
          <Database className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Scanning emails</p>
        </Card>
        <Card className="p-4 text-center">
          <Brain className="w-8 h-8 text-blue-500 mx-auto mb-2 animate-pulse" />
          <p className="text-sm text-gray-600">AI categorization</p>
        </Card>
        <Card className="p-4 text-center">
          <Target className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">Identifying patterns</p>
        </Card>
      </div>
    </div>
  )

  // Render preview view
  const renderPreviewView = () => {
    if (!session) return null

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Review Cleaning Suggestions</h3>
            <p className="text-gray-600 mt-1">
              AI has categorized your emails. Select what you want to clean.
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPreviewMode(previewMode === 'grid' ? 'list' : 'grid')}
            >
              {previewMode === 'grid' ? 'List View' : 'Grid View'}
            </Button>
            <Toggle
              checked={showIndividualParsedMessages}
              onChange={setShowIndividualParsedMessages}
              label="Individual Selection"
            />
          </div>
        </div>

        {/* Summary */}
        <Card className="p-4 bg-blue-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Info className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">
                  {selectedImpact.emails.toLocaleString()} emails selected
                </p>
                <p className="text-sm text-gray-600">
                  {(selectedImpact.size / 1024 / 1024).toFixed(1)} MB will be cleaned
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedCategories(new Set(session.categories.map((c) => c.id)))}
              >
                Select All
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setSelectedCategories(new Set())}>
                Deselect All
              </Button>
            </div>
          </div>
        </Card>

        {/* Categories */}
        <div className={previewMode === 'grid' ? 'grid grid-cols-2 gap-4' : 'space-y-2'}>
          {session.categories.map((category) => {
            const isExpanded = expandedCategories.has(category.id)
            const isSelected = selectedCategories.has(category.id)

            return (
              <Card
                key={category.id}
                className={`overflow-hidden transition-all ${isSelected ? 'border-blue-500' : ''}`}
              >
                <div className="p-4">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => toggleCategory(category.id)}
                      className="mt-1"
                    />

                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            {category.icon}
                            <h4 className="font-medium text-gray-900">{category.name}</h4>
                            <Badge
                              variant={
                                category.confidence >= 90
                                  ? 'success'
                                  : category.confidence >= 70
                                    ? 'warning'
                                    : 'default'
                              }
                              size="sm"
                            >
                              {category.confidence}% confident
                            </Badge>
                          </div>

                          <p className="text-sm text-gray-600 mt-1">{category.description}</p>

                          <div className="flex items-center gap-4 mt-2 text-sm">
                            <span className="text-gray-500">
                              {category.count.toLocaleString()} emails
                            </span>
                            <span className="text-gray-500">
                              {(category.sizeInBytes / 1024 / 1024).toFixed(1)} MB
                            </span>
                            <Badge
                              variant={
                                category.suggestedAction === 'delete'
                                  ? 'destructive'
                                  : category.suggestedAction === 'archive'
                                    ? 'warning'
                                    : 'default'
                              }
                              size="sm"
                            >
                              {category.suggestedAction}
                            </Badge>
                          </div>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const newExpanded = new Set(expandedCategories)
                            if (isExpanded) {
                              newExpanded.delete(category.id)
                            } else {
                              newExpanded.add(category.id)
                            }
                            setExpandedCategories(newExpanded)
                          }}
                        >
                          {isExpanded ? <ChevronUp /> : <ChevronDown />}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {isExpanded && (
                  <div className="px-4 pb-4 border-t bg-gray-50">
                    <div className="mt-4 space-y-3">
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Example emails:</h5>
                        <div className="space-y-1">
                          {category.examples.slice(0, 3).map((example, index) => (
                            <p key={index} className="text-xs text-gray-600 truncate">
                              {example}
                            </p>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">
                          Cleaning criteria:
                        </h5>
                        <div className="space-y-1 text-xs text-gray-600">
                          {category.criteria.ageInDays && (
                            <p>" Older than {category.criteria.ageInDays} days</p>
                          )}
                          {category.criteria.senders && (
                            <p>" From {category.criteria.senders.length} specific senders</p>
                          )}
                          {category.criteria.hasAttachments !== undefined && (
                            <p>" {category.criteria.hasAttachments ? 'Has' : 'No'} attachments</p>
                          )}
                        </div>
                      </div>

                      {showIndividualParsedMessages && (
                        <Button
                          variant="secondary"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            /* Show individual emails */
                          }}
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          View ParsedMessages
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </Card>
            )
          })}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button variant="secondary" onClick={() => setViewMode('setup')}>
            Back to Setup
          </Button>

          <div className="flex items-center gap-2">
            {settings.autoBackup && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Shield className="w-4 h-4" />
                <span>Backup will be created</span>
              </div>
            )}

            <Button
              variant="primary"
              onClick={executeCleaning}
              disabled={selectedCategories.size === 0}
            >
              <Wand2 className="w-4 h-4 mr-2" />
              Clean {selectedImpact.emails.toLocaleString()} ParsedMessages
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Render cleaning view
  const renderCleaningView = () => {
    if (!session) return null

    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <Wand2 className="w-8 h-8 text-green-600 animate-pulse" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Cleaning In Progress</h3>
          <p className="text-gray-600">
            Your inbox is being cleaned. This may take a few minutes...
          </p>
        </div>

        <Card className="p-6">
          <Progress value={session.progress.percentage} className="mb-4" />

          <div className="grid grid-cols-4 gap-4 text-center mb-4">
            <div>
              <p className="text-2xl font-bold text-gray-900">{session.progress.current}</p>
              <p className="text-sm text-gray-600">Processed</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">{session.results?.archived || 0}</p>
              <p className="text-sm text-gray-600">Archived</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">{session.results?.deleted || 0}</p>
              <p className="text-sm text-gray-600">Deleted</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">{session.results?.kept || 0}</p>
              <p className="text-sm text-gray-600">Kept</p>
            </div>
          </div>

          {session.progress.currentStep && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <RefreshCw className="w-4 h-4 inline mr-1 animate-spin" />
                {session.progress.currentStep}
              </p>
            </div>
          )}
        </Card>

        {/* Live updates */}
        <Card className="p-4">
          <h4 className="font-medium text-gray-900 mb-3">Recent Actions</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {/* Mock recent actions */}
            <div className="flex items-center gap-2 text-sm">
              <Archive className="w-4 h-4 text-yellow-500" />
              <span className="text-gray-600">Archived 15 marketing emails</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Trash2 className="w-4 h-4 text-red-500" />
              <span className="text-gray-600">Deleted 8 old notifications</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <FolderOpen className="w-4 h-4 text-blue-500" />
              <span className="text-gray-600">Organized 23 receipt emails</span>
            </div>
          </div>
        </Card>

        <div className="flex justify-center">
          <Button
            variant="secondary"
            onClick={() => {
              /* Cancel operation */
            }}
          >
            <PauseCircle className="w-4 h-4 mr-2" />
            Pause Cleaning
          </Button>
        </div>
      </div>
    )
  }

  // Render results view
  const renderResultsView = () => {
    if (!session?.results) return null

    const results = session.results

    return (
      <div className="space-y-6">
        {/* Success Header */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Inbox Cleaned Successfully!</h3>
          <p className="text-gray-600">
            Your inbox is now{' '}
            {Math.round((results.storageFreed / (session as any).analytics.totalSize) * 100)}%
            cleaner
          </p>
        </div>

        {/* Results Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="p-4 text-center">
            <Archive className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">{results.archived}</p>
            <p className="text-sm text-gray-600">Archived</p>
          </Card>
          <Card className="p-4 text-center">
            <Trash2 className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">{results.deleted}</p>
            <p className="text-sm text-gray-600">Deleted</p>
          </Card>
          <Card className="p-4 text-center">
            <Database className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">
              {(results.storageFreed / 1024 / 1024 / 1024).toFixed(1)} GB
            </p>
            <p className="text-sm text-gray-600">Freed</p>
          </Card>
          <Card className="p-4 text-center">
            <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-900">
              {Math.round(results.timeElapsed / 60)}m
            </p>
            <p className="text-sm text-gray-600">Time Taken</p>
          </Card>
        </div>

        {/* Recommendations */}
        {results.recommendations.length > 0 && (
          <Card className="p-6">
            <h4 className="font-semibold text-gray-900 mb-4">
              Recommendations for Ongoing Maintenance
            </h4>
            <div className="space-y-3">
              {results.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                    <span className="text-xs text-blue-600">{index + 1}</span>
                  </div>
                  <p className="text-sm text-gray-700">{recommendation}</p>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Before/After Comparison */}
        <Card className="p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Before & After</h4>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Storage Usage</span>
                <span className="text-sm font-medium">
                  {((session as any).analytics.totalSize / 1024 / 1024 / 1024).toFixed(1)} GB �{' '}
                  {(
                    ((session as any).analytics.totalSize - results.storageFreed) /
                    1024 /
                    1024 /
                    1024
                  ).toFixed(1)}{' '}
                  GB
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all"
                  style={{
                    width: `${(((session as any).analytics.totalSize - results.storageFreed) / (session as any).analytics.totalSize) * 100}%`,
                  }}
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">ParsedMessage Count</span>
                <span className="text-sm font-medium">
                  {(session as any).analytics.totalParsedMessages.toLocaleString()} �{' '}
                  {(
                    (session as any).analytics.totalParsedMessages -
                    results.archived -
                    results.deleted
                  ).toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all"
                  style={{
                    width: `${(((session as any).analytics.totalParsedMessages - results.archived - results.deleted) / (session as any).analytics.totalParsedMessages) * 100}%`,
                  }}
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {session.backup && (
              <Button variant="ghost" size="sm" onClick={restoreBackup}>
                <Undo2 className="w-4 h-4 mr-2" />
                Restore Backup
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                /* Export report */
              }}
            >
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="secondary"
              onClick={() => {
                setViewMode('setup')
                setSession(null)
                loadAnalytics()
              }}
            >
              <History className="w-4 h-4 mr-2" />
              Clean Again
            </Button>
            <Button variant="primary" onClick={() => setShowSchedule(true)}>
              <Calendar className="w-4 h-4 mr-2" />
              Schedule Regular Cleaning
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Settings Dialog
  const renderSettingsDialog = () => (
    <Dialog
      open={showSettings}
      onClose={() => setShowSettings(false)}
      title="Cleaning Settings"
      className="max-w-md"
    >
      <div className="space-y-4">
        <div>
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Auto Backup</span>
            <Toggle
              checked={settings.autoBackup}
              onChange={(checked) => setSettings({ ...settings, autoBackup: checked })}
            />
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Automatically create a backup before cleaning
          </p>
        </div>

        <div>
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Confirm Destructive Actions</span>
            <Toggle
              checked={settings.confirmDestructive}
              onChange={(checked) => setSettings({ ...settings, confirmDestructive: checked })}
            />
          </label>
          <p className="text-xs text-gray-500 mt-1">Ask for confirmation before deleting emails</p>
        </div>

        <div>
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Show Preview</span>
            <Toggle
              checked={settings.showPreview}
              onChange={(checked) => setSettings({ ...settings, showPreview: checked })}
            />
          </label>
          <p className="text-xs text-gray-500 mt-1">Preview emails before cleaning</p>
        </div>

        <div>
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Preserve Starred</span>
            <Toggle
              checked={settings.preserveStarred}
              onChange={(checked) => setSettings({ ...settings, preserveStarred: checked })}
            />
          </label>
          <p className="text-xs text-gray-500 mt-1">Never clean starred emails</p>
        </div>

        <div>
          <label className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Preserve Important</span>
            <Toggle
              checked={settings.preserveImportant}
              onChange={(checked) => setSettings({ ...settings, preserveImportant: checked })}
            />
          </label>
          <p className="text-xs text-gray-500 mt-1">Never clean emails marked as important</p>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">Batch Size</label>
          <Slider
            value={settings.batchSize}
            onChange={(value) => setSettings({ ...settings, batchSize: value })}
            min={10}
            max={500}
            step={10}
            className="mt-2"
          />
          <p className="text-xs text-gray-500 mt-1">
            Process {settings.batchSize} emails at a time
          </p>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="secondary" onClick={() => setShowSettings(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={() => setShowSettings(false)}>
            Save Settings
          </Button>
        </div>
      </div>
    </Dialog>
  )

  // Schedule Dialog
  const renderScheduleDialog = () => (
    <Dialog
      open={showSchedule}
      onClose={() => setShowSchedule(false)}
      title="Schedule Regular Cleaning"
      className="max-w-md"
    >
      <div className="space-y-4">
        <div>
          <label className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-700">Enable Scheduled Cleaning</span>
            <Toggle
              checked={schedule.enabled}
              onChange={(checked) => setSchedule({ ...schedule, enabled: checked })}
            />
          </label>
        </div>

        {schedule.enabled && (
          <>
            <div>
              <label className="text-sm font-medium text-gray-700">Frequency</label>
              <Select
                value={schedule.frequency}
                onValueChange={(value) => setSchedule({ ...schedule, frequency: value })}
                className="mt-1"
              >
                <Select.Item value="daily">Daily</Select.Item>
                <Select.Item value="weekly">Weekly</Select.Item>
                <Select.Item value="biweekly">Bi-weekly</Select.Item>
                <Select.Item value="monthly">Monthly</Select.Item>
              </Select>
            </div>

            {schedule.frequency === 'weekly' && (
              <div>
                <label className="text-sm font-medium text-gray-700">Day of Week</label>
                <Select
                  value={schedule.dayOfWeek.toString()}
                  onValueChange={(value) =>
                    setSchedule({ ...schedule, dayOfWeek: parseInt(value) })
                  }
                  className="mt-1"
                >
                  <Select.Item value="0">Sunday</Select.Item>
                  <Select.Item value="1">Monday</Select.Item>
                  <Select.Item value="2">Tuesday</Select.Item>
                  <Select.Item value="3">Wednesday</Select.Item>
                  <Select.Item value="4">Thursday</Select.Item>
                  <Select.Item value="5">Friday</Select.Item>
                  <Select.Item value="6">Saturday</Select.Item>
                </Select>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-700">Time of Day</label>
              <Input
                type="time"
                value={schedule.timeOfDay}
                onChange={(e) => setSchedule({ ...schedule, timeOfDay: e.target.value })}
                className="mt-1"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">Workflow to Use</label>
              <Select
                value={schedule.workflow}
                onValueChange={(value) => setSchedule({ ...schedule, workflow: value })}
                className="mt-1"
              >
                {DEFAULT_WORKFLOWS.map((w) => (
                  <Select.Item key={w.id} value={w.id}>
                    {w.name}
                  </Select.Item>
                ))}
              </Select>
            </div>
          </>
        )}

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="secondary" onClick={() => setShowSchedule(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              // Save schedule
              setShowSchedule(false)
              setToast({ message: 'Cleaning schedule saved', type: 'success' })
            }}
          >
            Save Schedule
          </Button>
        </div>
      </div>
    </Dialog>
  )

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Inbox className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Clean Inbox</h2>
            <p className="text-gray-600">AI-powered inbox cleaning and organization</p>
          </div>
        </div>

        {viewMode !== 'setup' && (
          <div className="flex items-center gap-2">
            <Badge variant="info">
              {viewMode === 'analysis' && 'Analyzing'}
              {viewMode === 'preview' && 'Preview'}
              {viewMode === 'cleaning' && 'Cleaning'}
              {viewMode === 'results' && 'Completed'}
            </Badge>
          </div>
        )}
      </div>

      {/* Progress Steps */}
      {viewMode !== 'setup' && (
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-8">
            <div
              className={`flex items-center gap-2 ${viewMode === 'analysis' ? 'text-blue-600' : 'text-gray-400'}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  viewMode === 'analysis' ? 'bg-blue-600 text-white' : 'bg-gray-200'
                }`}
              >
                1
              </div>
              <span className="text-sm font-medium">Analyze</span>
            </div>

            <div className="flex-1 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-blue-600 rounded-full transition-all"
                style={{ width: viewMode === 'analysis' ? '0%' : '33%' }}
              />
            </div>

            <div
              className={`flex items-center gap-2 ${viewMode === 'preview' ? 'text-blue-600' : 'text-gray-400'}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  viewMode === 'preview' ? 'bg-blue-600 text-white' : 'bg-gray-200'
                }`}
              >
                2
              </div>
              <span className="text-sm font-medium">Review</span>
            </div>

            <div className="flex-1 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-blue-600 rounded-full transition-all"
                style={{
                  width: viewMode === 'preview' ? '0%' : viewMode === 'cleaning' ? '50%' : '66%',
                }}
              />
            </div>

            <div
              className={`flex items-center gap-2 ${viewMode === 'cleaning' ? 'text-blue-600' : 'text-gray-400'}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  viewMode === 'cleaning' ? 'bg-blue-600 text-white' : 'bg-gray-200'
                }`}
              >
                3
              </div>
              <span className="text-sm font-medium">Clean</span>
            </div>

            <div className="flex-1 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-blue-600 rounded-full transition-all"
                style={{ width: viewMode === 'results' ? '100%' : '0%' }}
              />
            </div>

            <div
              className={`flex items-center gap-2 ${viewMode === 'results' ? 'text-green-600' : 'text-gray-400'}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  viewMode === 'results' ? 'bg-green-600 text-white' : 'bg-gray-200'
                }`}
              >
                <CheckCircle className="w-5 h-5" />
              </div>
              <span className="text-sm font-medium">Done</span>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {viewMode === 'setup' && renderSetupView()}
      {viewMode === 'analysis' && renderAnalysisView()}
      {viewMode === 'preview' && renderPreviewView()}
      {viewMode === 'cleaning' && renderCleaningView()}
      {viewMode === 'results' && renderResultsView()}

      {/* Dialogs */}
      {renderSettingsDialog()}
      {renderScheduleDialog()}

      {/* Toast */}
      {toast && (
        <Toast
          description={toast.message}
          variant={
            toast.type === 'error' ? 'destructive' : toast.type === 'success' ? 'success' : 'info'
          }
          onClose={() => setToast(null)}
        />
      )}
    </div>
  )
}
