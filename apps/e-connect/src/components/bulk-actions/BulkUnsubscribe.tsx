import {
  AlertCircle,
  ArrowLeft,
  BarChart,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  Download,
  FileText,
  Filter,
  Info,
  Link,
  Mail,
  Pause,
  Play,
  RefreshCw,
  RotateCcw,
  Search,
  Send,
  Shield,
  Trash2,
  TrendingDown,
  TrendingUp,
  Upload,
  User,
  XCircle,
  Zap,
} from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import type {
  BulkOperation,
  BulkUnsubscribeOperation,
  NewsletterSender,
  UnsubscribeDetection,
  WhitelistEntry,
} from '../../types/bulk'
import { Badge } from '../ui/Badge'
import { Button } from '../ui/Button'
import { Card } from '../ui/Card'
import { Checkbox } from '../ui/Checkbox'
import { Dialog } from '../ui/Dialog'
import { Input } from '../ui/Input'
import { Loading } from '../ui/Loading'
import { Progress } from '../ui/Progress'
import { Select } from '../ui/Select'
import { Tabs } from '../ui/Tabs'
import { Toast } from '../ui/Toast'
import { ProgressTracker } from './ProgressTracker'

interface BulkUnsubscribeProps {
  className?: string
  onComplete?: (operation: BulkUnsubscribeOperation) => void
}

type ViewMode = 'senders' | 'whitelist' | 'progress' | 'analytics'
type SortField = 'volume' | 'frequency' | 'lastReceived' | 'reputation' | 'name'
type FilterCategory =
  | 'all'
  | 'newsletter'
  | 'marketing'
  | 'promotion'
  | 'notification'
  | 'transactional'

export function BulkUnsubscribe({ className, onComplete }: BulkUnsubscribeProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('senders')
  const [senders, setSenders] = useState<NewsletterSender[]>([])
  const [whitelist, setWhitelist] = useState<WhitelistEntry[]>([])
  const [selectedSenders, setSelectedSenders] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortField, setSortField] = useState<SortField>('volume')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filterCategory, setFilterCategory] = useState<FilterCategory>('all')
  const [showPreview, setShowPreview] = useState(false)
  const [currentOperation, setCurrentOperation] = useState<BulkUnsubscribeOperation | null>(null)
  const [expandedSenders, setExpandedSenders] = useState<Set<string>>(new Set())
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null)
  const [showWhitelistDialog, setShowWhitelistDialog] = useState(false)
  const [newWhitelistEntry, setNewWhitelistEntry] = useState<Partial<WhitelistEntry>>({
    type: 'email',
    value: '',
    reason: '',
  })
  const [showScheduleDialog, setShowScheduleDialog] = useState(false)
  const [scheduleOptions, setScheduleOptions] = useState({
    scheduledFor: new Date(),
    recurring: false,
    frequency: 'weekly',
  })

  // Load initial data
  useEffect(() => {
    loadSenders()
    loadWhitelist()
  }, [])

  const loadSenders = async () => {
    setLoading(true)
    try {
      const response = await fetch(
        `/api/bulk/newsletter-senders?category=${filterCategory}&sort=${sortField}&order=${sortOrder}&search=${searchQuery}`
      )
      const data = await response.json()
      setSenders(data.senders)
    } catch (error) {
      setToast({ message: 'Failed to load newsletter senders', type: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const loadWhitelist = async () => {
    try {
      const response = await fetch('/api/bulk/whitelist')
      const data = await response.json()
      setWhitelist(data.entries)
    } catch (error) {
      console.error('Failed to load whitelist:', error)
    }
  }

  // Filter and sort senders
  const filteredSenders = useMemo(() => {
    let filtered = senders

    // Apply whitelist filter
    filtered = filtered.filter((sender) => !sender.isWhitelisted)

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (sender) =>
          sender.email.toLowerCase().includes(query) ||
          sender.name.toLowerCase().includes(query) ||
          sender.domain.toLowerCase().includes(query)
      )
    }

    // Apply category filter
    if (filterCategory !== 'all') {
      filtered = filtered.filter((sender) => sender.category === filterCategory)
    }

    return filtered
  }, [senders, searchQuery, filterCategory])

  // Selection helpers
  const toggleSender = (senderId: string) => {
    const newSelected = new Set(selectedSenders)
    if (newSelected.has(senderId)) {
      newSelected.delete(senderId)
    } else {
      newSelected.add(senderId)
    }
    setSelectedSenders(newSelected)
  }

  const selectAll = () => {
    const allIds = filteredSenders.map((s) => s.id)
    setSelectedSenders(new Set(allIds))
  }

  const deselectAll = () => {
    setSelectedSenders(new Set())
  }

  // Unsubscribe operations
  const startUnsubscribe = async (preview = false) => {
    if (selectedSenders.size === 0) {
      setToast({ message: 'Please select senders to unsubscribe from', type: 'error' })
      return
    }

    try {
      const response = await fetch('/api/bulk/unsubscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          senderIds: Array.from(selectedSenders),
          preview,
          schedule: showScheduleDialog ? scheduleOptions : undefined,
        }),
      })

      const data = await response.json()

      if (preview) {
        setShowPreview(true)
        setCurrentOperation(data.operation)
      } else {
        setCurrentOperation(data.operation)
        setViewMode('progress')
        pollOperationStatus(data.operationId)
      }
    } catch (error) {
      setToast({ message: 'Failed to start unsubscribe operation', type: 'error' })
    }
  }

  const pollOperationStatus = async (operationId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/bulk/unsubscribe/${operationId}`)
        const operation = await response.json()
        setCurrentOperation(operation)

        if (
          operation.status === 'completed' ||
          operation.status === 'failed' ||
          operation.status === 'cancelled'
        ) {
          clearInterval(interval)
          if (onComplete) onComplete(operation)
        }
      } catch (error) {
        console.error('Failed to poll operation status:', error)
      }
    }, 1000)
  }

  const cancelOperation = async () => {
    if (!currentOperation) return

    try {
      await fetch(`/api/bulk/unsubscribe/${currentOperation.id}/cancel`, {
        method: 'POST',
      })
      setToast({ message: 'Operation cancelled', type: 'success' })
    } catch (error) {
      setToast({ message: 'Failed to cancel operation', type: 'error' })
    }
  }

  const restoreFromBackup = async () => {
    if (!currentOperation?.backup) return

    try {
      await fetch(`/api/bulk/unsubscribe/${currentOperation.id}/restore`, {
        method: 'POST',
      })
      setToast({ message: 'Successfully restored from backup', type: 'success' })
      loadSenders()
    } catch (error) {
      setToast({ message: 'Failed to restore from backup', type: 'error' })
    }
  }

  // Whitelist operations
  const addToWhitelist = async () => {
    if (!newWhitelistEntry.value) {
      setToast({ message: 'Please enter a value to whitelist', type: 'error' })
      return
    }

    try {
      const response = await fetch('/api/bulk/whitelist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newWhitelistEntry),
      })

      if (response.ok) {
        setToast({ message: 'Added to whitelist', type: 'success' })
        loadWhitelist()
        setShowWhitelistDialog(false)
        setNewWhitelistEntry({ type: 'email', value: '', reason: '' })
      }
    } catch (error) {
      setToast({ message: 'Failed to add to whitelist', type: 'error' })
    }
  }

  const removeFromWhitelist = async (entryId: string) => {
    try {
      await fetch(`/api/bulk/whitelist/${entryId}`, {
        method: 'DELETE',
      })
      setToast({ message: 'Removed from whitelist', type: 'success' })
      loadWhitelist()
    } catch (error) {
      setToast({ message: 'Failed to remove from whitelist', type: 'error' })
    }
  }

  const exportWhitelist = async (format: 'json' | 'csv') => {
    try {
      const response = await fetch(`/api/bulk/whitelist/export?format=${format}`)
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `whitelist.${format}`
      a.click()
      URL.revokeObjectURL(url)
    } catch (error) {
      setToast({ message: 'Failed to export whitelist', type: 'error' })
    }
  }

  // Render sender row
  const renderSenderRow = (sender: NewsletterSender) => {
    const isExpanded = expandedSenders.has(sender.id)
    const isSelected = selectedSenders.has(sender.id)

    return (
      <div key={sender.id} className="border rounded-lg overflow-hidden">
        <div className="p-4 hover:bg-gray-50 transition-colors">
          <div className="flex items-start gap-4">
            <Checkbox
              checked={isSelected}
              onChange={() => toggleSender(sender.id)}
              className="mt-1"
            />

            <div className="flex-1">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-gray-900">{sender.name}</h4>
                    <Badge
                      variant={
                        sender.reputation.score >= 80
                          ? 'success'
                          : sender.reputation.score >= 60
                            ? 'warning'
                            : 'destructive'
                      }
                    >
                      {sender.reputation.score}% reputation
                    </Badge>
                    {sender.isWhitelisted && (
                      <Badge variant="info">
                        <Shield className="w-3 h-3 mr-1" />
                        Whitelisted
                      </Badge>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mt-1">{sender.email}</p>

                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <Mail className="w-4 h-4" />
                      {sender.volume.total} emails
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {sender.frequency}
                    </span>
                    <span className="flex items-center gap-1">
                      {sender.volume.trend === 'increasing' ? (
                        <TrendingUp className="w-4 h-4 text-red-500" />
                      ) : sender.volume.trend === 'decreasing' ? (
                        <TrendingDown className="w-4 h-4 text-green-500" />
                      ) : (
                        <BarChart className="w-4 h-4 text-gray-400" />
                      )}
                      {sender.volume.trend}
                    </span>
                  </div>

                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="default" className="text-xs">
                      {sender.category}
                    </Badge>
                    {sender.unsubscribeMethod && (
                      <Badge variant="success" className="text-xs">
                        {sender.unsubscribeMethod === 'link' && <Link className="w-3 h-3 mr-1" />}
                        {sender.unsubscribeMethod === 'email' && <Send className="w-3 h-3 mr-1" />}
                        {sender.unsubscribeMethod === 'manual' && <User className="w-3 h-3 mr-1" />}
                        {sender.unsubscribeMethod} unsubscribe
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newExpanded = new Set(expandedSenders)
                      if (isExpanded) {
                        newExpanded.delete(sender.id)
                      } else {
                        newExpanded.add(sender.id)
                      }
                      setExpandedSenders(newExpanded)
                    }}
                  >
                    {isExpanded ? <ChevronUp /> : <ChevronDown />}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {isExpanded && (
          <div className="px-4 pb-4 border-t bg-gray-50">
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Statistics</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Open rate:</span>
                    <span className="font-medium">
                      {(sender.statistics.openRate * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Click rate:</span>
                    <span className="font-medium">
                      {(sender.statistics.clickRate * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Unsubscribe rate:</span>
                    <span className="font-medium">
                      {(sender.statistics.unsubscribeRate * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Recent Emails</h5>
                <div className="space-y-1">
                  {sender.recentEmails.slice(0, 3).map((email) => (
                    <div key={email.id} className="text-sm">
                      <p className="text-gray-600 truncate">{email.subject}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(email.date).toLocaleDateString()}
                        {email.hasUnsubscribeLink && (
                          <span className="ml-2 text-green-600"> Has unsubscribe</span>
                        )}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {sender.unsubscribeUrl && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <Link className="w-4 h-4 inline mr-1" />
                  Unsubscribe URL detected: {sender.unsubscribeUrl}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  // Render views
  const renderSendersView = () => (
    <div className="space-y-4">
      {/* Filters and search */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search senders..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select
          value={filterCategory}
          onValueChange={(value) => setFilterCategory(value as FilterCategory)}
        >
          <Select.Item value="all">All Categories</Select.Item>
          <Select.Item value="newsletter">Newsletters</Select.Item>
          <Select.Item value="marketing">Marketing</Select.Item>
          <Select.Item value="promotion">Promotions</Select.Item>
          <Select.Item value="notification">Notifications</Select.Item>
          <Select.Item value="transactional">Transactional</Select.Item>
        </Select>

        <Select value={sortField} onValueChange={(value) => setSortField(value as SortField)}>
          <Select.Item value="volume">Volume</Select.Item>
          <Select.Item value="frequency">Frequency</Select.Item>
          <Select.Item value="lastReceived">Last Received</Select.Item>
          <Select.Item value="reputation">Reputation</Select.Item>
          <Select.Item value="name">Name</Select.Item>
        </Select>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
        >
          {sortOrder === 'asc' ? '�' : '�'}
        </Button>
      </div>

      {/* Selection controls */}
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            {selectedSenders.size} of {filteredSenders.length} selected
          </span>
          <Button variant="ghost" size="sm" onClick={selectAll}>
            Select All
          </Button>
          <Button variant="ghost" size="sm" onClick={deselectAll}>
            Deselect All
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowScheduleDialog(true)}
            disabled={selectedSenders.size === 0}
          >
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
          <Button
            variant="primary"
            onClick={() => startUnsubscribe(true)}
            disabled={selectedSenders.size === 0}
          >
            Preview Unsubscribe ({selectedSenders.size})
          </Button>
        </div>
      </div>

      {/* Senders list */}
      {loading ? (
        <Loading />
      ) : filteredSenders.length === 0 ? (
        <Card className="p-8 text-center">
          <p className="text-gray-500">No newsletter senders found</p>
        </Card>
      ) : (
        <div className="space-y-2">{filteredSenders.map((sender) => renderSenderRow(sender))}</div>
      )}
    </div>
  )

  const renderWhitelistView = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">{whitelist.length} entries in whitelist</p>
        <div className="flex items-center gap-2">
          <Button variant="secondary" size="sm" onClick={() => exportWhitelist('csv')}>
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="secondary" size="sm" onClick={() => exportWhitelist('json')}>
            <Download className="w-4 h-4 mr-2" />
            Export JSON
          </Button>
          <Button variant="primary" size="sm" onClick={() => setShowWhitelistDialog(true)}>
            <Shield className="w-4 h-4 mr-2" />
            Add Entry
          </Button>
        </div>
      </div>

      <div className="space-y-2">
        {whitelist.map((entry) => (
          <Card key={entry.id} className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  {entry.type === 'email' && <Mail className="w-4 h-4 text-blue-600" />}
                  {entry.type === 'domain' && <Shield className="w-4 h-4 text-blue-600" />}
                  {entry.type === 'pattern' && <Filter className="w-4 h-4 text-blue-600" />}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{entry.value}</p>
                  {entry.reason && <p className="text-sm text-gray-600">{entry.reason}</p>}
                  <p className="text-xs text-gray-500 mt-1">
                    Added {new Date(entry.addedAt).toLocaleDateString()}
                    {entry.isVIP && (
                      <Badge variant="info" className="ml-2">
                        VIP
                      </Badge>
                    )}
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={() => removeFromWhitelist(entry.id)}>
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )

  const renderProgressView = () => {
    if (!currentOperation) return null

    // Convert BulkUnsubscribeOperation to BulkOperation format for ProgressTracker
    const bulkOperation: BulkOperation = {
      id: currentOperation.id,
      type: 'unsubscribe',
      status: currentOperation.status as any,
      createdAt: currentOperation.createdAt,
      startedAt: currentOperation.startedAt,
      completedAt: currentOperation.completedAt,
      createdBy: currentOperation.createdBy,
      selection: {
        type: 'manual',
        totalCount: currentOperation.progress.total,
        threadIds: currentOperation.selection.senderIds,
      },
      progress: {
        processed: currentOperation.progress.current,
        succeeded: currentOperation.progress.successful,
        failed: currentOperation.progress.failed,
        skipped: currentOperation.progress.manual,
        percentage: currentOperation.progress.percentage,
        estimatedTimeRemaining: currentOperation.progress.estimatedTime,
        currentItem: currentOperation.progress.currentSender,
      },
      errors: currentOperation.results.failed.map((f) => ({
        itemId: f.senderId,
        error: f.error,
        timestamp: f.timestamp,
        retryable: f.retryable,
      })),
      retryCount: 0,
      maxRetries: 3,
      results: [
        ...currentOperation.results.successful.map((s) => ({
          itemId: s.senderId,
          itemType: 'message' as const,
          success: true,
          processingTime: 0,
          retries: 0,
        })),
        ...currentOperation.results.failed.map((f) => ({
          itemId: f.senderId,
          itemType: 'message' as const,
          success: false,
          error: f.error,
          processingTime: 0,
          retries: 0,
        })),
      ],
      summary:
        currentOperation.status === 'completed'
          ? {
              totalItems: currentOperation.progress.total,
              processedItems: currentOperation.progress.current,
              successfulItems: currentOperation.progress.successful,
              failedItems: currentOperation.progress.failed,
              skippedItems: currentOperation.progress.manual,
              totalTime: 0,
              avgTimePerItem: 0,
              changes: {
                unsubscribed: currentOperation.progress.successful,
              },
              sizeReclaimed: currentOperation.analytics.storageReclaimed,
              costSaved: currentOperation.analytics.costSaved,
            }
          : undefined,
    }

    return (
      <ProgressTracker
        operation={bulkOperation}
        onCancel={cancelOperation}
        onRetry={(itemId) => {
          // Handle retry logic here
          console.log('Retry item:', itemId)
        }}
        onExport={(format) => {
          // Handle export logic here
          console.log('Export format:', format)
        }}
        showNotifications={true}
        persistState={true}
      />
    )
  }

  const renderAnalyticsView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Unsubscribe Analytics</h3>
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-3xl font-bold text-gray-900">156</p>
            <p className="text-sm text-gray-600">Total Unsubscribed</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold text-green-600">87.5%</p>
            <p className="text-sm text-gray-600">Success Rate</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold text-blue-600">1.8s</p>
            <p className="text-sm text-gray-600">Avg Time</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold text-purple-600">$15.50</p>
            <p className="text-sm text-gray-600">Cost Saved</p>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="font-semibold mb-4">By Method</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Link className="w-4 h-4 text-gray-600" />
                <span className="text-sm">Link-based</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">109</span>
                <Badge variant="success">92%</Badge>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Send className="w-4 h-4 text-gray-600" />
                <span className="text-sm">Email-based</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">31</span>
                <Badge variant="success">84%</Badge>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-600" />
                <span className="text-sm">Manual</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">16</span>
                <Badge variant="warning">0%</Badge>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h4 className="font-semibold mb-4">Top Unsubscribed</h4>
          <div className="space-y-2">
            {[
              'Amazon Deals',
              'LinkedIn Updates',
              'Medium Digest',
              'TechCrunch',
              'Product Hunt',
            ].map((name, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-700">{name}</span>
                <Badge variant="default">{5 - index}%</Badge>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  )

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Bulk Unsubscribe</h2>
          <p className="text-gray-600 mt-1">Manage newsletter subscriptions at scale</p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'senders' ? 'primaryBlue' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('senders')}
          >
            <Mail className="w-4 h-4 mr-2" />
            Senders
          </Button>
          <Button
            variant={viewMode === 'whitelist' ? 'primaryBlue' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('whitelist')}
          >
            <Shield className="w-4 h-4 mr-2" />
            Whitelist
          </Button>
          <Button
            variant={viewMode === 'progress' ? 'primaryBlue' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('progress')}
            disabled={!currentOperation}
          >
            <Zap className="w-4 h-4 mr-2" />
            Progress
          </Button>
          <Button
            variant={viewMode === 'analytics' ? 'primaryBlue' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('analytics')}
          >
            <BarChart className="w-4 h-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Content */}
      {viewMode === 'senders' && renderSendersView()}
      {viewMode === 'whitelist' && renderWhitelistView()}
      {viewMode === 'progress' && renderProgressView()}
      {viewMode === 'analytics' && renderAnalyticsView()}

      {/* Preview Dialog */}
      <Dialog
        open={showPreview}
        onClose={() => setShowPreview(false)}
        title="Unsubscribe Preview"
        className="max-w-2xl"
      >
        {currentOperation && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-800">
                This operation will unsubscribe you from {currentOperation.selection.totalSenders}{' '}
                senders, freeing up approximately {currentOperation.selection.totalEmails} emails.
              </p>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Selected Senders:</h4>
              <div className="max-h-60 overflow-y-auto space-y-1">
                {senders
                  .filter((s) => selectedSenders.has(s.id))
                  .map((sender) => (
                    <div
                      key={sender.id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                      <span className="text-sm">{sender.name}</span>
                      <Badge variant="default" size="sm">
                        {sender.unsubscribeMethod || 'unknown'}
                      </Badge>
                    </div>
                  ))}
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="secondary" onClick={() => setShowPreview(false)}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  setShowPreview(false)
                  startUnsubscribe(false)
                }}
              >
                Start Unsubscribe
              </Button>
            </div>
          </div>
        )}
      </Dialog>

      {/* Whitelist Dialog */}
      <Dialog
        open={showWhitelistDialog}
        onClose={() => setShowWhitelistDialog(false)}
        title="Add to Whitelist"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <Select
              value={newWhitelistEntry.type}
              onValueChange={(value) =>
                setNewWhitelistEntry({ ...newWhitelistEntry, type: value as any })
              }
            >
              <Select.Item value="email">Email Address</Select.Item>
              <Select.Item value="domain">Domain</Select.Item>
              <Select.Item value="pattern">Pattern (wildcards)</Select.Item>
            </Select>
          </div>

          <Input
            value={newWhitelistEntry.value}
            onChange={(e) => setNewWhitelistEntry({ ...newWhitelistEntry, value: e.target.value })}
            placeholder={
              newWhitelistEntry.type === 'email'
                ? '<EMAIL>'
                : newWhitelistEntry.type === 'domain'
                  ? 'example.com'
                  : '*.example.com'
            }
            label="Value"
          />

          <Input
            value={newWhitelistEntry.reason}
            onChange={(e) => setNewWhitelistEntry({ ...newWhitelistEntry, reason: e.target.value })}
            placeholder="Optional reason for whitelisting"
            label="Reason"
          />

          <Checkbox
            checked={newWhitelistEntry.isVIP || false}
            onChange={(e) =>
              setNewWhitelistEntry({ ...newWhitelistEntry, isVIP: e.target.checked })
            }
            label="Mark as VIP (extra protection)"
          />

          <div className="flex justify-end gap-2">
            <Button variant="secondary" onClick={() => setShowWhitelistDialog(false)}>
              Cancel
            </Button>
            <Button variant="primary" onClick={addToWhitelist}>
              Add to Whitelist
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Schedule Dialog */}
      <Dialog
        open={showScheduleDialog}
        onClose={() => setShowScheduleDialog(false)}
        title="Schedule Unsubscribe"
      >
        <div className="space-y-4">
          <Input
            type="datetime-local"
            value={scheduleOptions.scheduledFor.toISOString().slice(0, -8)}
            onChange={(e) =>
              setScheduleOptions({
                ...scheduleOptions,
                scheduledFor: new Date(e.target.value),
              })
            }
            label="Schedule for"
          />

          <Checkbox
            checked={scheduleOptions.recurring}
            onChange={(e) =>
              setScheduleOptions({
                ...scheduleOptions,
                recurring: e.target.checked,
              })
            }
            label="Make this a recurring operation"
          />

          {scheduleOptions.recurring && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
              <Select
                value={scheduleOptions.frequency}
                onValueChange={(value) =>
                  setScheduleOptions({
                    ...scheduleOptions,
                    frequency: value,
                  })
                }
              >
                <Select.Item value="weekly">Weekly</Select.Item>
                <Select.Item value="monthly">Monthly</Select.Item>
              </Select>
            </div>
          )}

          <div className="flex justify-end gap-2">
            <Button variant="secondary" onClick={() => setShowScheduleDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                setShowScheduleDialog(false)
                startUnsubscribe(true)
              }}
            >
              Schedule
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Toast */}
      {toast && (
        <Toast
          description={toast.message}
          variant={toast.type === 'error' ? 'destructive' : 'success'}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  )
}
