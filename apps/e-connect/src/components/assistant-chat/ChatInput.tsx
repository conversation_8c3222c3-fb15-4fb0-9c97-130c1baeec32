import { Button } from '@luminar/shared-ui'
import {
  CalendarIcon,
  CommandLineIcon,
  DocumentIcon,
  FaceSmileIcon,
  LightBulbIcon,
  LinkIcon,
  MicrophoneIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
  PhotoIcon,
  UserGroupIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { AnimatePresence, motion } from 'framer-motion'
import type React from 'react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import TextareaAutosize from 'react-textarea-autosize'
import { useAssistantStore } from '@/stores/assistantStore'
import { cn } from '@/utils'

interface ChatInputProps {
  placeholder?: string
  disabled?: boolean
  maxLength?: number
  autoFocus?: boolean
  onFocus?: () => void
  onBlur?: () => void
}

interface QuickAction {
  id: string
  label: string
  icon: React.ElementType
  action: string
  description: string
  category: 'email' | 'automation' | 'analysis' | 'help'
}

const quickActions: QuickAction[] = [
  {
    id: 'create-rule',
    label: 'Create Rule',
    icon: CommandLineIcon,
    action: 'Help me create an automation rule for my emails',
    description: 'Create email automation rules',
    category: 'automation',
  },
  {
    id: 'analyze-inbox',
    label: 'Analyze Inbox',
    icon: LightBulbIcon,
    action: 'Analyze my inbox and provide insights',
    description: 'Get AI insights about your emails',
    category: 'analysis',
  },
  {
    id: 'bulk-unsubscribe',
    label: 'Bulk Unsubscribe',
    icon: UserGroupIcon,
    action: 'Help me unsubscribe from newsletters and promotional emails',
    description: 'Unsubscribe from multiple emails',
    category: 'email',
  },
  {
    id: 'schedule-email',
    label: 'Schedule Email',
    icon: CalendarIcon,
    action: 'Help me schedule an email to be sent later',
    description: 'Schedule emails for later',
    category: 'email',
  },
]

export const ChatInput: React.FC<ChatInputProps> = ({
  placeholder = 'Ask me anything about your emails...',
  disabled = false,
  maxLength = 4000,
  autoFocus = false,
  onFocus,
  onBlur,
}) => {
  const [showQuickActions, setShowQuickActions] = useState(false)
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    inputValue,
    setInputValue,
    sendMessage,
    attachments,
    addAttachment,
    removeAttachment,
    clearAttachments,
    isTyping,
    isLoading,
  } = useAssistantStore()

  const isDisabled = disabled || isTyping || isLoading
  const canSend = inputValue.trim().length > 0 && !isDisabled
  const characterCount = inputValue.length
  const isNearLimit = characterCount > maxLength * 0.8

  // Keyboard shortcuts
  useHotkeys('mod+enter', () => handleSend(), {
    enableOnFormTags: true,
    preventDefault: true,
  })

  useHotkeys('escape', () => {
    setShowQuickActions(false)
    setShowAttachmentMenu(false)
  })

  useHotkeys(
    'mod+/',
    () => {
      setShowQuickActions(!showQuickActions)
      textareaRef.current?.focus()
    },
    { preventDefault: true }
  )

  const handleSend = useCallback(async () => {
    if (!canSend) return

    const message = inputValue.trim()
    const attachmentsCopy = [...attachments]

    // Clear input immediately for better UX
    setInputValue('')
    clearAttachments()

    await sendMessage(message, attachmentsCopy)
  }, [canSend, inputValue, attachments, setInputValue, clearAttachments, sendMessage])

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        handleSend()
      }
    },
    [handleSend]
  )

  const handleQuickAction = (action: QuickAction) => {
    setInputValue(action.action)
    setShowQuickActions(false)
    textareaRef.current?.focus()
  }

  const handleFileUpload = (files: FileList | null) => {
    if (!files) return

    Array.from(files).forEach((file) => {
      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        alert('File size must be less than 10MB')
        return
      }

      const attachment = {
        type: file.type.startsWith('image/') ? 'image' : 'file',
        name: file.name,
        url: URL.createObjectURL(file),
        metadata: {
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
        },
      }

      addAttachment(attachment)
    })

    setShowAttachmentMenu(false)
  }

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    handleFileUpload(e.dataTransfer.files)
  }, [])

  const handleVoiceInput = async () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech recognition is not supported in your browser')
      return
    }

    const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'en-US'

    recognition.onstart = () => setIsRecording(true)
    recognition.onend = () => setIsRecording(false)

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      setInputValue(inputValue + (inputValue ? ' ' : '') + transcript)
    }

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error)
      setIsRecording(false)
    }

    recognition.start()
  }

  const getAttachmentIcon = (type: string) => {
    if (type === 'image') return PhotoIcon
    if (type === 'link') return LinkIcon
    return DocumentIcon
  }

  return (
    <div className="relative">
      {/* Quick Actions Panel */}
      <AnimatePresence>
        {showQuickActions && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50"
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-900">Quick Actions</h3>
              <Button variant="ghost" size="xs" onClick={() => setShowQuickActions(false)}>
                <XMarkIcon className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action) => {
                const Icon = action.icon
                return (
                  <button
                    key={action.id}
                    onClick={() => handleQuickAction(action)}
                    className="flex items-center gap-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Icon className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{action.label}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </button>
                )
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Attachments Display */}
      <AnimatePresence>
        {attachments.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-2 flex flex-wrap gap-2"
          >
            {attachments.map((attachment) => {
              const Icon = getAttachmentIcon(attachment.type)
              return (
                <motion.div
                  key={attachment.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="flex items-center gap-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2"
                >
                  <Icon className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-900 max-w-32 truncate">{attachment.name}</span>
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => removeAttachment(attachment.id)}
                    className="text-blue-600 hover:text-blue-800 h-4 w-4"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </Button>
                </motion.div>
              )
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Input Container */}
      <div
        className={cn(
          'relative bg-white border border-gray-300 rounded-lg shadow-sm transition-all duration-200',
          isDragging && 'border-blue-500 bg-blue-50',
          isDisabled && 'opacity-50 cursor-not-allowed'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Drag Overlay */}
        {isDragging && (
          <div className="absolute inset-0 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg flex items-center justify-center z-10">
            <div className="text-center">
              <PaperClipIcon className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <p className="text-sm text-blue-700">Drop files to attach</p>
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="flex items-end gap-2 p-3">
          {/* Attachment Button */}
          <div className="relative">
            <Button
              variant="ghost"
              size="xs"
              onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}
              disabled={isDisabled}
              className="text-gray-500 hover:text-gray-700"
            >
              <PaperClipIcon className="h-5 w-5" />
            </Button>

            {/* Attachment Menu */}
            <AnimatePresence>
              {showAttachmentMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50 min-w-40"
                >
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <DocumentIcon className="h-4 w-4" />
                    Upload File
                  </button>
                  <button
                    onClick={() => {
                      const input = document.createElement('input')
                      input.type = 'file'
                      input.accept = 'image/*'
                      input.onchange = (e) => {
                        const target = e.target as HTMLInputElement
                        handleFileUpload(target.files)
                      }
                      input.click()
                    }}
                    className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <PhotoIcon className="h-4 w-4" />
                    Upload Image
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Textarea */}
          <div className="flex-1 min-w-0">
            <TextareaAutosize
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={onFocus}
              onBlur={onBlur}
              placeholder={placeholder}
              disabled={isDisabled}
              maxLength={maxLength}
              autoFocus={autoFocus}
              maxRows={6}
              className="w-full resize-none border-none bg-transparent text-gray-900 placeholder-gray-500 focus:outline-none text-sm leading-6"
            />
          </div>

          {/* Voice Input Button */}
          <Button
            variant="ghost"
            size="xs"
            onClick={handleVoiceInput}
            disabled={isDisabled}
            className={cn(
              'text-gray-500 hover:text-gray-700 transition-colors',
              isRecording && 'text-red-500 animate-pulse'
            )}
          >
            <MicrophoneIcon className="h-5 w-5" />
          </Button>

          {/* Quick Actions Toggle */}
          <Button
            variant="ghost"
            size="xs"
            onClick={() => setShowQuickActions(!showQuickActions)}
            disabled={isDisabled}
            className="text-gray-500 hover:text-gray-700"
          >
            <LightBulbIcon className="h-5 w-5" />
          </Button>

          {/* Send Button */}
          <Button
            onClick={handleSend}
            disabled={!canSend}
            size="xs"
            className={cn(
              'transition-all duration-200',
              canSend
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            )}
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </Button>
        </div>

        {/* Character Count and Shortcuts */}
        <div className="flex items-center justify-between px-3 pb-2 text-xs text-gray-500">
          <div className="flex items-center gap-4">
            <span>Press +Enter to send</span>
            <span>+/ for quick actions</span>
          </div>

          {characterCount > 0 && (
            <span
              className={cn(
                'transition-colors',
                isNearLimit && 'text-orange-500',
                characterCount >= maxLength && 'text-red-500'
              )}
            >
              {characterCount}/{maxLength}
            </span>
          )}
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={(e) => handleFileUpload(e.target.files)}
      />
    </div>
  )
}

export default ChatInput
