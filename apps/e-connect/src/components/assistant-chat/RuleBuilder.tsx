import { Button } from '@luminar/shared-ui'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import {
  ArrowRightIcon,
  CheckIcon,
  CogIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  InformationCircleIcon,
  LightBulbIcon,
  PlayIcon,
  PlusIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { AnimatePresence, motion } from 'framer-motion'
import type React from 'react'
import { useCallback, useState } from 'react'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import type {
  Rule,
  RuleAction,
  RuleActionType,
  RuleCondition,
  RuleConditionType,
  RuleOperator,
} from '@/types/rules'
import { cn, generateId } from '@/utils'

interface RuleBuilderProps {
  onSave?: (rule: Partial<Rule>) => void
  onTest?: (rule: Partial<Rule>) => Promise<any>
  onCancel?: () => void
  initialRule?: Partial<Rule>
  className?: string
  inline?: boolean
}

interface RuleFormData {
  name: string
  description: string
  conditions: RuleCondition[]
  conditionLogic: 'all' | 'any'
  actions: RuleAction[]
  enabled: boolean
}

const conditionTypes: { value: RuleConditionType; label: string; description: string }[] = [
  { value: 'from', label: 'From', description: 'Sender email address' },
  { value: 'to', label: 'To', description: 'Recipient email address' },
  { value: 'subject', label: 'Subject', description: 'Email subject line' },
  { value: 'body', label: 'Body', description: 'Email content' },
  { value: 'category', label: 'Category', description: 'Email category' },
  { value: 'hasAttachment', label: 'Has Attachment', description: 'Email has attachments' },
  { value: 'isUnread', label: 'Is Unread', description: 'Email is unread' },
  { value: 'isImportant', label: 'Is Important', description: 'Email is marked important' },
  { value: 'domain', label: 'Domain', description: 'Sender domain' },
  { value: 'age', label: 'Age', description: 'Email age in days' },
  { value: 'size', label: 'Size', description: 'Email size in bytes' },
]

const operators: { value: RuleOperator; label: string; description: string }[] = [
  { value: 'equals', label: 'equals', description: 'Exact match' },
  { value: 'contains', label: 'contains', description: 'Contains text' },
  { value: 'startsWith', label: 'starts with', description: 'Begins with text' },
  { value: 'endsWith', label: 'ends with', description: 'Ends with text' },
  { value: 'matches', label: 'matches regex', description: 'Regular expression match' },
  { value: 'greaterThan', label: 'greater than', description: 'Numeric comparison' },
  { value: 'lessThan', label: 'less than', description: 'Numeric comparison' },
  { value: 'exists', label: 'exists', description: 'Field has any value' },
  { value: 'notExists', label: 'does not exist', description: 'Field is empty' },
]

const actionTypes: {
  value: RuleActionType
  label: string
  description: string
  icon: React.ElementType
}[] = [
  {
    value: 'archive',
    label: 'Archive',
    description: 'Move to archive',
    icon: DocumentDuplicateIcon,
  },
  { value: 'delete', label: 'Delete', description: 'Delete email', icon: XMarkIcon },
  { value: 'markRead', label: 'Mark as Read', description: 'Mark email as read', icon: EyeIcon },
  {
    value: 'markUnread',
    label: 'Mark as Unread',
    description: 'Mark email as unread',
    icon: EyeIcon,
  },
  { value: 'label', label: 'Add Label', description: 'Add a label', icon: PlusIcon },
  { value: 'categorize', label: 'Categorize', description: 'Set category', icon: CogIcon },
  { value: 'forward', label: 'Forward', description: 'Forward to address', icon: ArrowRightIcon },
  {
    value: 'reply',
    label: 'Auto Reply',
    description: 'Send automatic reply',
    icon: ArrowRightIcon,
  },
  {
    value: 'moveToFolder',
    label: 'Move to Folder',
    description: 'Move to specific folder',
    icon: DocumentDuplicateIcon,
  },
]

const ConditionBuilder: React.FC<{
  condition: RuleCondition
  index: number
  onUpdate: (index: number, condition: RuleCondition) => void
  onRemove: (index: number) => void
  canRemove: boolean
}> = ({ condition, index, onUpdate, onRemove, canRemove }) => {
  const [isAdvanced, setIsAdvanced] = useState(false)

  const handleFieldChange = (field: keyof RuleCondition, value: any) => {
    onUpdate(index, { ...condition, [field]: value })
  }

  const selectedType = conditionTypes.find((t) => t.value === condition.type)
  const selectedOperator = operators.find((o) => o.value === condition.operator)

  const needsValue = !['exists', 'notExists'].includes(condition.operator)
  const isNumericField = ['age', 'size'].includes(condition.type)
  const isBooleanField = ['hasAttachment', 'isUnread', 'isImportant'].includes(condition.type)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Condition {index + 1}</span>
          {condition.negate && (
            <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">NOT</span>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="xs"
            onClick={() => setIsAdvanced(!isAdvanced)}
            className="text-gray-500"
          >
            {isAdvanced ? 'Simple' : 'Advanced'}
          </Button>
          {canRemove && (
            <Button
              variant="ghost"
              size="xs"
              onClick={() => onRemove(index)}
              className="text-red-500 hover:text-red-700"
            >
              <XMarkIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* Field Type */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Field</label>
          <Select
            value={condition.type}
            onValueChange={(value) => handleFieldChange('type', value as RuleConditionType)}
          >
            {conditionTypes.map((type) => (
              <Select.Item key={type.value} value={type.value}>
                <div>
                  <div className="font-medium">{type.label}</div>
                  <div className="text-xs text-gray-500">{type.description}</div>
                </div>
              </Select.Item>
            ))}
          </Select>
        </div>

        {/* Operator */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Operator</label>
          <Select
            value={condition.operator}
            onValueChange={(value) => handleFieldChange('operator', value as RuleOperator)}
          >
            {operators
              .filter((op) => {
                if (isBooleanField) return ['equals', 'exists', 'notExists'].includes(op.value)
                if (isNumericField)
                  return ['equals', 'greaterThan', 'lessThan', 'between'].includes(op.value)
                return true
              })
              .map((operator) => (
                <Select.Item key={operator.value} value={operator.value}>
                  <div>
                    <div className="font-medium">{operator.label}</div>
                    <div className="text-xs text-gray-500">{operator.description}</div>
                  </div>
                </Select.Item>
              ))}
          </Select>
        </div>

        {/* Value */}
        {needsValue && (
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Value</label>
            {isBooleanField ? (
              <Select
                value={String(condition.value)}
                onValueChange={(value) => handleFieldChange('value', value === 'true')}
              >
                <Select.Item value="true">True</Select.Item>
                <Select.Item value="false">False</Select.Item>
              </Select>
            ) : (
              <Input
                type={isNumericField ? 'number' : 'text'}
                value={String(condition.value || '')}
                onChange={(e) =>
                  handleFieldChange(
                    'value',
                    isNumericField ? Number(e.target.value) : e.target.value
                  )
                }
                placeholder={`Enter ${selectedType?.label.toLowerCase()} value`}
                className="text-sm"
              />
            )}
          </div>
        )}
      </div>

      {/* Advanced Options */}
      <AnimatePresence>
        {isAdvanced && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-3 pt-3 border-t border-gray-200"
          >
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={condition.negate || false}
                  onChange={(e) => handleFieldChange('negate', e.target.checked)}
                  className="rounded"
                />
                <span className="text-xs text-gray-700">Negate condition (NOT)</span>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={condition.caseSensitive || false}
                  onChange={(e) => handleFieldChange('caseSensitive', e.target.checked)}
                  className="rounded"
                />
                <span className="text-xs text-gray-700">Case sensitive</span>
              </label>
            </div>

            {condition.field && (
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Custom Field</label>
                <Input
                  value={condition.field || ''}
                  onChange={(e) => handleFieldChange('field', e.target.value)}
                  placeholder="e.g., X-Priority, Custom-Header"
                  className="text-sm"
                />
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Preview */}
      <div className="text-xs text-gray-600 bg-white border border-gray-200 rounded p-2">
        <span className="font-medium">Preview:</span> {condition.negate && 'NOT '}
        {selectedType?.label} {selectedOperator?.label} {needsValue && String(condition.value)}
      </div>
    </motion.div>
  )
}

const ActionBuilder: React.FC<{
  action: RuleAction
  index: number
  onUpdate: (index: number, action: RuleAction) => void
  onRemove: (index: number) => void
  canRemove: boolean
}> = ({ action, index, onUpdate, onRemove, canRemove }) => {
  const handleFieldChange = (field: keyof RuleAction, value: any) => {
    onUpdate(index, { ...action, [field]: value })
  }

  const selectedType = actionTypes.find((t) => t.value === action.type)
  const Icon = selectedType?.icon || CogIcon

  const needsValue = ['label', 'categorize', 'forward', 'reply', 'moveToFolder'].includes(
    action.type
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-3"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-700">Action {index + 1}</span>
        </div>

        {canRemove && (
          <Button
            variant="ghost"
            size="xs"
            onClick={() => onRemove(index)}
            className="text-red-500 hover:text-red-700"
          >
            <XMarkIcon className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Action Type */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Action</label>
          <Select
            value={action.type}
            onValueChange={(value) => handleFieldChange('type', value as RuleActionType)}
          >
            {actionTypes.map((type) => {
              const TypeIcon = type.icon
              return (
                <Select.Item key={type.value} value={type.value}>
                  <div className="flex items-center gap-2">
                    <TypeIcon className="h-4 w-4" />
                    <div>
                      <div className="font-medium">{type.label}</div>
                      <div className="text-xs text-gray-500">{type.description}</div>
                    </div>
                  </div>
                </Select.Item>
              )
            })}
          </Select>
        </div>

        {/* Value */}
        {needsValue && (
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              {action.type === 'label'
                ? 'Label Name'
                : action.type === 'categorize'
                  ? 'Category'
                  : action.type === 'forward'
                    ? 'Email Address'
                    : action.type === 'reply'
                      ? 'Reply Template'
                      : action.type === 'moveToFolder'
                        ? 'Folder Path'
                        : 'Value'}
            </label>
            <Input
              value={String(action.value || '')}
              onChange={(e) => handleFieldChange('value', e.target.value)}
              placeholder={
                action.type === 'label'
                  ? 'Enter label name'
                  : action.type === 'categorize'
                    ? 'Select category'
                    : action.type === 'forward'
                      ? 'Enter email address'
                      : action.type === 'reply'
                        ? 'Enter reply message'
                        : action.type === 'moveToFolder'
                          ? 'Enter folder path'
                          : 'Enter value'
              }
              className="text-sm"
            />
          </div>
        )}
      </div>

      {/* Preview */}
      <div className="text-xs text-gray-600 bg-white border border-gray-200 rounded p-2">
        <span className="font-medium">Preview:</span> {selectedType?.label}{' '}
        {needsValue && action.value && `"${action.value}"`}
      </div>
    </motion.div>
  )
}

export const RuleBuilder: React.FC<RuleBuilderProps> = ({
  onSave,
  onTest,
  onCancel,
  initialRule,
  className,
  inline = false,
}) => {
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [testResults, setTestResults] = useState<any>(null)
  const [isTestLoading, setIsTestLoading] = useState(false)

  const form = useForm<RuleFormData>({
    defaultValues: {
      name: initialRule?.name || '',
      description: initialRule?.description || '',
      conditions: initialRule?.conditions || [
        {
          id: generateId(),
          type: 'from',
          operator: 'contains',
          value: '',
          negate: false,
          caseSensitive: false,
        },
      ],
      conditionLogic: initialRule?.conditionLogic || 'all',
      actions: initialRule?.actions || [
        {
          id: generateId(),
          type: 'archive',
          order: 1,
        },
      ],
      enabled: initialRule?.enabled ?? true,
    },
  })

  const {
    fields: conditionFields,
    append: appendCondition,
    remove: removeCondition,
    update: updateCondition,
  } = useFieldArray({
    control: form.control,
    name: 'conditions',
  })

  const {
    fields: actionFields,
    append: appendAction,
    remove: removeAction,
    update: updateAction,
  } = useFieldArray({
    control: form.control,
    name: 'actions',
  })

  const watchedData = form.watch()

  const handleAddCondition = () => {
    appendCondition({
      id: generateId(),
      type: 'from',
      operator: 'contains',
      value: '',
      negate: false,
      caseSensitive: false,
    })
  }

  const handleAddAction = () => {
    appendAction({
      id: generateId(),
      type: 'archive',
      order: actionFields.length + 1,
    })
  }

  const handleUpdateCondition = useCallback(
    (index: number, condition: RuleCondition) => {
      updateCondition(index, condition)
    },
    [updateCondition]
  )

  const handleUpdateAction = useCallback(
    (index: number, action: RuleAction) => {
      updateAction(index, action)
    },
    [updateAction]
  )

  const handleTest = async () => {
    if (!onTest) return

    setIsTestLoading(true)
    try {
      const rule = form.getValues()
      const results = await onTest(rule)
      setTestResults(results)
    } catch (error) {
      console.error('Test failed:', error)
    } finally {
      setIsTestLoading(false)
    }
  }

  const handleSave = () => {
    const rule = form.getValues()
    onSave?.(rule)
  }

  const rulePreview = `
If ${watchedData.conditionLogic === 'all' ? 'ALL' : 'ANY'} of these conditions are met:
${watchedData.conditions
  .map(
    (c, i) =>
      `  ${i + 1}. ${c.negate ? 'NOT ' : ''}${conditionTypes.find((t) => t.value === c.type)?.label} ${operators.find((o) => o.value === c.operator)?.label} ${c.value ? `"${c.value}"` : ''}`
  )
  .join('\n')}

Then perform these actions:
${watchedData.actions
  .map(
    (a, i) =>
      `  ${i + 1}. ${actionTypes.find((t) => t.value === a.type)?.label} ${a.value ? `"${a.value}"` : ''}`
  )
  .join('\n')}
  `.trim()

  if (inline) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            {...form.register('name', { required: true })}
            placeholder="Rule name"
            className="font-medium"
          />
          <Select
            value={watchedData.conditionLogic}
            onValueChange={(value) => form.setValue('conditionLogic', value as 'all' | 'any')}
          >
            <Select.Item value="all">Match ALL conditions</Select.Item>
            <Select.Item value="any">Match ANY condition</Select.Item>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button size="sm" onClick={handleAddCondition}>
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Condition
          </Button>
          <Button size="sm" onClick={handleAddAction}>
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Action
          </Button>
          <Button size="sm" onClick={handleSave}>
            <CheckIcon className="h-4 w-4 mr-1" />
            Save Rule
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CogIcon className="h-5 w-5" />
            Rule Builder
          </CardTitle>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => setIsPreviewMode(!isPreviewMode)}>
              <EyeIcon className="h-4 w-4 mr-1" />
              {isPreviewMode ? 'Edit' : 'Preview'}
            </Button>

            {onTest && (
              <Button variant="outline" size="sm" onClick={handleTest} loading={isTestLoading}>
                <PlayIcon className="h-4 w-4 mr-1" />
                Test
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <AnimatePresence mode="wait">
          {isPreviewMode ? (
            <motion.div
              key="preview"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Rule Preview</h4>
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">{rulePreview}</pre>
              </div>

              {testResults && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900 mb-2">Test Results</h4>
                  <pre className="text-sm text-green-800">
                    {JSON.stringify(testResults, null, 2)}
                  </pre>
                </div>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="builder"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-6"
            >
              {/* Basic Info */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    {...form.register('name', { required: true })}
                    label="Rule Name"
                    placeholder="My automation rule"
                    error={form.formState.errors.name ? 'Rule name is required' : undefined}
                  />
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Condition Logic
                    </label>
                    <Select
                      value={watchedData.conditionLogic}
                      onValueChange={(value) =>
                        form.setValue('conditionLogic', value as 'all' | 'any')
                      }
                    >
                      <Select.Item value="all">Match ALL conditions (AND)</Select.Item>
                      <Select.Item value="any">Match ANY condition (OR)</Select.Item>
                    </Select>
                  </div>
                </div>

                <Input
                  {...form.register('description')}
                  label="Description (optional)"
                  placeholder="Describe what this rule does"
                />
              </div>

              {/* Conditions */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Conditions</h3>
                  <Button variant="outline" size="sm" onClick={handleAddCondition}>
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Add Condition
                  </Button>
                </div>

                <AnimatePresence>
                  {conditionFields.map((field, index) => (
                    <ConditionBuilder
                      key={field.id}
                      condition={watchedData.conditions[index]}
                      index={index}
                      onUpdate={handleUpdateCondition}
                      onRemove={removeCondition}
                      canRemove={conditionFields.length > 1}
                    />
                  ))}
                </AnimatePresence>
              </div>

              {/* Actions */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Actions</h3>
                  <Button variant="outline" size="sm" onClick={handleAddAction}>
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Add Action
                  </Button>
                </div>

                <AnimatePresence>
                  {actionFields.map((field, index) => (
                    <ActionBuilder
                      key={field.id}
                      action={watchedData.actions[index]}
                      index={index}
                      onUpdate={handleUpdateAction}
                      onRemove={removeAction}
                      canRemove={actionFields.length > 1}
                    />
                  ))}
                </AnimatePresence>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <label className="flex items-center gap-2">
                  <input type="checkbox" {...form.register('enabled')} className="rounded" />
                  <span className="text-sm text-gray-700">Enable this rule</span>
                </label>

                <div className="flex items-center gap-2">
                  {onCancel && (
                    <Button variant="outline" onClick={onCancel}>
                      Cancel
                    </Button>
                  )}
                  <Button onClick={handleSave} className="flex items-center gap-2">
                    <CheckIcon className="h-4 w-4" />
                    Save Rule
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  )
}

export default RuleBuilder
