import { <PERSON><PERSON>, LuminarBadge, ProgressBar } from '@luminar/shared-ui'
/**
 * Performance Dashboard Component
 * Displays Core Web Vitals, bundle sizes, and performance metrics
 */

import {
  ArrowPathIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  CpuChipIcon,
  ExclamationTriangleIcon,
  ServerStackIcon,
  TrashIcon,
  WifiIcon,
} from '@heroicons/react/24/outline'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useWebVitals, type WebVitalsMetric, webVitalsMonitor } from '@/utils/performance/webVitals'
import { CacheManager, ServiceWorkerUpdateManager, StorageManager } from '@/utils/serviceWorker'

interface PerformanceData {
  bundleSize: number
  cacheSize: number
  storageQuota: {
    quota: number
    usage: number
    percentage: number
  } | null
  connectionInfo: {
    type: string
    downlink: number
    rtt: number
  } | null
}

export function PerformanceDashboard() {
  const webVitals = useWebVitals()
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    bundleSize: 0,
    cacheSize: 0,
    storageQuota: null,
    connectionInfo: null,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [swUpdateManager] = useState(() => new ServiceWorkerUpdateManager())

  useEffect(() => {
    loadPerformanceData()
  }, [])

  const loadPerformanceData = async () => {
    setIsLoading(true)

    try {
      const [cacheSize, storageQuota] = await Promise.all([
        CacheManager.getCacheSize(),
        StorageManager.getStorageQuota(),
      ])

      const connectionInfo = getConnectionInfo()

      setPerformanceData({
        bundleSize: getBundleSize(),
        cacheSize,
        storageQuota,
        connectionInfo,
      })
    } catch (error) {
      console.error('Error loading performance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getBundleSize = () => {
    // Estimate bundle size from loaded scripts
    const scripts = document.querySelectorAll('script[src]')
    let totalSize = 0

    scripts.forEach((script) => {
      const src = script.getAttribute('src')
      if (src && src.includes('/js/')) {
        // Rough estimate based on typical bundle sizes
        totalSize += 200000 // 200KB average per chunk
      }
    })

    return totalSize
  }

  const getConnectionInfo = () => {
    const nav = navigator as any
    if (nav.connection) {
      return {
        type: nav.connection.effectiveType,
        downlink: nav.connection.downlink,
        rtt: nav.connection.rtt,
      }
    }
    return null
  }

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'good':
        return 'bg-green-100 text-green-800'
      case 'needs-improvement':
        return 'bg-yellow-100 text-yellow-800'
      case 'poor':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / k ** i).toFixed(2)) + ' ' + sizes[i]
  }

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`
    return `${(ms / 1000).toFixed(2)}s`
  }

  const handleClearCache = async () => {
    await CacheManager.clearCache()
    await loadPerformanceData()
  }

  const handleRefresh = () => {
    loadPerformanceData()
  }

  const generateReport = () => {
    const report = webVitalsMonitor.generateReport()
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${new Date().toISOString()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Performance Dashboard</h2>
        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} variant="outline" size="sm" className="gap-2">
            <ArrowPathIcon className="h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={generateReport} variant="outline" size="sm">
            Export Report
          </Button>
        </div>
      </div>

      {/* Core Web Vitals */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {webVitals.map((metric) => (
          <Card key={metric.name}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              <Badge className={getRatingColor(metric.rating)}>{metric.rating}</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(metric.value)}</div>
              <p className="text-xs text-muted-foreground">{getMetricDescription(metric.name)}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bundle and Cache Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bundle Size</CardTitle>
            <ChartBarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(performanceData.bundleSize)}</div>
            <p className="text-xs text-muted-foreground">Estimated JavaScript bundle size</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Size</CardTitle>
            <ServerStackIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(performanceData.cacheSize)}</div>
            <p className="text-xs text-muted-foreground">Service worker cache usage</p>
            <Button onClick={handleClearCache} variant="outline" size="sm" className="mt-2 gap-2">
              <TrashIcon className="h-3 w-3" />
              Clear Cache
            </Button>
          </CardContent>
        </Card>

        {performanceData.storageQuota && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Storage Quota</CardTitle>
              <CpuChipIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatBytes(performanceData.storageQuota.usage)}
              </div>
              <p className="text-xs text-muted-foreground mb-2">
                of {formatBytes(performanceData.storageQuota.quota)} used
              </p>
              <Progress value={performanceData.storageQuota.percentage} className="h-2" />
            </CardContent>
          </Card>
        )}

        {performanceData.connectionInfo && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Connection</CardTitle>
              <WifiIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{performanceData.connectionInfo.type}</div>
              <p className="text-xs text-muted-foreground">
                {performanceData.connectionInfo.downlink} Mbps,
                {performanceData.connectionInfo.rtt}ms RTT
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Performance Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {generateRecommendations(webVitals, performanceData).map((rec, index) => (
              <div key={index} className="flex items-start gap-3">
                {rec.type === 'warning' ? (
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mt-0.5" />
                ) : (
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mt-0.5" />
                )}
                <div>
                  <p className="font-medium">{rec.title}</p>
                  <p className="text-sm text-muted-foreground">{rec.description}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function getMetricDescription(name: string): string {
  const descriptions = {
    CLS: 'Cumulative Layout Shift',
    FID: 'First Input Delay',
    FCP: 'First Contentful Paint',
    LCP: 'Largest Contentful Paint',
    TTFB: 'Time to First Byte',
  }
  return descriptions[name as keyof typeof descriptions] || name
}

function generateRecommendations(webVitals: WebVitalsMetric[], data: PerformanceData) {
  const recommendations = []

  // Check web vitals
  const poorMetrics = webVitals.filter((m) => m.rating === 'poor')
  const needsImprovement = webVitals.filter((m) => m.rating === 'needs-improvement')

  if (poorMetrics.length === 0 && needsImprovement.length === 0) {
    recommendations.push({
      type: 'success',
      title: 'Great performance!',
      description: 'All Core Web Vitals are in the good range.',
    })
  } else {
    if (poorMetrics.length > 0) {
      recommendations.push({
        type: 'warning',
        title: 'Poor Core Web Vitals detected',
        description: `${poorMetrics.map((m) => m.name).join(', ')} need immediate attention.`,
      })
    }

    if (needsImprovement.length > 0) {
      recommendations.push({
        type: 'warning',
        title: 'Core Web Vitals can be improved',
        description: `${needsImprovement.map((m) => m.name).join(', ')} can be optimized.`,
      })
    }
  }

  // Check bundle size
  if (data.bundleSize > 1000000) {
    // 1MB
    recommendations.push({
      type: 'warning',
      title: 'Large bundle size',
      description: 'Consider code splitting and tree shaking to reduce bundle size.',
    })
  }

  // Check cache size
  if (data.cacheSize > 50000000) {
    // 50MB
    recommendations.push({
      type: 'warning',
      title: 'Large cache size',
      description: 'Consider clearing old cache entries or reducing cache scope.',
    })
  }

  // Check storage quota
  if (data.storageQuota && data.storageQuota.percentage > 80) {
    recommendations.push({
      type: 'warning',
      title: 'High storage usage',
      description: 'Storage quota is almost full. Consider clearing cache or data.',
    })
  }

  return recommendations
}
