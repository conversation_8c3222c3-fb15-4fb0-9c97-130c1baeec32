import { Component, type ErrorInfo, type ReactNode } from 'react'
import { LoadingSpinner } from '@luminar/shared-ui'

interface ChartWrapperProps {
  children: ReactNode
  title?: string
  description?: string
  isLoading?: boolean
  error?: Error | null
  height?: number
  className?: string
}

interface ChartErrorBoundaryState {
  hasError: boolean
  error?: Error
}

// Error boundary for charts
class ChartErrorBoundary extends Component<
  { children: ReactNode; fallback?: ReactNode },
  ChartErrorBoundaryState
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Chart error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="flex items-center justify-center h-64 border border-dashed border-gray-300 rounded-lg">
            <div className="text-center">
              <p className="text-gray-500 mb-2">Chart failed to render</p>
              <button
                onClick={() => this.setState({ hasError: false })}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Try again
              </button>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}

// Loading skeleton for charts
function ChartSkeleton({ height = 300 }: { height?: number }) {
  return (
    <div className="animate-pulse" style={{ height }}>
      <div className="flex items-end justify-center space-x-2 h-full pb-8">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="bg-gray-200 dark:bg-gray-700 rounded-t"
            style={{
              width: '20px',
              height: `${Math.random() * 60 + 20}%`,
            }}
          />
        ))}
      </div>
      <div className="flex justify-between mt-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8" />
        ))}
      </div>
    </div>
  )
}

// Main chart wrapper component
export function ChartWrapper({
  children,
  title,
  description,
  isLoading = false,
  error = null,
  height = 300,
  className = '',
}: ChartWrapperProps) {
  if (error) {
    return (
      <div className={`border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="text-center">
          <p className="text-red-600 mb-2">Failed to load chart</p>
          <p className="text-sm text-gray-500">{error.message}</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className={className}>
        {title && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            {description && (
              <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>
            )}
          </div>
        )}
        <ChartSkeleton height={height} />
      </div>
    )
  }

  return (
    <div className={className}>
      {title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          {description && <p className="text-sm text-gray-600 dark:text-gray-400">{description}</p>}
        </div>
      )}
      <ChartErrorBoundary>
        <div
          role="img"
          aria-label={title || 'Chart'}
          tabIndex={0}
          className="focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
        >
          {children}
        </div>
      </ChartErrorBoundary>
    </div>
  )
}

// Export utilities
export { ChartErrorBoundary, ChartSkeleton }

// Accessibility helper for chart data
export function generateChartAriaLabel(
  chartType: string,
  dataPoints: number,
  valueRange?: { min: number; max: number }
): string {
  let label = `${chartType} with ${dataPoints} data points`

  if (valueRange) {
    label += `. Values range from ${valueRange.min} to ${valueRange.max}`
  }

  return label
}

// Chart export utilities
export const chartExportUtils = {
  // Convert chart data to CSV
  dataToCSV: (data: any[], headers: string[]): string => {
    const csvHeaders = headers.join(',')
    const csvRows = data.map((row) =>
      headers
        .map((header) => {
          const value = row[header]
          return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
        })
        .join(',')
    )
    return [csvHeaders, ...csvRows].join('\n')
  },

  // Download data as file
  downloadAsFile: (content: string, filename: string, type: string = 'text/csv') => {
    const blob = new Blob([content], { type })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  },

  // Convert chart to image (requires html2canvas)
  chartToImage: async (element: HTMLElement, filename: string): Promise<void> => {
    try {
      // This would require html2canvas to be installed
      // const html2canvas = await import('html2canvas')
      // const canvas = await html2canvas.default(element)
      // const url = canvas.toDataURL('image/png')
      // const a = document.createElement('a')
      // a.href = url
      // a.download = filename
      // a.click()

      console.log('Image export would be implemented with html2canvas')
      alert('Image export requires html2canvas library to be installed')
    } catch (error) {
      console.error('Failed to export chart as image:', error)
      throw error
    }
  },
}

// Responsive breakpoints for charts
export const chartBreakpoints = {
  mobile: 480,
  tablet: 768,
  desktop: 1024,
}

// Chart color schemes
export const chartColorSchemes = {
  default: {
    light: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'],
    dark: ['#60a5fa', '#34d399', '#fbbf24', '#f87171', '#a78bfa', '#22d3ee'],
  },
  accessibility: {
    light: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b'],
    dark: ['#aec7e8', '#ffbb78', '#98df8a', '#ff9896', '#c5b0d5', '#c49c94'],
  },
  monochrome: {
    light: ['#374151', '#6b7280', '#9ca3af', '#d1d5db', '#e5e7eb', '#f3f4f6'],
    dark: ['#f9fafb', '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af', '#6b7280'],
  },
}

// Get colors based on theme and scheme
export function getChartColors(
  isDark: boolean,
  scheme: keyof typeof chartColorSchemes = 'default'
): string[] {
  return chartColorSchemes[scheme][isDark ? 'dark' : 'light']
}
