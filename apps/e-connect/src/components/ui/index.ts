/**
 * Component Export Mapping for E-Connect UI Components
 * 
 * This file provides a centralized mapping between local components and shared-ui components
 * to enable smooth migration while maintaining backward compatibility.
 */

// Export shared-ui components with standardized names
export { Button, LuminarBadge as Badge } from '@luminar/shared-ui'
export { LuminarCheckbox as Checkbox, LuminarInput as Input, LuminarSelect as Select } from '@luminar/shared-ui/forms'
export { LoadingSpinner, ProgressBar as Progress } from '@luminar/shared-ui/display'

// Export local components that don't have shared-ui equivalents yet
export { Loading, LoadingContent } from './Loading'
export { Progress as LocalProgress } from './Progress'
export { Badge as LocalBadge } from './Badge'
export { Tabs, TabsList, TabsTrigger, TabsContent } from './Tabs'
export { Toast, ToastProvider, useToast } from './Toast'
export { Card, CardContent, CardHeader, CardTitle } from './Card'
export { Dialog } from './Dialog'
export { Container } from './Container'
export { Toggle } from './Toggle'
export { Tooltip } from './Tooltip'
export { Slider } from './Slider'
export { RadioGroup } from './RadioGroup'
export { KeyboardShortcuts } from './KeyboardShortcuts'
export { KeyboardShortcutsWrapper } from './KeyboardShortcutsWrapper'

// Export alert and dropdown menu components
export * from './alert'
export * from './dropdown-menu'
export * from './separator'

// Re-export types
export type { BadgeProps } from './Badge'
export type { CheckboxProps } from './Checkbox'
export type { InputProps } from './Input'
export type { ToastProps, ToastData } from './Toast'