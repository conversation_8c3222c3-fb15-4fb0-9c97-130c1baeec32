#!/usr/bin/env node

/**
 * E-Connect to Shared-UI Migration Script
 * 
 * This script automatically migrates local UI component imports to @luminar/shared-ui
 * Run with: node migrate-to-shared-ui.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Component mapping from local to shared-ui
const COMPONENT_MAPPING = {
  // Direct 1:1 mappings
  'Button': { import: 'Button', from: '@luminar/shared-ui' },
  'Input': { import: 'Input', from: '@luminar/shared-ui' },
  'Select': { import: 'Select', from: '@luminar/shared-ui' },
  'Checkbox': { import: 'Checkbox', from: '@luminar/shared-ui' },
  'RadioGroup': { import: 'RadioGroup', from: '@luminar/shared-ui' },
  'Progress': { import: 'ProgressBar', from: '@luminar/shared-ui' },
  'Badge': { import: 'LuminarBadge', from: '@luminar/shared-ui' },
  'Slider': { import: 'Slider', from: '@luminar/shared-ui' },
  'Toggle': { import: 'Switch', from: '@luminar/shared-ui' },
  'Loading': { import: 'LoadingSpinner', from: '@luminar/shared-ui' },
  'Toast': { import: 'Toast', from: '@luminar/shared-ui' },
  'Tooltip': { import: 'Tooltip', from: '@luminar/shared-ui' },
  'Dialog': { import: 'Modal', from: '@luminar/shared-ui' },
  'alert': { import: 'Alert', from: '@luminar/shared-ui' },
  'dropdown-menu': { import: 'Dropdown', from: '@luminar/shared-ui' },
  'separator': { import: 'Separator', from: '@luminar/shared-ui' },
  'Tabs': { import: 'Tabs', from: '@luminar/shared-ui' }
};

// Components to keep local (app-specific)
const KEEP_LOCAL = [
  'Container',
  'KeyboardShortcuts', 
  'KeyboardShortcutsWrapper'
];

// File patterns to process
const FILE_PATTERNS = [
  '**/*.tsx',
  '**/*.ts'
];

const SRC_DIR = path.join(__dirname, 'src');

function getAllFiles(dir, pattern = /\.(tsx?|jsx?)$/) {
  const files = [];
  
  function traverse(currentDir) {
    const entries = fs.readdirSync(currentDir);
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !entry.startsWith('.')) {
        traverse(fullPath);
      } else if (pattern.test(entry)) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function migrateFile(filePath) {
  console.log(`Processing: ${path.relative(process.cwd(), filePath)}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Track imports to group them
  const sharedUIImports = new Set();
  const linesToRemove = [];
  
  // Process each line
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Match import statements from local ui components
    const importMatch = line.match(/import\s+{([^}]+)}\s+from\s+['"]@\/components\/ui\/([^'"]+)['"]/);
    
    if (importMatch) {
      const imports = importMatch[1].split(',').map(imp => imp.trim());
      const componentFile = importMatch[2];
      
      let hasSharedUIComponents = false;
      const keepLocalImports = [];
      
      for (const imp of imports) {
        const cleanImport = imp.replace(/ as .+$/, ''); // Remove aliases
        
        if (COMPONENT_MAPPING[componentFile] || COMPONENT_MAPPING[cleanImport]) {
          const mapping = COMPONENT_MAPPING[componentFile] || COMPONENT_MAPPING[cleanImport];
          sharedUIImports.add(mapping.import);
          hasSharedUIComponents = true;
        } else if (KEEP_LOCAL.includes(cleanImport)) {
          keepLocalImports.push(imp);
        } else {
          // Try to find component mapping by import name
          const found = Object.entries(COMPONENT_MAPPING).find(([key, value]) => 
            key === cleanImport || value.import === cleanImport
          );
          
          if (found) {
            sharedUIImports.add(found[1].import);
            hasSharedUIComponents = true;
          } else {
            keepLocalImports.push(imp);
          }
        }
      }
      
      if (hasSharedUIComponents) {
        if (keepLocalImports.length > 0) {
          // Replace line with remaining local imports
          lines[i] = `import { ${keepLocalImports.join(', ')} } from '@/components/ui/${componentFile}'`;
        } else {
          // Mark line for removal
          linesToRemove.push(i);
        }
        modified = true;
      }
    }
  }
  
  // Remove marked lines
  for (let i = linesToRemove.length - 1; i >= 0; i--) {
    lines.splice(linesToRemove[i], 1);
  }
  
  // Add shared-ui import at the top
  if (sharedUIImports.size > 0) {
    const sharedUIImportLine = `import { ${Array.from(sharedUIImports).sort().join(', ')} } from '@luminar/shared-ui'`;
    
    // Find where to insert (after other imports)
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ') || lines[i].startsWith('import{')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '') {
        continue;
      } else {
        break;
      }
    }
    
    lines.splice(insertIndex, 0, sharedUIImportLine);
    modified = true;
  }
  
  if (modified) {
    const newContent = lines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ Updated: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

function main() {
  console.log('🚀 Starting E-Connect to Shared-UI migration...\n');
  
  if (!fs.existsSync(SRC_DIR)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }
  
  const files = getAllFiles(SRC_DIR);
  console.log(`📁 Found ${files.length} files to process\n`);
  
  let modifiedCount = 0;
  
  for (const file of files) {
    try {
      if (migrateFile(file)) {
        modifiedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n✨ Migration complete!`);
  console.log(`📊 Modified ${modifiedCount} out of ${files.length} files`);
  
  if (modifiedCount > 0) {
    console.log('\n📝 Next steps:');
    console.log('1. Run `npm run typecheck` to verify TypeScript compilation');
    console.log('2. Run `npm run dev` to test the application');
    console.log('3. Review and test components for visual consistency');
    console.log('4. Remove unused local component files');
  }
}

main();

export { migrateFile, COMPONENT_MAPPING };