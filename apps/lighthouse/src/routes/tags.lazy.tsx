import { useQuery } from '@tanstack/react-query'
import { createLazyFileRoute } from '@tanstack/react-router'
import {
  BarChart3,
  CheckSquare,
  FileText,
  Filter,
  Hash,
  Plus,
  Search,
  Settings,
  Sparkles,
  Square,
  Tags,
  TrendingUp,
  X,
  Zap,
} from 'lucide-react'
import { useMemo, useState } from 'react'
import { DocumentCard } from '~/components/DocumentCard'
import { BulkTagEditor } from '~/components/tags/BulkTagEditor'
import { TagManager } from '~/components/tags/TagManager'
import { TagSuggestions } from '~/components/tags/TagSuggestions'
import { 
  Button, 
  LuminarBadge as Badge, 
  LuminarInput as Input 
} from '@luminar/shared-ui'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { useFilteredDocuments, useSelectedDocuments } from '~/hooks/useStoreSelectors'
import { useLighthouseStore } from '~/store/lighthousestore'
import type { ProcessedDocument, Tag } from '~/types'

type ViewMode = 'overview' | 'bulk-edit' | 'auto-tag' | 'analytics'

function TagsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTagForFilter, setSelectedTagForFilter] = useState<string | null>(null)
  const [showAutoTagging, setShowAutoTagging] = useState(false)
  const [autoTagContent, setAutoTagContent] = useState('')

  // Store state
  const {
    tags: tagsMap,
    filters,
    addTag,
    deleteTag,
    setFilter,
    selectAll,
    deselectAll,
    selectedDocumentIds,
  } = useLighthouseStore()

  // Get data
  const tags = useMemo(() => Array.from(tagsMap.values()), [tagsMap])
  const selectedDocuments = useSelectedDocuments()
  const filteredDocuments = useFilteredDocuments()

  // Fetch all documents for analytics
  const { data: allDocuments = [] } = useQuery<ProcessedDocument[]>({
    queryKey: ['documents'],
    queryFn: () => fetch('/api/documents').then((res) => res.json()),
  })

  // Filter tags based on search
  const filteredTags = useMemo(() => {
    if (!searchTerm) return tags
    return tags.filter((tag) => tag.name.toLowerCase().includes(searchTerm.toLowerCase()))
  }, [tags, searchTerm])

  // Tag analytics
  const tagAnalytics = useMemo(() => {
    const analytics = tags.map((tag) => {
      const documentsWithTag = allDocuments.filter((doc) => doc.tags.includes(tag.name))
      return {
        ...tag,
        usage: documentsWithTag.length,
        recentUsage: documentsWithTag.filter(
          (doc) => new Date(doc.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        ).length,
      }
    })

    return analytics.sort((a, b) => b.usage - a.usage)
  }, [tags, allDocuments])

  // Most used tags
  const mostUsedTags = tagAnalytics.slice(0, 10)
  const unusedTags = tagAnalytics.filter((tag) => tag.usage === 0)

  // Handle tag actions
  const handleCreateTag = (tagName: string) => {
    if (tagName && !tags.find((t) => t.name === tagName)) {
      addTag({
        id: `tag-${Date.now()}`,
        name: tagName,
        color: '#3B82F6',
        documentCount: 0,
      })
    }
  }

  const handleTagSuggestionSelect = (tagName: string) => {
    handleCreateTag(tagName)
  }

  const handleFilterByTag = (tagName: string) => {
    setSelectedTagForFilter(tagName)
    setFilter('tags', [tagName])
  }

  const clearTagFilter = () => {
    setSelectedTagForFilter(null)
    setFilter('tags', [])
  }

  const handleSelectAll = () => {
    if (selectedDocumentIds.size === filteredDocuments.length) {
      deselectAll()
    } else {
      selectAll(filteredDocuments.map((doc) => doc.id))
    }
  }

  const handleAutoTagAll = async () => {
    // This would trigger auto-tagging for all documents
    // For now, just show a placeholder
    console.log('Auto-tagging all documents...')
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Tags className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Tag Management</h1>
          </div>

          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            <div className="flex border border-gray-300 rounded-md">
              <Button
                variant={viewMode === 'overview' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('overview')}
                className="rounded-r-none"
              >
                <Hash className="w-4 h-4 mr-1" />
                Overview
              </Button>
              <Button
                variant={viewMode === 'bulk-edit' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('bulk-edit')}
                className="rounded-none"
              >
                <Settings className="w-4 h-4 mr-1" />
                Bulk Edit
              </Button>
              <Button
                variant={viewMode === 'auto-tag' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('auto-tag')}
                className="rounded-none"
              >
                <Sparkles className="w-4 h-4 mr-1" />
                Auto Tag
              </Button>
              <Button
                variant={viewMode === 'analytics' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('analytics')}
                className="rounded-l-none"
              >
                <BarChart3 className="w-4 h-4 mr-1" />
                Analytics
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Tag Search */}
          <div className="p-4 border-b border-gray-200">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
              <LuminarInput
                placeholder="Search tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Tag List */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {filteredTags.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {searchTerm ? 'No tags match your search' : 'No tags created yet'}
                </div>
              ) : (
                filteredTags.map((tag) => (
                  <div
                    key={tag.id}
                    className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedTagForFilter === tag.name
                        ? 'bg-blue-50 border-blue-300'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      if (selectedTagForFilter === tag.name) {
                        clearTagFilter()
                      } else {
                        handleFilterByTag(tag.name)
                      }
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="font-medium">{tag.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <LuminarBadge variant="secondary" className="text-xs">
                        {tag.documentCount}
                      </LuminarBadge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteTag(tag.id)
                        }}
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="p-4 border-t border-gray-200">
            <div className="space-y-2">
              <Button onClick={handleAutoTagAll} className="w-full justify-start" variant="outline">
                <Zap className="w-4 h-4 mr-2" />
                Auto-tag All Documents
              </Button>
              {selectedTagForFilter && (
                <Button onClick={clearTagFilter} variant="ghost" className="w-full justify-start">
                  <X className="w-4 h-4 mr-2" />
                  Clear Filter
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Content Header */}
          <div className="bg-white border-b border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold">
                  {viewMode === 'overview' && 'Tag Overview'}
                  {viewMode === 'bulk-edit' && 'Bulk Tag Editor'}
                  {viewMode === 'auto-tag' && 'Auto Tagging'}
                  {viewMode === 'analytics' && 'Tag Analytics'}
                </h2>
                <p className="text-sm text-gray-600">
                  {viewMode === 'overview' && 'Manage your tags and filter documents'}
                  {viewMode === 'bulk-edit' && `${selectedDocuments.length} documents selected`}
                  {viewMode === 'auto-tag' && 'Generate tags automatically using AI'}
                  {viewMode === 'analytics' && 'Tag usage statistics and insights'}
                </p>
              </div>

              {viewMode === 'bulk-edit' && (
                <Button
                  onClick={handleSelectAll}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {selectedDocumentIds.size === filteredDocuments.length &&
                  filteredDocuments.length > 0 ? (
                    <CheckSquare className="w-4 h-4" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                  Select All
                </Button>
              )}
            </div>
          </div>

          {/* Content Body */}
          <div className="flex-1 overflow-y-auto p-4">
            {viewMode === 'overview' && (
              <div className="space-y-6">
                {/* Tag Manager */}
                <TagManager />

                {/* Filtered Documents */}
                {selectedTagForFilter && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Documents with "{selectedTagForFilter}" tag
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {filteredDocuments.length === 0 ? (
                        <div className="text-center py-8">
                          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600">No documents found with this tag</p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {filteredDocuments.map((document) => (
                            <DocumentCard key={document.id} document={document} className="h-fit" />
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {viewMode === 'bulk-edit' && (
              <div className="space-y-6">
                {selectedDocuments.length === 0 ? (
                  <div className="text-center py-12">
                    <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      No documents selected
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Select documents to add or remove tags in bulk
                    </p>
                    <Button onClick={() => setViewMode('overview')}>Go to Overview</Button>
                  </div>
                ) : (
                  <div>
                    <BulkTagEditor />

                    <div className="mt-6">
                      <h3 className="text-lg font-semibold mb-4">Selected Documents</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {selectedDocuments.map((document) => (
                          <DocumentCard key={document.id} document={document} className="h-fit" />
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {viewMode === 'auto-tag' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="w-5 h-5" />
                      Auto Tag Generator
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Enter content to generate tag suggestions:
                        </label>
                        <textarea
                          value={autoTagContent}
                          onChange={(e) => setAutoTagContent(e.target.value)}
                          placeholder="Paste content here to generate relevant tags..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={6}
                        />
                      </div>

                      {autoTagContent && (
                        <div>
                          <h4 className="font-medium mb-2">Suggested Tags:</h4>
                          <TagSuggestions
                            content={autoTagContent}
                            onSelect={handleTagSuggestionSelect}
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {viewMode === 'analytics' && (
              <div className="space-y-6">
                {/* Tag Usage Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Total Tags</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{tags.length}</div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Most Used Tag</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{mostUsedTags[0]?.name || 'None'}</div>
                      <div className="text-sm text-gray-600">
                        {mostUsedTags[0]?.usage || 0} documents
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Unused Tags</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-red-600">{unusedTags.length}</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Most Used Tags */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Most Used Tags
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mostUsedTags.map((tag, index) => (
                        <div key={tag.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium text-gray-500 w-6">
                              #{index + 1}
                            </span>
                            <LuminarBadge variant="outline">{tag.name}</LuminarBadge>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="text-sm text-gray-600">{tag.usage} documents</div>
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{
                                  width: `${Math.min(100, (tag.usage / Math.max(mostUsedTags[0]?.usage || 1, 1)) * 100)}%`,
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Unused Tags */}
                {unusedTags.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-red-600">
                        <X className="w-5 h-5" />
                        Unused Tags
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {unusedTags.map((tag) => (
                          <LuminarBadge key={tag.id} variant="outline" className="text-red-600">
                            {tag.name}
                          </LuminarBadge>
                        ))}
                      </div>
                      <div className="mt-4">
                        <p className="text-sm text-gray-600">
                          Consider removing unused tags to keep your tag system organized.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export const Route = createLazyFileRoute('/tags')({
  component: TagsPage,
})
