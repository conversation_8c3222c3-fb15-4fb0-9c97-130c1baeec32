import { createLazyFileRoute } from '@tanstack/react-router'
import {
  Brain,
  ChevronLeft,
  ChevronRight,
  MessageCircle,
  Network,
  PanelLeft,
  PanelRight,
  Settings,
  Sparkles,
} from 'lucide-react'
import { useMemo, useState } from 'react'
import { ChatInterface } from '~/components/research/ChatInterface'
import { DocumentSelector } from '~/components/research/DocumentSelector'
import { KnowledgeGraphComponent } from '~/components/research/KnowledgeGraph'
import { ChatInterfaceSkeleton, ThreeColumnLayoutSkeleton } from '~/components/skeletons'
import { 
  Button, 
  LuminarBadge as Badge 
} from '@luminar/shared-ui'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { useLighthouseStore } from '~/store/lighthousestore'

type ViewMode = 'chat' | 'graph' | 'split'

function ResearchPage() {
  const [showDocumentSelector, setShowDocumentSelector] = useState(true)
  const [showKnowledgeGraph, setShowKnowledgeGraph] = useState(false)
  const [viewMode, setViewMode] = useState<ViewMode>('chat')
  const [showModelSettings, setShowModelSettings] = useState(false)

  // Store state
  const {
    ai: { currentModel, isProcessing },
    ui: { selectedDocumentIds },
    setAIModel,
    deselectAll,
  } = useLighthouseStore()

  // Convert Set to Array for components
  const selectedDocumentIdsArray = useMemo(
    () => Array.from(selectedDocumentIds),
    [selectedDocumentIds]
  )

  const handleModelChange = (model: 'gpt-4o' | 'claude-3-sonnet' | 'gemini-pro') => {
    setAIModel(model)
    setShowModelSettings(false)
  }

  const toggleDocumentSelector = () => {
    setShowDocumentSelector(!showDocumentSelector)
  }

  const toggleKnowledgeGraph = () => {
    setShowKnowledgeGraph(!showKnowledgeGraph)
    if (!showKnowledgeGraph && viewMode === 'chat') {
      setViewMode('split')
    }
  }

  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode)
    if (mode === 'graph') {
      setShowKnowledgeGraph(true)
    } else if (mode === 'chat') {
      setShowKnowledgeGraph(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar - Document Selector */}
      <div
        className={`${showDocumentSelector ? 'w-80' : 'w-0'} transition-all duration-300 bg-white border-r border-gray-200 flex flex-col overflow-hidden`}
      >
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Brain className="w-5 h-5 text-blue-600" />
              Research Assistant
            </h2>
            <Button variant="ghost" size="sm" onClick={toggleDocumentSelector}>
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          <DocumentSelector />
        </div>

        {/* Selection Summary */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">
              {selectedDocumentIdsArray.length} documents selected
            </span>
            {selectedDocumentIdsArray.length > 0 && (
              <Button variant="ghost" size="sm" onClick={deselectAll} className="text-xs">
                Clear All
              </Button>
            )}
          </div>
          {selectedDocumentIdsArray.length > 0 && (
            <LuminarBadge variant="outline" className="text-xs">
              Ready for AI queries
            </LuminarBadge>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {!showDocumentSelector && (
                <Button variant="ghost" size="sm" onClick={toggleDocumentSelector}>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              )}

              <div className="flex items-center gap-2">
                <h1 className="text-xl font-bold">AI Research</h1>
                {isProcessing && (
                  <div className="flex items-center gap-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">Processing...</span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-md">
                <Button
                  variant={viewMode === 'chat' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('chat')}
                  className="rounded-r-none"
                >
                  <MessageCircle className="w-4 h-4 mr-1" />
                  Chat
                </Button>
                <Button
                  variant={viewMode === 'split' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('split')}
                  className="rounded-none"
                >
                  <PanelRight className="w-4 h-4 mr-1" />
                  Split
                </Button>
                <Button
                  variant={viewMode === 'graph' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('graph')}
                  className="rounded-l-none"
                >
                  <Network className="w-4 h-4 mr-1" />
                  Graph
                </Button>
              </div>

              {/* AI Model Settings */}
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowModelSettings(!showModelSettings)}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  {currentModel}
                </Button>

                {showModelSettings && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="p-2">
                      <div className="text-sm font-medium mb-2">AI Model</div>
                      <div className="space-y-1">
                        {[
                          { id: 'gpt-4o', name: 'GPT-4o', description: 'OpenAI' },
                          {
                            id: 'claude-3-sonnet',
                            name: 'Claude 3 Sonnet',
                            description: 'Anthropic',
                          },
                          { id: 'gemini-pro', name: 'Gemini Pro', description: 'Google' },
                        ].map((model) => (
                          <button
                            key={model.id}
                            onClick={() => handleModelChange(model.id as any)}
                            className={`w-full text-left px-2 py-1 rounded text-sm ${
                              currentModel === model.id
                                ? 'bg-blue-100 text-blue-700'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <div className="font-medium">{model.name}</div>
                            <div className="text-xs text-gray-600">{model.description}</div>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Chat Interface */}
          <div
            className={`${
              viewMode === 'chat' ? 'flex-1' : viewMode === 'split' ? 'flex-1' : 'hidden'
            } bg-white`}
          >
            {selectedDocumentIdsArray.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center max-w-md">
                  <Sparkles className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Welcome to AI Research
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Select documents from your library to start asking questions and exploring
                    insights with AI.
                  </p>
                  <Button onClick={() => setShowDocumentSelector(true)}>
                    <PanelLeft className="w-4 h-4 mr-2" />
                    Select Documents
                  </Button>
                </div>
              </div>
            ) : (
              <ChatInterface documentIds={selectedDocumentIdsArray} className="h-full" />
            )}
          </div>

          {/* Knowledge Graph */}
          {(viewMode === 'graph' || viewMode === 'split') && (
            <div
              className={`${
                viewMode === 'graph'
                  ? 'flex-1'
                  : viewMode === 'split'
                    ? 'w-1/2 border-l border-gray-200'
                    : 'hidden'
              } bg-white p-4`}
            >
              <KnowledgeGraphComponent documentIds={selectedDocumentIdsArray} />
            </div>
          )}
        </div>

        {/* Status Bar */}
        <div className="bg-white border-t border-gray-200 p-3">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>Model: {currentModel}</span>
              <span>Documents: {selectedDocumentIdsArray.length}</span>
            </div>

            <div className="flex items-center gap-4">
              {isProcessing && <span className="text-blue-600">Processing query...</span>}
              <span>View: {viewMode}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const Route = createLazyFileRoute('/research')({
  component: ResearchPage,
})
