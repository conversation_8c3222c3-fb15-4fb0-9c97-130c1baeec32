import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { <PERSON><PERSON>hart3, Brain, Library, Search, Tags, Upload, Zap } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { But<PERSON> } from '@luminar/shared-ui'

export const Route = createFileRoute('/')({
  component: Home,
})

function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to Lighthouse</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Your AI-powered research assistant for uploading documents, querying with AI, and
            visualizing knowledge graphs. Transform your research workflow with intelligent
            insights.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <Link to="/library" className="block">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <Library className="w-5 h-5 text-blue-600" />
                  <CardTitle className="text-lg">Document Library</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Upload, organize, and manage your research documents
                </CardDescription>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <Link to="/research" className="block">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <Brain className="w-5 h-5 text-green-600" />
                  <CardTitle className="text-lg">AI Research</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Query your documents with AI and explore knowledge graphs
                </CardDescription>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <Link to="/tags" className="block">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <Tags className="w-5 h-5 text-purple-600" />
                  <CardTitle className="text-lg">Tag Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Organize and categorize your content with smart tags
                </CardDescription>
              </CardContent>
            </Link>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <Link to="/analytics" className="block">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-orange-600" />
                  <CardTitle className="text-lg">Analytics</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription>Track your research activity and usage insights</CardDescription>
              </CardContent>
            </Link>
          </Card>
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-lg shadow p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                <Upload className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Document Upload</h3>
              <p className="text-gray-600">
                Upload PDFs, Word documents, and web URLs. Our AI processes and indexes content for
                intelligent search.
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-4">
                <Search className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">AI-Powered Search</h3>
              <p className="text-gray-600">
                Ask questions in natural language and get intelligent answers from your documents
                with source citations.
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-4">
                <Zap className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Knowledge Graphs</h3>
              <p className="text-gray-600">
                Visualize connections between concepts and documents with interactive knowledge
                graphs.
              </p>
            </div>
          </div>
        </div>

        {/* Getting Started */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to get started?</h2>
          <p className="text-gray-600 mb-6">
            Begin by uploading your first document to start building your knowledge base.
          </p>
          <Link to="/library">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              <Upload className="w-4 h-4 mr-2" />
              Upload Your First Document
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
