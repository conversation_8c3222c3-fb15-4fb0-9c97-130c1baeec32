import {
  AMNAWidget,
  AuthProvider,
  AuthStatusIndicator,
  CrossAppNavigation,
  IntegrationProvider,
  QuickNavButtons,
  ThemeProvider,
  Toaster,
  UserMenu,
} from '@luminar/shared-ui'
import { createRootRoute, HeadContent, Link, Outlet, Scripts } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { BarChart3, Brain, Home, Library, Tags } from 'lucide-react'
import type * as React from 'react'
import { DefaultCatchBoundary } from '~/components/DefaultCatchBoundary'
import { NotFound } from '~/components/NotFound'
import { QueryProvider } from '~/components/QueryProvider'
import appCss from '~/styles/app.css?url'
import { seo } from '~/utils/seo'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title: 'Lighthouse - AI Research Assistant',
        description:
          'AI-powered knowledge management platform for uploading documents, querying with AI, and visualizing knowledge graphs. Built with React, TypeScript, and modern AI services.',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
  }),
  errorComponent: (props) => {
    return (
      <QueryProvider>
        <RootDocument>
          <DefaultCatchBoundary {...props} />
        </RootDocument>
      </QueryProvider>
    )
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
})

function RootComponent() {
  return (
    <QueryProvider>
      <AuthProvider>
        <ThemeProvider>
          <IntegrationProvider>
            <RootDocument>
              <Outlet />
            </RootDocument>
          </IntegrationProvider>
        </ThemeProvider>
      </AuthProvider>
    </QueryProvider>
  )
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body className="min-h-screen flex flex-col">
        <nav className="border-b border-gray-200 bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link
                  to="/"
                  className="text-2xl font-bold text-blue-600 hover:text-blue-700"
                  activeOptions={{ exact: true }}
                >
                  Lighthouse
                </Link>
                <span className="ml-2 text-sm text-gray-500">AI Research Assistant</span>
              </div>
              <div className="flex items-center space-x-8">
                <Link
                  to="/"
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2"
                  activeProps={{
                    className: 'text-blue-600 bg-blue-50 hover:text-blue-600',
                  }}
                  activeOptions={{ exact: true }}
                >
                  <Home className="w-4 h-4" />
                  Home
                </Link>
                <Link
                  to="/library"
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2"
                  activeProps={{
                    className: 'text-blue-600 bg-blue-50 hover:text-blue-600',
                  }}
                >
                  <Library className="w-4 h-4" />
                  Library
                </Link>
                <Link
                  to="/research"
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2"
                  activeProps={{
                    className: 'text-blue-600 bg-blue-50 hover:text-blue-600',
                  }}
                >
                  <Brain className="w-4 h-4" />
                  Research
                </Link>
                <Link
                  to="/tags"
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2"
                  activeProps={{
                    className: 'text-blue-600 bg-blue-50 hover:text-blue-600',
                  }}
                >
                  <Tags className="w-4 h-4" />
                  Tags
                </Link>
                <Link
                  to="/analytics"
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2"
                  activeProps={{
                    className: 'text-blue-600 bg-blue-50 hover:text-blue-600',
                  }}
                >
                  <BarChart3 className="w-4 h-4" />
                  Analytics
                </Link>

                {/* Cross-app navigation and Auth */}
                <div className="flex items-center space-x-4">
                  <QuickNavButtons className="hidden md:flex" />
                  <div className="h-6 w-px bg-gray-300" />
                  <CrossAppNavigation variant="dropdown" />
                  <div className="h-6 w-px bg-gray-300" />
                  <AuthStatusIndicator variant="compact" />
                  <UserMenu />
                </div>
              </div>
            </div>
          </div>
        </nav>
        <main className="flex-1 bg-gray-50">{children}</main>

        {/* AMNAWidget for cross-app AI assistance */}
        <AMNAWidget
          position="bottom-left"
          context={{
            app: 'lighthouse',
            page: typeof window !== 'undefined' ? window.location.pathname : '/',
            environment: 'production',
          }}
          showBadge={true}
        />

        <Toaster />
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  )
}
