import { useQuery } from '@tanstack/react-query'
import { createLazyFileRoute } from '@tanstack/react-router'
import {
  CheckSquare,
  Download,
  FileText,
  Filter,
  Grid,
  Link as LinkIcon,
  List,
  Plus,
  Search,
  Square,
  Trash2,
  Upload,
  X,
} from 'lucide-react'
import { useRef, useState } from 'react'
import { DocumentCard } from '~/components/DocumentCard'
import { DocumentSearch } from '~/components/DocumentSearch'
import { FolderTree } from '~/components/folders/FolderTree'
import { DocumentGridSkeleton, SidebarSkeleton } from '~/components/skeletons'
import { 
  But<PERSON>, 
  LuminarBadge as Badge,
  LuminarInput as Input
} from '@luminar/shared-ui'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import {
  useBulkDocumentDelete,
  useDocumentDelete,
  useDocuments,
  useDocumentUpload,
} from '~/hooks/useLighthouseQuery'
import { useLighthouseStore } from '~/store/lighthousestore'
import type { Folder, Tag } from '~/types'

function LibraryPage() {
  const [showUpload, setShowUpload] = useState(false)
  const [uploadType, setUploadType] = useState<'file' | 'url'>('file')
  const [urlInput, setUrlInput] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Store state
  const filters = useLighthouseStore((state) => state.filters)
  const ui = useLighthouseStore((state) => state.ui)
  const setFilters = useLighthouseStore((state) => state.setFilters)
  const setViewMode = useLighthouseStore((state) => state.setViewMode)
  const toggleSelection = useLighthouseStore((state) => state.toggleSelection)
  const selectAll = useLighthouseStore((state) => state.selectAll)
  const deselectAll = useLighthouseStore((state) => state.deselectAll)

  // Hooks
  const { data: documents = [], isLoading, error } = useDocuments()
  const uploadMutation = useDocumentUpload()
  const deleteMutation = useDocumentDelete()
  const bulkDeleteMutation = useBulkDocumentDelete()

  // Fetch tags and folders for search
  const { data: tags = [] } = useQuery<Tag[]>({
    queryKey: ['tags'],
    queryFn: () => fetch('/api/tags').then((res) => res.json()),
  })

  const { data: folders = [] } = useQuery<Folder[]>({
    queryKey: ['folders'],
    queryFn: () => fetch('/api/folders').then((res) => res.json()),
  })

  // Upload handlers
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const formData = new FormData()
    formData.append('file', file)
    if (filters.folderId) {
      formData.append('folderId', filters.folderId)
    }

    await uploadMutation.mutateAsync(formData)
    setShowUpload(false)
  }

  const handleUrlUpload = async () => {
    if (!urlInput.trim()) return

    const formData = new FormData()
    formData.append('url', urlInput)
    if (filters.folderId) {
      formData.append('folderId', filters.folderId)
    }

    await uploadMutation.mutateAsync(formData)
    setUrlInput('')
    setShowUpload(false)
  }

  // Selection handlers
  const handleSelectAll = () => {
    if (ui.selectedDocumentIds.size === documents.length) {
      deselectAll()
    } else {
      selectAll(documents.map((doc) => doc.id))
    }
  }

  const handleBulkDelete = async () => {
    if (ui.selectedDocumentIds.size === 0) return

    const confirmed = window.confirm(
      `Are you sure you want to delete ${ui.selectedDocumentIds.size} documents?`
    )
    if (!confirmed) return

    await bulkDeleteMutation.mutateAsync(Array.from(ui.selectedDocumentIds))
  }

  // Filter handlers
  const handleSearch = (searchFilters: any) => {
    setFilters(searchFilters)
  }

  const clearAllFilters = () => {
    setFilters({})
  }

  const selectedCount = ui.selectedDocumentIds.size
  const hasFilters = filters.search || filters.folderId || (filters.tags && filters.tags.length > 0)

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-red-600">Error loading documents</h2>
          <p className="text-gray-600 mt-2">Please try refreshing the page</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-900">Document Library</h1>
        </div>

        {/* Upload Section */}
        <div className="p-4 border-b border-gray-200">
          <Button
            onClick={() => setShowUpload(!showUpload)}
            className="w-full"
            variant={showUpload ? 'outline' : 'default'}
          >
            <Plus className="w-4 h-4 mr-2" />
            {showUpload ? 'Cancel Upload' : 'Add Documents'}
          </Button>

          {showUpload && (
            <div className="mt-4 space-y-3">
              <div className="flex gap-2">
                <Button
                  variant={uploadType === 'file' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setUploadType('file')}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  File
                </Button>
                <Button
                  variant={uploadType === 'url' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setUploadType('url')}
                >
                  <LinkIcon className="w-4 h-4 mr-2" />
                  URL
                </Button>
              </div>

              {uploadType === 'file' ? (
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.docx,.txt,.md"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadMutation.isPending}
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {uploadMutation.isPending ? 'Uploading...' : 'Choose File'}
                  </Button>
                </div>
              ) : (
                <div className="flex gap-2">
                  <LuminarInput
                    placeholder="Enter URL..."
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleUrlUpload()}
                  />
                  <Button
                    onClick={handleUrlUpload}
                    disabled={!urlInput.trim() || uploadMutation.isPending}
                  >
                    {uploadMutation.isPending ? 'Adding...' : 'Add'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Folder Tree */}
        <div className="flex-1 overflow-y-auto">
          <FolderTree />
        </div>

        {/* Filter Toggle */}
        <div className="p-4 border-t border-gray-200">
          <Button
            variant="ghost"
            onClick={() => setShowFilters(!showFilters)}
            className="w-full justify-start"
          >
            <Filter className="w-4 h-4 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold">
                {isLoading ? 'Loading...' : `${documents.length} Documents`}
              </h2>

              {hasFilters && (
                <div className="flex items-center space-x-2">
                  <LuminarBadge variant="secondary">Filtered</LuminarBadge>
                  <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                    <X className="w-4 h-4 mr-1" />
                    Clear
                  </Button>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {/* Selection Controls */}
              {selectedCount > 0 && (
                <div className="flex items-center space-x-2 mr-4">
                  <span className="text-sm text-gray-600">{selectedCount} selected</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkDelete}
                    disabled={bulkDeleteMutation.isPending}
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Delete
                  </Button>
                  <Button variant="ghost" size="sm" onClick={deselectAll}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              )}

              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-md">
                <Button
                  variant={ui.viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={ui.viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>

              {/* Select All */}
              <Button variant="ghost" size="sm" onClick={handleSelectAll}>
                {ui.selectedDocumentIds.size === documents.length && documents.length > 0 ? (
                  <CheckSquare className="w-4 h-4" />
                ) : (
                  <Square className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="bg-white border-b border-gray-200 p-4">
            <DocumentSearch folders={folders} tags={tags} onSearch={handleSearch} />
          </div>
        )}

        {/* Document Grid/List */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <DocumentGridSkeleton count={ui.viewMode === 'grid' ? 8 : 4} />
          ) : documents.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {hasFilters ? 'No documents match your filters' : 'No documents yet'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {hasFilters
                    ? 'Try adjusting your search criteria or clearing filters'
                    : 'Start by uploading your first document'}
                </p>
                {!hasFilters && (
                  <Button onClick={() => setShowUpload(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Document
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div
              className={
                ui.viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                  : 'space-y-4'
              }
            >
              {documents.map((document) => (
                <DocumentCard
                  key={document.id}
                  document={document}
                  onSelect={() => toggleSelection(document.id)}
                  onDelete={() => deleteMutation.mutate(document.id)}
                  selected={ui.selectedDocumentIds.has(document.id)}
                  className={ui.viewMode === 'list' ? 'max-w-none' : ''}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export const Route = createLazyFileRoute('/library')({
  component: LibraryPage,
})
