import {
  Calendar,
  ChevronDown,
  Download,
  FileSpreadsheet,
  FileText,
  Image,
  Loader2,
} from 'lucide-react'
import { useState } from 'react'
import { Button } from '@luminar/shared-ui'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import { useToast } from '~/hooks/useToast'

interface AnalyticsExportProps {
  data?: any
  dateRange?: {
    start: string
    end: string
  }
}

export function AnalyticsExport({ data, dateRange }: AnalyticsExportProps) {
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const handleExport = async (format: 'csv' | 'pdf' | 'png' | 'json') => {
    setIsExporting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // In a real app, this would call an API endpoint
      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format,
          data,
          dateRange,
        }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `analytics-${new Date().toISOString().split('T')[0]}.${format}`
        a.click()
        window.URL.revokeObjectURL(url)

        toast({
          title: 'Export successful',
          description: `Analytics data exported as ${format.toUpperCase()}`,
          variant: 'success',
        })
      } else {
        throw new Error('Export failed')
      }
    } catch (error) {
      // Fallback: Generate and download mock data
      const mockData = generateMockExportData(format)
      downloadFile(mockData, `analytics-${new Date().toISOString().split('T')[0]}.${format}`)

      toast({
        title: 'Export completed',
        description: `Analytics data exported as ${format.toUpperCase()}`,
        variant: 'default',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const generateMockExportData = (format: string) => {
    const timestamp = new Date().toISOString()

    switch (format) {
      case 'csv':
        return `Date,Sessions,Documents,Queries
2024-01-01,45,20,15
2024-01-02,52,25,18
2024-01-03,38,15,12
2024-01-04,67,30,22
2024-01-05,71,35,25
2024-01-06,43,18,14
2024-01-07,39,16,11`

      case 'json':
        return JSON.stringify(
          {
            exportedAt: timestamp,
            dateRange: dateRange || { start: '2024-01-01', end: '2024-01-07' },
            summary: {
              totalSessions: 355,
              totalDocuments: 159,
              totalQueries: 117,
              averageSessionDuration: 12.5,
            },
            dailyStats: [
              { date: '2024-01-01', sessions: 45, documents: 20, queries: 15 },
              { date: '2024-01-02', sessions: 52, documents: 25, queries: 18 },
              { date: '2024-01-03', sessions: 38, documents: 15, queries: 12 },
              { date: '2024-01-04', sessions: 67, documents: 30, queries: 22 },
              { date: '2024-01-05', sessions: 71, documents: 35, queries: 25 },
              { date: '2024-01-06', sessions: 43, documents: 18, queries: 14 },
              { date: '2024-01-07', sessions: 39, documents: 16, queries: 11 },
            ],
          },
          null,
          2
        )

      case 'pdf':
        return `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>
endobj
4 0 obj
<< /Length 44 >>
stream
BT
/F1 12 Tf
100 700 Td
(Analytics Report - ${timestamp}) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000221 00000 n 
trailer
<< /Size 5 /Root 1 0 R >>
startxref
317
%%EOF`

      default:
        return `Analytics Export - ${timestamp}`
    }
  }

  const downloadFile = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" disabled={isExporting}>
          {isExporting ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          {isExporting ? 'Exporting...' : 'Export'}
          <ChevronDown className="w-4 h-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem onClick={() => handleExport('csv')}>
          <FileSpreadsheet className="w-4 h-4 mr-2" />
          Export as CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('json')}>
          <FileText className="w-4 h-4 mr-2" />
          Export as JSON
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => handleExport('pdf')}>
          <FileText className="w-4 h-4 mr-2" />
          Export as PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('png')}>
          <Image className="w-4 h-4 mr-2" />
          Export as PNG
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
