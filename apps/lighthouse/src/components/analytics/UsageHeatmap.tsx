import { CalendarDays, Clock, TrendingDown, TrendingUp } from 'lucide-react'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { Button, LuminarBadge } from '@luminar/shared-ui'

interface UsageData {
  date: string
  hour: number
  day: string
  value: number
  documents: number
  queries: number
}

interface UsageHeatmapProps {
  data: UsageData[]
}

export function UsageHeatmap({ data }: UsageHeatmapProps) {
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week')
  const [selectedDate, setSelectedDate] = useState<string | null>(null)

  // Create a matrix for the heatmap
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  const hours = Array.from({ length: 24 }, (_, i) => i)

  // Process data into a matrix
  const matrix = days.map((day) =>
    hours.map((hour) => {
      const dataPoint = data.find((d) => d.day === day && d.hour === hour)
      return dataPoint || { date: '', hour, day, value: 0, documents: 0, queries: 0 }
    })
  )

  // Calculate max value for color scaling
  const maxValue = Math.max(...data.map((d) => d.value))

  // Get color intensity based on value
  const getColorIntensity = (value: number) => {
    if (value === 0) return 'bg-gray-100'
    const intensity = Math.min(value / maxValue, 1)
    if (intensity <= 0.2) return 'bg-blue-100'
    if (intensity <= 0.4) return 'bg-blue-200'
    if (intensity <= 0.6) return 'bg-blue-300'
    if (intensity <= 0.8) return 'bg-blue-400'
    return 'bg-blue-500'
  }

  // Get text color based on background
  const getTextColor = (value: number) => {
    const intensity = Math.min(value / maxValue, 1)
    return intensity > 0.6 ? 'text-white' : 'text-gray-700'
  }

  // Calculate total stats
  const totalSessions = data.reduce((sum, d) => sum + d.value, 0)
  const totalDocuments = data.reduce((sum, d) => sum + d.documents, 0)
  const totalQueries = data.reduce((sum, d) => sum + d.queries, 0)

  // Find peak usage
  const peakUsage = data.reduce(
    (max, current) => (current.value > max.value ? current : max),
    data[0] || { value: 0, day: '', hour: 0 }
  )

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Usage Heatmap</h3>
          <p className="text-sm text-gray-600">Activity patterns throughout the week</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={viewMode} onValueChange={(value: 'week' | 'month') => setViewMode(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold">{totalSessions}</p>
              </div>
              <CalendarDays className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Documents Viewed</p>
                <p className="text-2xl font-bold">{totalDocuments}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">AI Queries</p>
                <p className="text-2xl font-bold">{totalQueries}</p>
              </div>
              <Clock className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Peak Usage Info */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Peak Usage</p>
              <p className="text-lg font-semibold">
                {peakUsage.day} at {peakUsage.hour}:00
              </p>
              <p className="text-sm text-gray-500">{peakUsage.value} sessions</p>
            </div>
            <LuminarBadge variant="outline" className="text-blue-600">
              Most Active
            </LuminarBadge>
          </div>
        </CardContent>
      </Card>

      {/* Heatmap */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Weekly Activity Pattern</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Hour labels */}
            <div className="flex">
              <div className="w-12"></div>
              <div className="flex-1 grid grid-cols-24 gap-1 text-xs text-gray-500">
                {hours.map((hour) => (
                  <div key={hour} className="text-center">
                    {hour % 6 === 0 ? hour : ''}
                  </div>
                ))}
              </div>
            </div>

            {/* Heatmap grid */}
            <div className="space-y-1">
              {matrix.map((dayData, dayIndex) => (
                <div key={days[dayIndex]} className="flex items-center">
                  <div className="w-12 text-xs text-gray-500 text-right pr-2">{days[dayIndex]}</div>
                  <div className="flex-1 grid grid-cols-24 gap-1">
                    {dayData.map((cellData, hourIndex) => (
                      <div
                        key={`${dayIndex}-${hourIndex}`}
                        className={`
                          h-4 rounded-sm cursor-pointer transition-all duration-200 hover:scale-110
                          ${getColorIntensity(cellData.value)}
                          ${getTextColor(cellData.value)}
                        `}
                        title={`${cellData.day} ${cellData.hour}:00 - ${cellData.value} sessions`}
                        onClick={() => setSelectedDate(cellData.date)}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Legend */}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Less active</span>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-gray-100 rounded-sm"></div>
                <div className="w-3 h-3 bg-blue-100 rounded-sm"></div>
                <div className="w-3 h-3 bg-blue-200 rounded-sm"></div>
                <div className="w-3 h-3 bg-blue-300 rounded-sm"></div>
                <div className="w-3 h-3 bg-blue-400 rounded-sm"></div>
                <div className="w-3 h-3 bg-blue-500 rounded-sm"></div>
              </div>
              <span>More active</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
