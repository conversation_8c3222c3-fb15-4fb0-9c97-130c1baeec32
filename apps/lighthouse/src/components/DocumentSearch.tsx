import { useState } from 'react'
import type { Folder, Tag } from '~/types'
import { Button, LuminarInput, LuminarBadge  } from '@luminar/shared-ui'

interface DocumentSearchProps {
  folders: Folder[]
  tags: Tag[]
  onSearch: (filters: { search: string; folderId?: string; selectedTags: string[] }) => void
}

export function DocumentSearch({ folders, tags, onSearch }: DocumentSearchProps) {
  const [search, setSearch] = useState('')
  const [selectedFolder, setSelectedFolder] = useState<string>('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const handleSearch = () => {
    onSearch({
      search,
      folderId: selectedFolder || undefined,
      selectedTags,
    })
  }

  const toggleTag = (tagName: string) => {
    setSelectedTags((prev) =>
      prev.includes(tagName) ? prev.filter((t) => t !== tagName) : [...prev, tagName]
    )
  }

  const clearFilters = () => {
    setSearch('')
    setSelectedFolder('')
    setSelectedTags([])
    onSearch({ search: '', selectedTags: [] })
  }

  return (
    <div className="space-y-4 bg-white p-4 rounded-lg border">
      <div className="flex gap-2">
        <LuminarInput
          placeholder="Search documents..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          className="flex-1"
        />
        <Button onClick={handleSearch}>Search</Button>
      </div>

      <div className="space-y-3">
        <div>
          <label className="text-sm font-medium mb-2 block">Folder</label>
          <select
            value={selectedFolder}
            onChange={(e) => setSelectedFolder(e.target.value)}
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Folders</option>
            {folders.map((folder) => (
              <option key={folder.id} value={folder.id}>
                {folder.name} ({folder.documentCount})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Tags</label>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <LuminarBadge
                key={tag.id}
                variant={selectedTags.includes(tag.name) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => toggleTag(tag.name)}
              >
                {tag.name} ({tag.documentCount})
              </LuminarBadge>
            ))}
          </div>
        </div>
      </div>

      {(search || selectedFolder || selectedTags.length > 0) && (
        <div className="flex justify-end">
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}
