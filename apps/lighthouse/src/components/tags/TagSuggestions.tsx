import { useEffect, useState } from 'react'
import { LuminarBadge } from '@luminar/shared-ui'

interface TagSuggestionsProps {
  content: string
  onSelect: (tag: string) => void
}

export function TagSuggestions({ content, onSelect }: TagSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<string[]>([])

  useEffect(() => {
    if (content) {
      fetch('/api/tags/auto-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content }),
      })
        .then((res) => res.json())
        .then(setSuggestions)
    }
  }, [content])

  return (
    <div className="flex flex-wrap gap-2">
      {suggestions.map((tag) => (
        <LuminarBadge key={tag} variant="outline" className="cursor-pointer" onClick={() => onSelect(tag)}>
          {tag}
        </LuminarBadge>
      ))}
    </div>
  )
}
