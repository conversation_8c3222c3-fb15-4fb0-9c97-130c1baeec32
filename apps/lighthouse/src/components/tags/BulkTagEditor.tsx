import { useMemo, useState } from 'react'
import { useSelectedDocuments } from '~/hooks/useStoreSelectors'
import { useLighthouseStore } from '~/store/lighthousestore'
import { Button, LuminarBadge } from '@luminar/shared-ui'

export function BulkTagEditor() {
  const selectedDocuments = useSelectedDocuments()
  const tagsMap = useLighthouseStore((state) => state.tags)
  const allTags = useMemo(() => Array.from(tagsMap.values()), [tagsMap])
  const addTagsToDocuments = useLighthouseStore((state) => state.addTagsToDocuments)
  const removeTagsFromDocuments = useLighthouseStore((state) => state.removeTagsFromDocuments)
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const handleAddTags = () => {
    addTagsToDocuments(
      selectedDocuments.map((d) => d.id),
      selectedTags
    )
    setSelectedTags([])
  }

  const handleRemoveTags = () => {
    removeTagsFromDocuments(
      selectedDocuments.map((d) => d.id),
      selectedTags
    )
    setSelectedTags([])
  }

  return (
    <div>
      <h3 className="text-lg font-semibold mb-2">Bulk Tag Editor</h3>
      <div className="flex flex-wrap gap-2 mb-4">
        {allTags.map((tag) => (
          <LuminarBadge
            key={tag.id}
            variant={selectedTags.includes(tag.name) ? 'default' : 'outline'}
            onClick={() =>
              setSelectedTags((prev) =>
                prev.includes(tag.name) ? prev.filter((t) => t !== tag.name) : [...prev, tag.name]
              )
            }
          >
            {tag.name}
          </LuminarBadge>
        ))}
      </div>
      <div className="flex gap-2">
        <Button onClick={handleAddTags} disabled={selectedTags.length === 0}>
          Add Tags
        </Button>
        <Button
          onClick={handleRemoveTags}
          variant="destructive"
          disabled={selectedTags.length === 0}
        >
          Remove Tags
        </Button>
      </div>
    </div>
  )
}
