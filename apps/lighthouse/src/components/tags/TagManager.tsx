import { X } from 'lucide-react'
import { useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { useLighthouseStore } from '~/store/lighthousestore'
import { Button, LuminarInput, LuminarBadge  } from '@luminar/shared-ui'

export function TagManager() {
  const tagsMap = useLighthouseStore((state) => state.tags)
  const tags = useMemo(() => Array.from(tagsMap.values()), [tagsMap])
  const addTag = useLighthouseStore((state) => state.addTag)
  const deleteTag = useLighthouseStore((state) => state.deleteTag)
  const [newTagName, setNewTagName] = useState('')

  const handleAddTag = () => {
    if (newTagName && !tags.find((t) => t.name === newTagName)) {
      addTag({
        id: `tag-${Date.now()}`,
        name: newTagName,
        color: '#888',
        documentCount: 0,
      })
      setNewTagName('')
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tag Management</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-4">
          <LuminarInput
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            placeholder="New tag name..."
          />
          <Button onClick={handleAddTag}>Add Tag</Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <LuminarBadge key={tag.id} variant="outline" className="flex items-center gap-1">
              {tag.name} ({tag.documentCount})
              <X className="w-3 h-3 cursor-pointer" onClick={() => deleteTag(tag.id)} />
            </LuminarBadge>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
