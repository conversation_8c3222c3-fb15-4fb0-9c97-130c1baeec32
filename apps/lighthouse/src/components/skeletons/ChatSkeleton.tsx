import { <PERSON>, CardContent, CardHeader } from '~/components/ui/card'
import { Skeleton } from '@luminar/shared-ui'

export function ChatMessageSkeleton({ isUser = false }: { isUser?: boolean }) {
  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <Skeleton className="w-5 h-5 rounded-full" />
        </div>
      )}

      <div className={`max-w-[70%] ${isUser ? 'order-2' : 'order-1'}`}>
        <div className={`p-4 rounded-2xl ${isUser ? 'bg-blue-600' : 'bg-gray-100'}`}>
          <div className="space-y-2">
            <Skeleton className={`h-4 w-full ${isUser ? 'bg-blue-500' : ''}`} />
            <Skeleton className={`h-4 w-3/4 ${isUser ? 'bg-blue-500' : ''}`} />
            <Skeleton className={`h-4 w-1/2 ${isUser ? 'bg-blue-500' : ''}`} />
          </div>
        </div>

        <div className="flex items-center justify-between mt-2">
          <Skeleton className="h-3 w-16" />
          {!isUser && (
            <div className="flex items-center gap-1">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-6" />
            </div>
          )}
        </div>
      </div>

      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
          <Skeleton className="w-5 h-5 rounded-full" />
        </div>
      )}
    </div>
  )
}

export function ChatInterfaceSkeleton() {
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="w-5 h-5 rounded-full" />
            <Skeleton className="h-6 w-48" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-16 rounded-full" />
            <Skeleton className="h-5 w-24 rounded-full" />
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <ChatMessageSkeleton isUser={false} />
        <ChatMessageSkeleton isUser={true} />
        <ChatMessageSkeleton isUser={false} />
        <ChatMessageSkeleton isUser={true} />

        {/* Sources skeleton */}
        <Card className="mt-4">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Skeleton className="w-4 h-4" />
              <Skeleton className="h-5 w-24" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="p-3 bg-gray-50 rounded-lg">
              <Skeleton className="h-4 w-32 mb-2" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-3/4" />
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <Skeleton className="h-4 w-28 mb-2" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Input */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <Skeleton className="flex-1 h-10" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>
    </div>
  )
}
