import { Button, LuminarInput } from '@luminar/shared-ui'
'use client'

import { useQuery } from '@tanstack/react-query'
import {
  ChevronRight,
  Edit,
  Folder as FolderIcon,
  MoreHorizontal,
  Plus,
  Trash2,
} from 'lucide-react'
import type React from 'react'
import { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import { useFolderCreate, useFolderDelete, useFolderUpdate } from '~/hooks/useFolderQuery'
import { useLighthouseStore } from '~/store/lighthousestore'
import type { Folder } from '~/types'

// Helper to build the tree
const buildTree = (folders: Folder[]): Folder[] => {
  const folderMap = new Map(folders.map((f) => [f.id, { ...f, children: [] }]))
  const tree: Folder[] = []

  folders.forEach((folder) => {
    if (folder.parentId) {
      const parent = folderMap.get(folder.parentId)
      if (parent) {
        parent.children.push(folderMap.get(folder.id)!)
      }
    } else {
      tree.push(folderMap.get(folder.id)!)
    }
  })

  return tree
}

// Recursive component to render a folder node
function FolderNode({
  folder,
  level,
}: {
  folder: Folder & { children?: Folder[] }
  level: number
}) {
  const [isOpen, setIsOpen] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [editedName, setEditedName] = useState(folder.name)

  const setFilter = useLighthouseStore((state) => state.setFilter)
  const selectedFolderId = useLighthouseStore((state) => state.filters.folderId)
  const updateFolder = useFolderUpdate()
  const deleteFolder = useFolderDelete()

  const handleFolderClick = () => setFilter('folderId', folder.id)
  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsOpen(!isOpen)
  }

  const handleRename = async () => {
    if (editedName.trim() && editedName !== folder.name) {
      await updateFolder.mutateAsync({ id: folder.id, name: editedName.trim() })
    }
    setIsEditing(false)
  }

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${folder.name}"?`)) {
      await deleteFolder.mutateAsync(folder.id)
    }
  }

  const hasChildren = folder.children && folder.children.length > 0
  const isSelected = selectedFolderId === folder.id

  return (
    <li>
      <div
        className={`group flex items-center p-2 rounded-md cursor-pointer ${
          isSelected ? 'bg-blue-100 dark:bg-blue-900' : 'hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
        style={{ paddingLeft: `${level * 1.5}rem` }}
        onClick={handleFolderClick}
      >
        {hasChildren && (
          <ChevronRight
            className={`w-4 h-4 mr-2 transform transition-transform ${isOpen ? 'rotate-90' : ''}`}
            onClick={handleToggle}
          />
        )}
        <FolderIcon
          className="w-4 h-4 mr-2 flex-shrink-0"
          style={{ marginLeft: hasChildren ? '0' : '1rem' }}
        />
        {isEditing ? (
          <LuminarInput
            value={editedName}
            onChange={(e) => setEditedName(e.target.value)}
            onBlur={handleRename}
            onKeyDown={(e) => e.key === 'Enter' && handleRename()}
            className="h-7"
            autoFocus
          />
        ) : (
          <span className="flex-grow truncate">{folder.name}</span>
        )}
        <span className="text-xs text-gray-500 mr-2">{folder.documentCount}</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 opacity-0 group-hover:opacity-100"
            >
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setIsEditing(true)}>
              <Edit className="w-4 h-4 mr-2" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete} className="text-red-500">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {hasChildren && isOpen && (
        <ul>
          {folder.children.map((child) => (
            <FolderNode key={child.id} folder={child} level={level + 1} />
          ))}
        </ul>
      )}
    </li>
  )
}

// Main FolderTree component
export function FolderTree() {
  const {
    data: folders = [],
    isLoading,
    isError,
  } = useQuery<Folder[]>({
    queryKey: ['folders'],
    queryFn: () => fetch('/api/folders').then((res) => res.json()),
  })
  const createFolder = useFolderCreate()
  const [newFolderName, setNewFolderName] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return
    await createFolder.mutateAsync({ name: newFolderName.trim() })
    setNewFolderName('')
    setIsDialogOpen(false)
  }

  if (isLoading) return <div>Loading folders...</div>
  if (isError) return <div>Error loading folders.</div>

  const folderTree = buildTree(folders)

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">Folders</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Folder</DialogTitle>
            </DialogHeader>
            <LuminarInput
              placeholder="Folder name"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleCreateFolder()}
            />
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateFolder}>Create</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      <ul>
        {folderTree.map((folder) => (
          <FolderNode key={folder.id} folder={folder} level={0} />
        ))}
      </ul>
    </div>
  )
}
