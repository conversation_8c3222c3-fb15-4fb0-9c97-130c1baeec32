import { motion } from 'framer-motion'
import { AlertTriangle, ArrowLeft, Bug, Home, RefreshCw } from 'lucide-react'
import { useState } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { Button } from '@luminar/shared-ui'

interface ErrorFallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

const ErrorFallback = ({ error, resetErrorBoundary }: ErrorFallbackProps) => {
  const [showDetails, setShowDetails] = useState(false)
  const [reportSent, setReportSent] = useState(false)

  const reportError = async () => {
    try {
      // In production, this would send to error reporting service
      console.error('Error reported:', {
        message: error.message,
        stack: error.stack,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        url: window.location.href,
      })

      setReportSent(true)

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  const goHome = () => {
    window.location.href = '/'
  }

  const goBack = () => {
    window.history.back()
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4"
    >
      <div className="max-w-lg w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="text-center mb-6">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full mb-4"
          >
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </motion.div>

          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Oops! Something went wrong
          </h2>

          <p className="text-gray-600 dark:text-gray-400 mb-6">
            We're sorry for the inconvenience. The application encountered an unexpected error.
          </p>
        </div>

        <div className="space-y-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={resetErrorBoundary}
              className="flex-1 flex items-center justify-center gap-2"
              variant="default"
            >
              <RefreshCw className="w-4 h-4" />
              Try Again
            </Button>

            <Button
              onClick={goBack}
              className="flex-1 flex items-center justify-center gap-2"
              variant="outline"
            >
              <ArrowLeft className="w-4 h-4" />
              Go Back
            </Button>
          </div>

          <Button
            onClick={goHome}
            className="w-full flex items-center justify-center gap-2"
            variant="outline"
          >
            <Home className="w-4 h-4" />
            Go to Homepage
          </Button>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Help us improve by reporting this error
            </span>

            <Button
              onClick={reportError}
              disabled={reportSent}
              className="flex items-center gap-2"
              variant="outline"
              size="sm"
            >
              <Bug className="w-4 h-4" />
              {reportSent ? 'Reported' : 'Report Issue'}
            </Button>
          </div>

          {reportSent && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-green-600 dark:text-green-400 mb-4"
            >
              ✓ Thank you! The error has been reported to our team.
            </motion.div>
          )}

          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            {showDetails ? 'Hide' : 'Show'} Technical Details
          </button>

          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 p-4 bg-gray-100 dark:bg-gray-900 rounded-lg overflow-hidden"
            >
              <div className="text-sm">
                <div className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Error Details:
                </div>
                <div className="text-red-600 dark:text-red-400 mb-2">{error.message}</div>
                <details className="text-xs text-gray-600 dark:text-gray-400">
                  <summary className="cursor-pointer hover:text-gray-900 dark:hover:text-gray-100">
                    Stack Trace
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-200 dark:bg-gray-800 rounded text-xs overflow-auto max-h-32">
                    {error.stack}
                  </pre>
                </details>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

interface LighthouseErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

export const LighthouseErrorBoundary = ({
  children,
  fallback,
  onError,
}: LighthouseErrorBoundaryProps) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Lighthouse Error Boundary caught an error:', error)
      console.error('Error Info:', errorInfo)
    }

    // Report to error monitoring service
    try {
      // In production, integrate with Sentry, LogRocket, etc.
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'exception', {
          description: error.message,
          fatal: false,
        })
      }
    } catch (reportingError) {
      console.error('Failed to report error to monitoring service:', reportingError)
    }

    // Call custom error handler
    onError?.(error, errorInfo)
  }

  return (
    <ErrorBoundary
      FallbackComponent={fallback || ErrorFallback}
      onError={handleError}
      onReset={() => {
        // Clear any cached data that might be causing issues
        if (typeof window !== 'undefined') {
          // Clear localStorage if needed
          try {
            localStorage.removeItem('lighthouse-store')
          } catch (e) {
            console.warn('Failed to clear localStorage:', e)
          }
        }
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

// Component-specific error boundary
export const ComponentErrorBoundary = ({
  children,
  componentName,
  fallback,
}: {
  children: React.ReactNode
  componentName: string
  fallback?: React.ReactNode
}) => {
  const ComponentErrorFallback = ({ error, resetErrorBoundary }: ErrorFallbackProps) => {
    return (
      <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
          <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
            {componentName} Error
          </h3>
        </div>

        <p className="text-sm text-red-700 dark:text-red-300 mb-3">
          This component failed to render. You can try refreshing or continue using other parts of
          the application.
        </p>

        <div className="flex gap-2">
          <Button
            onClick={resetErrorBoundary}
            size="sm"
            variant="outline"
            className="text-red-700 dark:text-red-300 border-red-300 dark:border-red-700"
          >
            Retry
          </Button>

          <Button
            onClick={() => window.location.reload()}
            size="sm"
            variant="outline"
            className="text-red-700 dark:text-red-300 border-red-300 dark:border-red-700"
          >
            Refresh Page
          </Button>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="mt-3 text-xs">
            <summary className="cursor-pointer text-red-600 dark:text-red-400">
              Error Details (Development)
            </summary>
            <pre className="mt-2 p-2 bg-red-100 dark:bg-red-900/40 rounded text-red-800 dark:text-red-200 overflow-auto">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    )
  }

  return (
    <ErrorBoundary
      FallbackComponent={fallback ? () => <>{fallback}</> : ComponentErrorFallback}
      onError={(error, errorInfo) => {
        console.error(`${componentName} component error:`, error)
        console.error('Error Info:', errorInfo)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

// Hook for manual error reporting
export const useErrorReporting = () => {
  const reportError = (error: Error, context?: Record<string, any>) => {
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      }

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Manual error report:', errorReport)
      }

      // Send to error monitoring service
      // This would integrate with your error monitoring service
      console.warn('Error reported manually:', errorReport)
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  return { reportError }
}
