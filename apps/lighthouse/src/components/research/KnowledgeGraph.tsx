import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '@luminar/shared-ui'

interface KnowledgeGraphProps {
  documentIds: string[]
}

export function KnowledgeGraphComponent({ documentIds }: KnowledgeGraphProps) {
  const [isClient, setIsClient] = useState(false)
  const [ClientComponent, setClientComponent] = useState<any>(null)

  // Only render on client to avoid SSR issues
  useEffect(() => {
    setIsClient(true)

    // Dynamically import the client component
    import('./KnowledgeGraphClient').then((module) => {
      setClientComponent(() => module.KnowledgeGraphClient)
    })
  }, [])

  if (!isClient || !ClientComponent) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Knowledge Graph</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">Loading knowledge graph...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return <ClientComponent documentIds={documentIds} />
}
