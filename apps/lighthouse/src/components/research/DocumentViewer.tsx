import { Button, LuminarBadge } from '@luminar/shared-ui'
'use client'

import { useEffect, useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import type { Document } from '~/types'

interface DocumentViewerProps {
  document: Document | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function DocumentViewer({ document, open, onOpenChange }: DocumentViewerProps) {
  const [loading, setLoading] = useState(false)
  const [content, setContent] = useState<string>('')

  const fetchContent = async (docId: string) => {
    if (!docId) return
    setLoading(true)
    try {
      const response = await fetch(`/api/documents/${docId}`)
      if (!response.ok) throw new Error('Failed to fetch document')
      const data = await response.json()
      setContent(data.content || 'No content available')
    } catch (error) {
      console.error('Failed to fetch document content:', error)
      setContent('Failed to load document content')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Reset content when the dialog is closed or the document changes
    if (!open || !document) {
      setContent('')
    }
  }, [open, document])

  if (!document) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{document.name}</DialogTitle>
          <DialogDescription>
            Type: {document.type ? document.type.toUpperCase() : 'N/A'}
            {' • '}
            Size: {(document.size / 1024 / 1024).toFixed(2)} MB
            {' • '}
            Created: {new Date(document.createdAt).toLocaleDateString()}
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-wrap gap-1 mt-2">
          {(document.tags || []).map((tag) => (
            <LuminarBadge key={tag} variant="secondary">
              {tag}
            </LuminarBadge>
          ))}
        </div>

        <div className="flex-1 overflow-auto mt-4 p-4 bg-gray-50 rounded-lg">
          {loading ? (
            <p className="text-gray-500">Loading document content...</p>
          ) : content ? (
            <pre className="whitespace-pre-wrap font-sans text-sm">{content}</pre>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">Document preview not available</p>
              <Button onClick={() => fetchContent(document.id)}>Load Content</Button>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
