import { Button, LuminarInput, LuminarBadge  } from '@luminar/shared-ui'
import { Checkbox, Label, Separator } from '@luminar/shared-ui'
import { Tabs } from '@luminar/shared-ui'
'use client'

import { Download, Filter, Maximize2, RefreshCw, Search, ZoomIn, ZoomOut } from 'lucide-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import type { GraphEdge, GraphNode, KnowledgeGraph } from '~/types/graph'

interface GraphControls {
  layout: 'force' | 'radial' | 'hierarchical'
  nodeTypes: Set<string>
  edgeTypes: Set<string>
  minWeight: number
  maxWeight: number
  searchTerm: string
  selectedNodes: Set<string>
  showLabels: boolean
  nodeSize: number
  linkDistance: number
}

interface KnowledgeGraphProps {
  documentIds: string[]
  onNodeSelect?: (node: GraphNode) => void
  onEdgeSelect?: (edge: GraphEdge) => void
}

export const KnowledgeGraphClient = ({
  documentIds,
  onNodeSelect,
  onEdgeSelect,
}: KnowledgeGraphProps) => {
  const [graph, setGraph] = useState<KnowledgeGraph | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [ForceGraph2D, setForceGraph2D] = useState<any>(null)
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null)
  const [selectedEdge, setSelectedEdge] = useState<GraphEdge | null>(null)
  const [controls, setControls] = useState<GraphControls>({
    layout: 'force',
    nodeTypes: new Set(['document', 'concept', 'person', 'organization']),
    edgeTypes: new Set(['mentions', 'references', 'similar', 'contains']),
    minWeight: 0,
    maxWeight: 1,
    searchTerm: '',
    selectedNodes: new Set(),
    showLabels: true,
    nodeSize: 1,
    linkDistance: 30,
  })
  const [highlightNodes, setHighlightNodes] = useState<Set<string>>(new Set())
  const [highlightLinks, setHighlightLinks] = useState<Set<string>>(new Set())
  const graphRef = useRef<any>(null)

  // Dynamically import the force graph component only on client
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('react-force-graph-2d').then((module) => {
        setForceGraph2D(() => module.default)
      })
    }
  }, [])

  const generateGraph = async () => {
    if (documentIds.length === 0) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/ai/graph', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentIds }),
      })
      const graphData = await response.json()
      setGraph(graphData)
    } catch (error) {
      console.error('Failed to generate knowledge graph:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleNodeClick = useCallback(
    (node: GraphNode) => {
      setSelectedNode(node)
      setSelectedEdge(null)

      // Highlight connected nodes and edges
      if (graph) {
        const connectedEdges = graph.edges.filter(
          (edge) => edge.source === node.id || edge.target === node.id
        )
        const connectedNodes = new Set(connectedEdges.flatMap((edge) => [edge.source, edge.target]))

        setHighlightNodes(connectedNodes)
        setHighlightLinks(new Set(connectedEdges.map((edge) => edge.id)))
      }

      onNodeSelect?.(node)
    },
    [graph, onNodeSelect]
  )

  const handleLinkClick = useCallback(
    (edge: GraphEdge) => {
      setSelectedEdge(edge)
      setSelectedNode(null)

      // Highlight connected nodes
      setHighlightNodes(new Set([edge.source, edge.target]))
      setHighlightLinks(new Set([edge.id]))

      onEdgeSelect?.(edge)
    },
    [onEdgeSelect]
  )

  const handleNodeHover = useCallback(
    (node: GraphNode | null) => {
      if (!node) {
        setHighlightNodes(new Set())
        setHighlightLinks(new Set())
        return
      }

      if (graph) {
        const connectedEdges = graph.edges.filter(
          (edge) => edge.source === node.id || edge.target === node.id
        )
        const connectedNodes = new Set(connectedEdges.flatMap((edge) => [edge.source, edge.target]))

        setHighlightNodes(connectedNodes)
        setHighlightLinks(new Set(connectedEdges.map((edge) => edge.id)))
      }
    },
    [graph]
  )

  const clearSelection = () => {
    setSelectedNode(null)
    setSelectedEdge(null)
    setHighlightNodes(new Set())
    setHighlightLinks(new Set())
  }

  const updateControls = (updates: Partial<GraphControls>) => {
    setControls((prev) => ({ ...prev, ...updates }))
  }

  // Filter graph data based on controls
  const filteredGraph = graph
    ? {
        nodes: graph.nodes.filter((node) => {
          const matchesType = controls.nodeTypes.has(node.type)
          const matchesSearch =
            !controls.searchTerm ||
            node.label.toLowerCase().includes(controls.searchTerm.toLowerCase())
          return matchesType && matchesSearch
        }),
        edges: graph.edges.filter((edge) => {
          const matchesType = controls.edgeTypes.has(edge.type)
          const matchesWeight =
            edge.weight >= controls.minWeight && edge.weight <= controls.maxWeight
          const sourceExists = graph.nodes.some(
            (node) => node.id === edge.source && controls.nodeTypes.has(node.type)
          )
          const targetExists = graph.nodes.some(
            (node) => node.id === edge.target && controls.nodeTypes.has(node.type)
          )
          return matchesType && matchesWeight && sourceExists && targetExists
        }),
      }
    : null

  const exportGraph = () => {
    if (!graph) return

    const dataStr = JSON.stringify(graph, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr)

    const exportFileDefaultName = `knowledge-graph-${new Date().toISOString().split('T')[0]}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const zoomToFit = () => {
    if (graphRef.current) {
      graphRef.current.zoomToFit(400)
    }
  }

  const centerGraph = () => {
    if (graphRef.current) {
      graphRef.current.centerAt(0, 0, 1000)
    }
  }

  if (!ForceGraph2D) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Knowledge Graph</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">Loading graph visualization...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
      {/* Main Graph Visualization */}
      <Card className="lg:col-span-3">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Knowledge Graph</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={zoomToFit} disabled={!graph}>
                <Maximize2 className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={centerGraph} disabled={!graph}>
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={exportGraph} disabled={!graph}>
                <Download className="h-4 w-4" />
              </Button>
              <Button onClick={generateGraph} disabled={isLoading || documentIds.length === 0}>
                {isLoading ? 'Generating...' : 'Generate Graph'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {documentIds.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              Select documents to generate a knowledge graph
            </div>
          ) : !graph ? (
            <div className="text-center text-gray-500 py-8">
              Click "Generate Graph" to visualize document relationships
            </div>
          ) : (
            <div className="w-full h-96 border rounded-lg overflow-hidden relative">
              <ForceGraph2D
                ref={graphRef}
                graphData={filteredGraph}
                nodeLabel={(node: GraphNode) => (controls.showLabels ? node.label : '')}
                nodeColor={(node: GraphNode) => {
                  if (highlightNodes.has(node.id)) {
                    return node.color
                  }
                  return highlightNodes.size > 0 ? '#E5E7EB' : node.color
                }}
                nodeVal={(node: GraphNode) => node.size * controls.nodeSize}
                linkSource="source"
                linkTarget="target"
                linkColor={(link: GraphEdge) => {
                  if (highlightLinks.has(link.id)) {
                    return '#3B82F6'
                  }
                  return highlightLinks.size > 0 ? '#E5E7EB' : '#94A3B8'
                }}
                linkWidth={(link: GraphEdge) => Math.sqrt(link.weight * 5)}
                onNodeClick={handleNodeClick}
                onLinkClick={handleLinkClick}
                onNodeHover={handleNodeHover}
                onLinkHover={null}
                backgroundColor="#ffffff"
                width={undefined}
                height={384}
                d3AlphaDecay={0.02}
                d3VelocityDecay={0.3}
                linkDistance={controls.linkDistance}
                cooldownTime={3000}
              />

              {/* Clear Selection Button */}
              {(selectedNode || selectedEdge) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={clearSelection}
                >
                  Clear Selection
                </Button>
              )}
            </div>
          )}

          {graph && (
            <div className="mt-4 text-sm text-gray-600">
              <div className="flex items-center justify-between">
                <div>
                  <p>
                    Showing {filteredGraph?.nodes.length || 0} of {graph.nodes.length} nodes,
                    {filteredGraph?.edges.length || 0} of {graph.edges.length} edges
                  </p>
                  <p className="text-xs mt-1">
                    Generated from {graph.metadata.documentCount} documents
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {selectedNode && (
                    <LuminarBadge variant="secondary">Selected: {selectedNode.label}</LuminarBadge>
                  )}
                  {selectedEdge && (
                    <LuminarBadge variant="secondary">Selected: {selectedEdge.type} relationship</LuminarBadge>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Controls Panel */}
      <Card className="h-fit">
        <CardHeader>
          <CardTitle className="text-lg">Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="filters" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="filters">Filters</TabsTrigger>
              <TabsTrigger value="display">Display</TabsTrigger>
            </TabsList>

            <TabsContent value="filters" className="space-y-4">
              {/* Search */}
              <div className="space-y-2">
                <Label htmlFor="search">Search Nodes</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <LuminarInput
                    id="search"
                    placeholder="Search concepts..."
                    value={controls.searchTerm}
                    onChange={(e) => updateControls({ searchTerm: e.target.value })}
                    className="pl-8"
                  />
                </div>
              </div>

              {/* Node Types */}
              <div className="space-y-2">
                <Label>Node Types</Label>
                <div className="space-y-2">
                  {['document', 'concept', 'person', 'organization'].map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={type}
                        checked={controls.nodeTypes.has(type)}
                        onCheckedChange={(checked) => {
                          const newTypes = new Set(controls.nodeTypes)
                          if (checked) {
                            newTypes.add(type)
                          } else {
                            newTypes.delete(type)
                          }
                          updateControls({ nodeTypes: newTypes })
                        }}
                      />
                      <Label htmlFor={type} className="text-sm capitalize">
                        {type}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Edge Types */}
              <div className="space-y-2">
                <Label>Edge Types</Label>
                <div className="space-y-2">
                  {['mentions', 'references', 'similar', 'contains'].map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`edge-${type}`}
                        checked={controls.edgeTypes.has(type)}
                        onCheckedChange={(checked) => {
                          const newTypes = new Set(controls.edgeTypes)
                          if (checked) {
                            newTypes.add(type)
                          } else {
                            newTypes.delete(type)
                          }
                          updateControls({ edgeTypes: newTypes })
                        }}
                      />
                      <Label htmlFor={`edge-${type}`} className="text-sm capitalize">
                        {type}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Weight Filter */}
              <div className="space-y-2">
                <Label>Edge Weight Range</Label>
                <div className="flex items-center space-x-2">
                  <LuminarInput
                    type="number"
                    min="0"
                    max="1"
                    step="0.1"
                    value={controls.minWeight}
                    onChange={(e) => updateControls({ minWeight: Number(e.target.value) })}
                    className="w-20"
                  />
                  <span className="text-sm text-gray-500">to</span>
                  <LuminarInput
                    type="number"
                    min="0"
                    max="1"
                    step="0.1"
                    value={controls.maxWeight}
                    onChange={(e) => updateControls({ maxWeight: Number(e.target.value) })}
                    className="w-20"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="display" className="space-y-4">
              {/* Layout */}
              <div className="space-y-2">
                <Label>Layout</Label>
                <Select
                  value={controls.layout}
                  onValueChange={(value: 'force' | 'radial' | 'hierarchical') =>
                    updateControls({ layout: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="force">Force Layout</SelectItem>
                    <SelectItem value="radial">Radial Layout</SelectItem>
                    <SelectItem value="hierarchical">Hierarchical Layout</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Show Labels */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-labels"
                  checked={controls.showLabels}
                  onCheckedChange={(checked) => updateControls({ showLabels: !!checked })}
                />
                <Label htmlFor="show-labels" className="text-sm">
                  Show Labels
                </Label>
              </div>

              {/* Node Size */}
              <div className="space-y-2">
                <Label>Node Size: {controls.nodeSize.toFixed(1)}</Label>
                <input
                  type="range"
                  min="0.5"
                  max="3"
                  step="0.1"
                  value={controls.nodeSize}
                  onChange={(e) => updateControls({ nodeSize: Number(e.target.value) })}
                  className="w-full"
                />
              </div>

              {/* Link Distance */}
              <div className="space-y-2">
                <Label>Link Distance: {controls.linkDistance}</Label>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="5"
                  value={controls.linkDistance}
                  onChange={(e) => updateControls({ linkDistance: Number(e.target.value) })}
                  className="w-full"
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Selection Details */}
          {(selectedNode || selectedEdge) && (
            <>
              <Separator className="my-4" />
              <div className="space-y-2">
                <Label>Selection Details</Label>
                {selectedNode && (
                  <div className="text-sm space-y-1">
                    <p>
                      <strong>Node:</strong> {selectedNode.label}
                    </p>
                    <p>
                      <strong>Type:</strong> {selectedNode.type}
                    </p>
                    <p>
                      <strong>Size:</strong> {selectedNode.size}
                    </p>
                    {selectedNode.metadata.documentId && (
                      <p>
                        <strong>Document:</strong> {selectedNode.metadata.documentId}
                      </p>
                    )}
                  </div>
                )}
                {selectedEdge && (
                  <div className="text-sm space-y-1">
                    <p>
                      <strong>Edge:</strong> {selectedEdge.type}
                    </p>
                    <p>
                      <strong>Weight:</strong> {selectedEdge.weight.toFixed(2)}
                    </p>
                    <p>
                      <strong>Source:</strong> {selectedEdge.source}
                    </p>
                    <p>
                      <strong>Target:</strong> {selectedEdge.target}
                    </p>
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
