import { Button, LuminarInput, LuminarBadge  } from '@luminar/shared-ui'
'use client'

import { AnimatePresence, motion } from 'framer-motion'
import {
  AlertCircle,
  Bot,
  Copy,
  FileText,
  RefreshCw,
  Send,
  Sparkles,
  ThumbsDown,
  Thum<PERSON>Up,
  User,
  Zap,
} from 'lucide-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import type { AIQueryResponse } from '~/types/ai'
import { useAIQuery } from '../../hooks/useLighthouseQuery'
import { useLighthouseStore } from '../../store/lighthousestore'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: string
  response?: AIQueryResponse
  isLoading?: boolean
  error?: string
}

interface ChatInterfaceProps {
  documentIds: string[]
  className?: string
}

export const ChatInterface = ({ documentIds, className = '' }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const isProcessing = useLighthouseStore((state) => state.ai.isProcessing)
  const currentModel = useLighthouseStore((state) => state.ai.currentModel)
  const aiQuery = useAIQuery()

  // Auto-scroll to bottom
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // Handle message submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isProcessing) return

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputValue,
      timestamp: new Date().toISOString(),
    }

    const loadingMessage: Message = {
      id: `assistant-${Date.now()}`,
      type: 'assistant',
      content: 'Thinking...',
      timestamp: new Date().toISOString(),
      isLoading: true,
    }

    setMessages((prev) => [...prev, userMessage, loadingMessage])
    setInputValue('')
    setIsTyping(true)

    try {
      const response = await aiQuery.mutateAsync({
        query: inputValue,
        documentIds,
        model: currentModel,
        includeContext: true,
      })

      // Update the loading message with actual response
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessage.id
            ? {
                ...msg,
                content: response.answer,
                response,
                isLoading: false,
              }
            : msg
        )
      )
    } catch (error) {
      // Update with error message
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessage.id
            ? {
                ...msg,
                content: 'Sorry, I encountered an error. Please try again.',
                isLoading: false,
                error: error instanceof Error ? error.message : 'Unknown error',
              }
            : msg
        )
      )
    } finally {
      setIsTyping(false)
    }
  }

  // Copy message to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // TODO: Show toast notification
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  // Regenerate response
  const regenerateResponse = async (messageId: string) => {
    const message = messages.find((msg) => msg.id === messageId)
    if (!message) return

    // Find the user message that prompted this response
    const messageIndex = messages.findIndex((msg) => msg.id === messageId)
    const userMessage = messages[messageIndex - 1]

    if (userMessage?.type === 'user') {
      // Mark as loading
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, isLoading: true, content: 'Regenerating...' } : msg
        )
      )

      try {
        const response = await aiQuery.mutateAsync({
          query: userMessage.content,
          documentIds,
          model: currentModel,
          includeContext: true,
        })

        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === messageId
              ? {
                  ...msg,
                  content: response.answer,
                  response,
                  isLoading: false,
                  error: undefined,
                }
              : msg
          )
        )
      } catch (error) {
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === messageId
              ? {
                  ...msg,
                  content: 'Failed to regenerate response. Please try again.',
                  isLoading: false,
                  error: error instanceof Error ? error.message : 'Unknown error',
                }
              : msg
          )
        )
      }
    }
  }

  // Message feedback
  const handleFeedback = (messageId: string, feedback: 'positive' | 'negative') => {
    // TODO: Send feedback to analytics service
    console.log('Feedback for message', messageId, ':', feedback)
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              AI Research Assistant
            </h2>
          </div>

          <div className="flex items-center gap-2">
            <LuminarBadge variant="outline" className="text-xs">
              {currentModel}
            </LuminarBadge>
            {documentIds.length > 0 && (
              <LuminarBadge variant="secondary" className="text-xs">
                {documentIds.length} document{documentIds.length > 1 ? 's' : ''} selected
              </LuminarBadge>
            )}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <Sparkles className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Ready to help with your research
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              {documentIds.length > 0
                ? "Ask me anything about your selected documents. I'll provide answers with citations."
                : 'Select some documents from your library to get started with AI-powered research.'}
            </p>
          </div>
        ) : (
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex gap-3 ${
                  message.type === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.type === 'assistant' && (
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                )}

                <div className={`max-w-[70%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                  <div
                    className={`p-4 rounded-2xl ${
                      message.type === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-800'
                    }`}
                  >
                    {message.isLoading ? (
                      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                        >
                          <RefreshCw className="w-4 h-4" />
                        </motion.div>
                        <span className="text-sm">{message.content}</span>
                      </div>
                    ) : (
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      </div>
                    )}

                    {message.error && (
                      <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded flex items-center gap-2">
                        <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                        <span className="text-sm text-red-700 dark:text-red-300">
                          {message.error}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Message metadata and actions */}
                  <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{new Date(message.timestamp).toLocaleTimeString()}</span>

                    {message.type === 'assistant' && !message.isLoading && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(message.content)}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => regenerateResponse(message.id)}
                          className="h-6 w-6 p-0"
                        >
                          <RefreshCw className="w-3 h-3" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFeedback(message.id, 'positive')}
                          className="h-6 w-6 p-0"
                        >
                          <ThumbsUp className="w-3 h-3" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFeedback(message.id, 'negative')}
                          className="h-6 w-6 p-0"
                        >
                          <ThumbsDown className="w-3 h-3" />
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Sources and citations */}
                  {message.response?.sources && message.response.sources.length > 0 && (
                    <Card className="mt-4">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Sources ({message.response.sources.length})
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {message.response.sources.map((source, index) => (
                          <div key={index} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div className="flex items-start justify-between mb-2">
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                Document {source.metadata?.documentId}
                              </span>
                              <LuminarBadge variant="outline" className="text-xs">
                                Chunk {source.metadata?.chunkIndex}
                              </LuminarBadge>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                              {source.content}
                            </p>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  )}

                  {/* Performance metrics */}
                  {message.response && (
                    <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <Zap className="w-3 h-3" />
                        {message.response.processingTime}ms
                      </span>
                      <span>Confidence: {Math.round(message.response.confidence * 100)}%</span>
                    </div>
                  )}
                </div>

                {message.type === 'user' && (
                  <div className="flex-shrink-0 w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <LuminarInput
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={
              documentIds.length > 0
                ? 'Ask about your documents...'
                : 'Select documents to start asking questions...'
            }
            disabled={isProcessing || documentIds.length === 0}
            className="flex-1"
          />

          <Button
            type="submit"
            disabled={!inputValue.trim() || isProcessing || documentIds.length === 0}
            className="flex items-center gap-2"
          >
            {isProcessing ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              >
                <RefreshCw className="w-4 h-4" />
              </motion.div>
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </form>

        {isTyping && (
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-2">
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-gray-400 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
            <span>AI is thinking...</span>
          </div>
        )}
      </div>
    </div>
  )
}
