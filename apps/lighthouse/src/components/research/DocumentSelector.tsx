import { useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { useDocuments } from '~/hooks/useLighthouseQuery'
import { useLighthouseStore } from '~/store/lighthousestore'
import { Button, LuminarBadge } from '@luminar/shared-ui'

export function DocumentSelector() {
  const { data: documents = [], isLoading } = useDocuments()
  const setDocuments = useLighthouseStore((state) => state.setDocuments)

  useEffect(() => {
    if (documents) {
      setDocuments(documents)
    }
  }, [documents, setDocuments])

  const selectedDocumentIds = useLighthouseStore((state) => state.ui.selectedDocumentIds)

  const toggleSelection = useLighthouseStore((state) => state.toggleSelection)
  const selectAll = useLighthouseStore((state) => state.selectAll)
  const deselectAll = useLighthouseStore((state) => state.deselectAll)

  if (isLoading) {
    return <div>Loading documents...</div>
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Documents</CardTitle>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => selectAll(documents.map((d) => d.id))}
            >
              Select All
            </Button>
            <Button size="sm" variant="outline" onClick={deselectAll}>
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {documents.length === 0 ? (
            <p className="text-gray-500">No documents uploaded yet</p>
          ) : (
            documents.map((doc) => (
              <div
                key={doc.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedDocumentIds.has(doc.id)
                    ? 'bg-blue-50 border-blue-300'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => toggleSelection(doc.id)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">{doc.name}</h4>
                    <p className="text-sm text-gray-600">
                      {doc.type.toUpperCase()} • {new Date(doc.createdAt).toLocaleDateString()}
                    </p>
                    <div className="flex gap-1 mt-1">
                      {doc.tags.map((tag) => (
                        <LuminarBadge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </LuminarBadge>
                      ))}
                    </div>
                  </div>
                  <input
                    type="checkbox"
                    checked={selectedDocumentIds.has(doc.id)}
                    onChange={() => {}}
                    className="mt-1"
                  />
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
