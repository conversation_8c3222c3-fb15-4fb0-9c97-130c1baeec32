import { Button } from '@luminar/shared-ui'
'use client'

import { useEffect, useState } from 'react'
import type { Note } from '~/types'
import { NoteEditor } from './NoteEditor'

interface NotesPanelProps {
  documentId: string
}

export function NotesPanel({ documentId }: NotesPanelProps) {
  const [notes, setNotes] = useState<Note[]>([])
  const [editingNote, setEditingNote] = useState<Note | null>(null)

  useEffect(() => {
    fetch(`/api/documents/${documentId}/notes`)
      .then((res) => res.json())
      .then((data) => setNotes(Array.isArray(data) ? data : []))
      .catch((err) => {
        console.error('Failed to fetch notes:', err)
        setNotes([])
      })
  }, [documentId])

  const handleSaveNote = async (note: Partial<Note>) => {
    const url = note.id ? `/api/notes/${note.id}` : `/api/documents/${documentId}/notes`
    const method = note.id ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(note),
    })

    const savedNote = await response.json()

    setNotes((prev) =>
      note.id ? prev.map((n) => (n.id === savedNote.id ? savedNote : n)) : [...prev, savedNote]
    )
    setEditingNote(null)
  }

  const handleDeleteNote = async (id: string) => {
    await fetch(`/api/notes/${id}`, { method: 'DELETE' })
    setNotes((prev) => prev.filter((n) => n.id !== id))
  }

  return (
    <div className="p-4 space-y-4">
      <Button onClick={() => setEditingNote({} as Note)}>Add Note</Button>
      {editingNote && (
        <NoteEditor
          note={editingNote}
          onSave={handleSaveNote}
          onCancel={() => setEditingNote(null)}
        />
      )}
      <div>
        {Array.isArray(notes) &&
          notes.map((note) => (
            <div key={note.id} className="p-2 border-b">
              <p>{note.content}</p>
              <div className="text-xs text-gray-500">
                Category: {note.category}
                <Button size="sm" variant="ghost" onClick={() => setEditingNote(note)}>
                  Edit
                </Button>
                <Button size="sm" variant="ghost" onClick={() => handleDeleteNote(note.id)}>
                  Delete
                </Button>
              </div>
            </div>
          ))}
      </div>
    </div>
  )
}
