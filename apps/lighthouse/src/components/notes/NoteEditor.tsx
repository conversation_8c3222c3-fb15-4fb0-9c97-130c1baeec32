import { Button } from '@luminar/shared-ui'
import { Textarea } from '@luminar/shared-ui'
'use client'

import { useState } from 'react'
import type { Note } from '~/types'

interface NoteEditorProps {
  note?: Note
  onSave: (note: Partial<Note>) => void
  onCancel: () => void
}

export function NoteEditor({ note, onSave, onCancel }: NoteEditorProps) {
  const [content, setContent] = useState(note?.content || '')
  const [category, setCategory] = useState(note?.category || 'insight')

  const handleSave = () => {
    onSave({ id: note?.id, content, category })
  }

  return (
    <div className="space-y-4">
      <Textarea
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="Write your note..."
      />
      <div className="flex justify-between items-center">
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="p-2 border rounded"
        >
          <option value="insight">Insight</option>
          <option value="question">Question</option>
          <option value="todo">To-Do</option>
        </select>
        <div className="flex gap-2">
          <Button onClick={handleSave}>Save</Button>
          <Button variant="ghost" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  )
}
