import { useEffect, useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card'
import { useLighthouseStore } from '~/store/lighthousestore'
import type { DocumentType, FilterState } from '~/types'
import { Button, LuminarInput, LuminarBadge  } from '@luminar/shared-ui'

export function FilterPanel() {
  const globalFilters = useLighthouseStore((state) => state.filters)
  const setGlobalFilters = useLighthouseStore((state) => state.setFilters)
  const clearFilters = useLighthouseStore((state) => state.clearFilters)
  const tagsMap = useLighthouseStore((state) => state.tags)

  const [localFilters, setLocalFilters] = useState<Partial<FilterState>>(globalFilters)

  useEffect(() => {
    setLocalFilters(globalFilters)
  }, [globalFilters])

  const tags = useMemo(() => Array.from(tagsMap.values()), [tagsMap])

  const handleFilterChange = <K extends keyof FilterState>(key: K, value: FilterState[K]) => {
    setLocalFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleApplyFilters = () => {
    setGlobalFilters(localFilters)
  }

  const handleClear = () => {
    clearFilters()
  }

  const handleFileTypeToggle = (type: DocumentType) => {
    const currentTypes = localFilters.documentType || []
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter((t) => t !== type)
      : [...currentTypes, type]
    handleFilterChange('documentType', newTypes.length > 0 ? newTypes : undefined)
  }

  const handleTagToggle = (tagName: string) => {
    const currentTags = localFilters.tags || []
    const newTags = currentTags.includes(tagName)
      ? currentTags.filter((t) => t !== tagName)
      : [...currentTags, tagName]
    handleFilterChange('tags', newTags.length > 0 ? newTags : undefined)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-4">
        <CardTitle>Filters</CardTitle>
        <Button variant="ghost" size="sm" onClick={handleClear}>
          Clear
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium">Date Range</label>
          <div className="flex gap-2 mt-2">
            <LuminarInput
              type="date"
              placeholder="Start"
              value={localFilters.startDate || ''}
              onChange={(e) => handleFilterChange('startDate', e.target.value || undefined)}
            />
            <LuminarInput
              type="date"
              placeholder="End"
              value={localFilters.endDate || ''}
              onChange={(e) => handleFilterChange('endDate', e.target.value || undefined)}
            />
          </div>
        </div>
        <div>
          <label className="text-sm font-medium">File Types</label>
          <div className="flex flex-wrap gap-2 mt-2">
            {['pdf', 'docx', 'txt', 'url'].map((type) => (
              <LuminarBadge
                key={type}
                variant={
                  localFilters.documentType?.includes(type as DocumentType) ? 'default' : 'outline'
                }
                onClick={() => handleFileTypeToggle(type as DocumentType)}
                className="cursor-pointer"
              >
                {type}
              </LuminarBadge>
            ))}
          </div>
        </div>
        <div>
          <label className="text-sm font-medium">Tags</label>
          <div className="flex flex-wrap gap-1 mt-2">
            {tags.map((tag) => (
              <LuminarBadge
                key={tag.id}
                variant={localFilters.tags?.includes(tag.name) ? 'default' : 'outline'}
                onClick={() => handleTagToggle(tag.name)}
                className="cursor-pointer"
              >
                {tag.name}
              </LuminarBadge>
            ))}
          </div>
        </div>
        <Button onClick={handleApplyFilters} className="w-full">
          Apply Filters
        </Button>
      </CardContent>
    </Card>
  )
}
