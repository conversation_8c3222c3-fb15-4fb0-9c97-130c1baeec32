import { Save, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { 
  Button,
  LuminarInput as Input
} from '@luminar/shared-ui'
// TODO: Replace with shared-ui dropdown components when available
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu'
import { useLighthouseStore } from '~/store/lighthousestore'

export function SavedSearches() {
  const [newSearchName, setNewSearchName] = useState('')
  const savedSearches = useLighthouseStore((state) => state.savedSearches)
  const saveSearch = useLighthouseStore((state) => state.saveSearch)
  const deleteSearch = useLighthouseStore((state) => state.deleteSearch)
  const applySearch = useLighthouseStore((state) => state.applySearch)

  const handleSave = () => {
    if (newSearchName) {
      saveSearch(newSearchName)
      setNewSearchName('')
    }
  }

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">Saved Searches</Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {savedSearches.map((search) => (
            <DropdownMenuItem key={search.name} onSelect={() => applySearch(search.name)}>
              {search.name}
              <Trash2
                className="w-4 h-4 ml-auto text-gray-500 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  deleteSearch(search.name)
                }}
              />
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      <LuminarInput
        value={newSearchName}
        onChange={(e) => setNewSearchName(e.target.value)}
        placeholder="Save current search..."
      />
      <Button onClick={handleSave}>
        <Save className="w-4 h-4" />
      </Button>
    </div>
  )
}
