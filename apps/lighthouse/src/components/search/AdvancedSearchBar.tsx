import { AnimatePresence, motion } from 'framer-motion'
import { Clock, Search, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useDebounce } from 'use-debounce'
import { useLighthouseStore } from '~/store/lighthousestore'
import { LuminarInput } from '@luminar/shared-ui'

interface AdvancedSearchBarProps {
  onSearch: (query: string) => void
}

export function AdvancedSearchBar({ onSearch }: AdvancedSearchBarProps) {
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [debouncedQuery] = useDebounce(query, 300)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const searchHistory = useLighthouseStore((state) => state.filters.searchHistory || [])
  const addSearchToHistory = useLighthouseStore((state) => state.addSearchToHistory)

  const handleSearch = (searchQuery: string) => {
    onSearch(searchQuery)
    if (searchQuery) {
      addSearchToHistory(searchQuery)
    }
    setShowSuggestions(false)
  }

  useEffect(() => {
    if (debouncedQuery) {
      fetch(`/api/documents/suggestions?q=${debouncedQuery}`)
        .then((res) => res.json())
        .then(setSuggestions)
    } else {
      setSuggestions([])
    }
  }, [debouncedQuery])

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
        <LuminarInput
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={(e) => e.key === 'Enter' && handleSearch(query)}
          placeholder="Search documents..."
          className="pl-10 pr-10"
        />
        {query && (
          <X
            className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 cursor-pointer"
            onClick={() => setQuery('')}
          />
        )}
      </div>
      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border rounded-lg shadow-lg z-10"
          >
            {suggestions.length > 0 &&
              suggestions.map((s) => (
                <div
                  key={s}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                  onClick={() => {
                    setQuery(s)
                    setShowSuggestions(false)
                    handleSearch(s)
                  }}
                >
                  {s}
                </div>
              ))}
            {searchHistory.length > 0 && (
              <div className="p-2 border-t">
                <h3 className="text-xs font-semibold text-gray-500 flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  Recent
                </h3>
                {searchHistory.map((s) => (
                  <div
                    key={s}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                    onClick={() => {
                      setQuery(s)
                      setShowSuggestions(false)
                      handleSearch(s)
                    }}
                  >
                    {s}
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
