#!/usr/bin/env node

/**
 * Component Usage Fixer
 * 
 * Fixes component usage after import migration
 * Handles Badge → LuminarBadge and other component name changes
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Component usage replacements
const USAGE_REPLACEMENTS = [
  // Badge components
  { pattern: /<Badge\b/g, replacement: '<LuminarBadge' },
  { pattern: /<\/Badge>/g, replacement: '</LuminarBadge>' },
  
  // Input components (for components that use Input from shared-ui)
  { pattern: /<Input\b/g, replacement: '<LuminarInput' },
  { pattern: /<\/Input>/g, replacement: '</LuminarInput>' },
];

const SRC_DIR = path.join(__dirname, 'src');

function getAllFiles(dir, pattern = /\.(tsx?|jsx?)$/) {
  const files = [];
  
  function traverse(currentDir) {
    const entries = fs.readdirSync(currentDir);
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !entry.startsWith('.') && entry !== 'node_modules') {
        traverse(fullPath);
      } else if (pattern.test(entry)) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function fixComponentUsage(filePath) {
  console.log(`🔧 Checking: ${path.relative(process.cwd(), filePath)}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Apply all usage replacements
  for (const { pattern, replacement } of USAGE_REPLACEMENTS) {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      modified = true;
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔧 LIGHTHOUSE COMPONENT USAGE FIXER');
  console.log('📝 Fixing Badge → LuminarBadge usage');
  console.log('=' .repeat(50));
  
  if (!fs.existsSync(SRC_DIR)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }
  
  const files = getAllFiles(SRC_DIR);
  console.log(`\n📁 Found ${files.length} files to check`);
  
  let modifiedCount = 0;
  
  for (const file of files) {
    try {
      if (fixComponentUsage(file)) {
        modifiedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n✨ Component usage fix complete!`);
  console.log(`📊 Modified ${modifiedCount} out of ${files.length} files`);
  
  return { modifiedFiles: modifiedCount, totalFiles: files.length };
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { fixComponentUsage, USAGE_REPLACEMENTS };