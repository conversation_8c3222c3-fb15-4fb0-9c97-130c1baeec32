#!/usr/bin/env node

/**
 * Input Import Fixer
 * 
 * Fixes Input → LuminarInput in import statements
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.join(__dirname, 'src');

function getAllFiles(dir, pattern = /\.(tsx?|jsx?)$/) {
  const files = [];
  
  function traverse(currentDir) {
    const entries = fs.readdirSync(currentDir);
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !entry.startsWith('.') && entry !== 'node_modules') {
        traverse(fullPath);
      } else if (pattern.test(entry)) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function fixInputImports(filePath) {
  console.log(`🔧 Checking: ${path.relative(process.cwd(), filePath)}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Fix Input imports in shared-ui imports
  const fixedContent = content.replace(
    /import\s*{\s*([^}]*,\s*)?Input(\s*,\s*[^}]*)?\s*}\s*from\s*['"]@luminar\/shared-ui['"]/g,
    (match, before, after) => {
      const beforePart = before || '';
      const afterPart = after || '';
      return `import { ${beforePart}LuminarInput${afterPart} } from '@luminar/shared-ui'`;
    }
  );
  
  if (fixedContent !== content) {
    fs.writeFileSync(filePath, fixedContent, 'utf8');
    console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
    modified = true;
  }
  
  return modified;
}

function main() {
  console.log('🔧 LIGHTHOUSE INPUT IMPORT FIXER');
  console.log('📝 Fixing Input → LuminarInput in imports');
  console.log('=' .repeat(50));
  
  if (!fs.existsSync(SRC_DIR)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }
  
  const files = getAllFiles(SRC_DIR);
  console.log(`\n📁 Found ${files.length} files to check`);
  
  let modifiedCount = 0;
  
  for (const file of files) {
    try {
      if (fixInputImports(file)) {
        modifiedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n✨ Input import fix complete!`);
  console.log(`📊 Modified ${modifiedCount} out of ${files.length} files`);
  
  return { modifiedFiles: modifiedCount, totalFiles: files.length };
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { fixInputImports };